import { BaseLinkVue } from '../base/baseLikeVue'
import { Component, Directive, Plugin } from 'vue'

import dialog from './dialog/install'

class UI extends BaseLinkVue {
  use(plugin: Plugin, ...options: any[]) {
    dialog.use(plugin, options)
    return this
  }

  directive(name: string, directive: Directive) {
    dialog.directive(name, directive)
    return this
  }

  component(name: string, component: Component) {
    dialog.component(name, component)
    return this
  }
}

export default new UI()
