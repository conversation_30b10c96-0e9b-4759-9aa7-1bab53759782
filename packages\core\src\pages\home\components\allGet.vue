<template>
    <!-- 全部领取 -->
    <div class="all"
        :class="!(cardTimeOut > 0 && friendStore.isOwn || (nftEquity && useUserStore().isHK == 0)) ? 'all-hk' : ''">
        <div class="all-text">
            <span>{{ state.friend.你有NN個减碳值未领取.replace('NN', energyCount) }}</span>
        </div>
        <div class="all-btn" @click="getAllBall">
            <span>{{ state.friend.全部领取 }}</span>
        </div>
    </div>
</template>

<script setup lang='ts'>
import { ref,onBeforeUnmount } from 'vue'
import { useLang,useEventBus } from 'hook'
import { logEventStatistics } from '@via/mylink-sdk'
import { useTreeStore, useUserStore, useFriendStore } from '@/store'
const friendStore = useFriendStore()//api
const { state } = useLang()
const eventBus = useEventBus()

const props = defineProps({
    cardTimeOut: {//卡的过期倒计时
        type:Number,
        default: 0
    },
    nftEquity: {//nft权益展示
        type:Boolean,
        default: false
    },
    energyCount:{//自己可领取总减碳球
        type:Number,
        default:0
    }
})

const emit = defineEmits(['add','updatePoint'])
let showGetAll = ref<Boolean>(false)//领取全部减碳球气泡

// 点击全部领取
const getAllBall = async () => {
    logEventStatistics('garden_crexp_collect_click')
    showOrHide()
    if (!showGetAll.value) return
    showOrHide_Timer && clearInterval(showOrHide_Timer)
    showGetAll.value = false
    // 重新请求减碳球总数
    emit ('updatePoint')
}
let showOrHide_Timer: NodeJS.Timer | null = null
const showOrHide = () => {
    showGetAll.value = true
    showOrHide_Timer = setInterval(() => {
        showGetAll.value = !showGetAll.value
    }, 5000)
}

onBeforeUnmount(()=>{
    showOrHide_Timer && clearInterval(showOrHide_Timer)
})
</script>

<style lang='less' scoped>
.all-hk {
    top: 380px !important;
}

.all {
    padding: 0 20px;
    height: 52px;
    background: #FFFFFF;
    border-radius: 28px 28px 28px 28px;
    position: absolute;
    top: 432px;
    left: (50 / 750 *100vw);
    z-index: 20;
    display: flex;
    justify-content: center;
    align-items: center;

    .all-text {
        font-size: 20px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: #2A803D;

        span {
            display: flex;
            transform: scale(0.9);
        }
    }

    .all-btn {
        margin-left: -8px;
        height: 36px;
        background: #FDD83B;
        border-radius: 18px 18px 18px 18px;
        display: flex;
        position: relative;
        justify-content: center;

        span {
            transform: scale(0.8);
            font-size: 20px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #95510D;
        }
    }

    .all-btn::after {
        position: absolute;
        content: '';
        clear: both;
        display: block;
        width: 16px;
        height: 16px;
        background: #FD4232;
        right: -2px;
        top: -2px;
        border-radius: 50%;
    }
}

.all::after {
    content: '';
    clear: both;
    display: block;
    width: 30px;
    height: 20px;
    top: 46px;
    left: 56px;
    position: absolute;
    background: url('@assets/imgs/all-jt.png') no-repeat;
    background-size: cover;
}
</style>
