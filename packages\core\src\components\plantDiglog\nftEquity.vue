<template>
    <commonSheet :showSheet="$attrs.showSheet" @close="emit('close')">
        <template #title>
            <h1 class="title">{{ state.nft.showNFTTitle }}</h1>
        </template>
        <template #content>
            <div class="content">
                <div class="side">
                    <div class="side1">
                        <div class="left">
                            <img :src="treeSideImg1" />
                        </div>
                        <div class="right">
                            <p>{{ state.nft.explain1 }}</p>
                        </div>
                    </div>

                    <div class="side1">
                        <div class="left">
                            <img :src="treeSideImg2" />
                        </div>
                        <div class="right">
                            <p>{{ state.nft.explain2 }}</p>
                        </div>
                    </div>
                </div>
                <div class="notice">
                    <p>{{ state.nft.explain3 }}</p>
                    <p>{{ state.nft.explain4 }}</p>
                    </div>
                <div class="clause">
                    <a @click="toClause">{{ state.nft.clause }}</a>
                </div>
                <div class="bottom" @click="emit('close')">
                    <div class="click_btn active">{{ state.nft.gotIt }}</div>
                </div>
            </div>
        </template>
    </commonSheet>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useLang, useRouter } from 'hook'
import commonSheet from '@/components/common/commonSheet.vue'
import { imgs } from '@/assets/imgs'
import { nextTick } from 'process'
const { router } = useRouter()
const emit = defineEmits(['close', 'toclause'])
const treeSideImg1 = ref('')
const treeSideImg2 = ref('')
const { treeCode } = defineProps({
    /** 树的类型 */
    treeCode: {
        type: String,
        require: true,
        default: ''
    }
})
const { state } = useLang()
onMounted(async () => {
    // 根据传入树的信息显示不同的图片
    treeSideImg1.value = imgs[`NTFDetail/explain1/${treeCode}.png`]
    treeSideImg2.value = imgs[`NTFDetail/explain2/${treeCode}.png`]
})
/** 进入条款 */
const toClause = () => {
    emit('toclause')
    router.push({
        path: '/rule',
        query: {
            hideNavigationBar: 'true'
        }
    })
}
</script>

<style lang="less" scoped>
.title {
    font-size: 32px;
    color: #4e5b7e;
    line-height: 40px;
    text-align: center;
}

.content {
    height: 700px;
    padding: 80px 50px 0px;
    .side {
        .side1 {
            display: grid;
            grid-gap: 20px;
            grid-template-columns: 100px 1fr;
            grid-template-rows: repeat(auto-fill, 120px);
            .left {
                img {
                    width: 72px;
                    height: 72px;
                }
            }
            .right {
                p {
                    font-size: 28px;
                    color: #4e5b7e;
                    line-height: 40px;
                    text-align: left;
                }
            }
        }
    }
    .notice {
        font-size: 24px;
        color: #ef8a00;
        text-align: left;
        margin-top: 48px;
    }

    .clause {
        font-size: 24px;
        color: #38ba42;
        text-align: center;
        margin-top: 32px;
    }

    .bottom {
        margin-top: 48px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /** 弹窗按钮 */
    .click_btn {
        border-radius: 48px 12px 48px 12px;
        bottom: 50px;
        width: 570px;
        height: 84px;
        padding: 5px 20px;
        text-align: center;
        box-sizing: content-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #fff;
    }

    /** 未激活的按钮 */
    .active {
        background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
        box-shadow: 0px 2px 0px 2px rgba(252, 175, 40, 1), inset 0px 4px 0px 2px rgba(255, 242, 178, 1);
    }
}
</style>
