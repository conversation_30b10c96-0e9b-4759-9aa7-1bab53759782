<template>
    <!-- Tab标题 -->
    <div class="taskTitle">
        <p>{{ state.home.我的任务 }}</p>
    </div>
    <!-- 顶部切换栏 -->
    <div class="tabs">
        <div class="taskTab">
            <div class="limitTask" v-if="limitTask">
                <div class="lightHight">{{ state.task.限定任務 }}</div>
                <div class="otherTask" @click="clickLimit">{{ state.task.常規任務 }}</div>
            </div>
            <div class="routineTask" v-else>
                <div class="lightHight" @click="clickNoLimit" :class="{ lightHightEn: lang == 'en' }">
                    {{ state.task.限定任務 }}
                </div>
                <div class="otherTask">{{ state.task.常規任務 }}</div>
            </div>
        </div>
        <div class="taskTNC"
            @click="router.push({ path: '/rule', query: { hideNavigationBar: 'true', type: 'task' } })"></div>
    </div>
    <!-- 额外奖励卡片 -->
    <div class="extraCard" v-if="limitTask && props.taskList.length">
        <div class="title">
            <div class="subTitle">{{ state.task.额外奖赏进度 }}</div>
            <div class="time" v-if="taskTitleTime != '0'">
                <img :src="$imgs['task/time.png']" alt="" />
                <span style="color: #f06600">{{ taskTitleTime }}{{ state.task.進行更新 }}</span>
            </div>
        </div>
        <div class="taskProgress">
            <div class="proportion">
                <p>{{ successNum }}</p>
                /{{ taskList?.length }}
            </div>
            <p>{{ state.task.本期已完成 }}</p>
        </div>
    </div>
    <!-- 常规任务图片 -->
    <div class="routineImg" v-else-if="!limitTask">{{ state.task.常规任务标题 }}</div>
    <!-- 限定任务空状态 -->
    <div class="taskNone" v-else-if="!props.taskList.length">
        <img :src="$imgs['task/noTask.png']" alt="" srcset="" />
        <p>{{ state.task.任务结束 }}</p>
    </div>
    <!-- 限定任务 -->
    <div class="taskMain" v-if="limitTask">
        <!-- 额外任务 -->
        <div v-for="(item, index) in extraList">
            <div class="extraTask" v-if="(!item.expired_at && item.status == 1) || item.expired_at">
                <p>{{ state.task.触发额外任务[index] }}</p>
                <div class="extraList">
                    <img :src="$imgs[`task/${item.code}.png`]" alt="" />
                    <div class="extraTitle">
                        <div class="title">{{ item.metadata[langValue['title' + lang]] }}</div>
                        <div class="subtitle" v-html="item.metadata[langValue['content' + lang]].replace(
                            `${item.energy_value}g`,
                            `<span style='color:#22992C'>${item.energy_value}g</span>`
                        )
                            "></div>
                    </div>
                    <div class="extraRight">
                        <div class="listBtn"
                            :class="item.status == 0 ? 'tofiniosh' : item.status == 1 ? 'claim' : 'success'"
                            @click="getClaim(item)">
                            {{
                                item.status == 0
                                    ? state.task.去完成
                                    : item.status == 1
                                        ? state.task.待領取
                                        : state.task.已完成
                            }}
                        </div>
                        <div class="listPace" v-if="
                            countdownString &&
                            countdownString != '0' &&
                            item.code == 'use-ticket-equity' &&
                            item.status == 0
                        ">
                            {{ state.task.到期 }}{{ countdownString }}{{ state.task.後到期 }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 任务 -->
        <div class="taskList" v-for="(item, index) in taskList">
            <div class="taskTop">
                <img :src="$imgs[`task/${item.code}.png`]" alt="" />
                <div class="listTitle">
                    <div class="headline">{{ item.metadata[langValue['title' + lang]] }}</div>
                    <div class="subtitle" v-html="item.metadata[langValue['content' + lang]].replace(
                        `${item.energy_value}g`,
                        `<span style='color:#22992C'>${item.energy_value}g</span>`
                    )
                        "></div>
                </div>
                <div class="listRight">
                    <div :class="item.status == 0 ? 'tofiniosh' : item.status == 1 ? 'claim' : 'success'"
                        class="listBtn" @click="getClaim(item)">
                        {{
                            item.status == 0
                                ? state.task.去完成
                                : item.status == 1
                                    ? state.task.待領取
                                    : state.task.已完成
                        }}
                    </div>
                    <div class="listPace">{{ state.task.進度 }}:{{ item.recv_count }}/{{ item.req_count }}</div>
                </div>
            </div>
            <div class="loginBar" v-if="bars[item.code] !== undefined">
                <div class="taskTick" v-if="bars[item.code] !== undefined">
                    <div class="tick" :style="`width: ${bars[item.code]}`"></div>
                </div>
                <img :src="$imgs['tick.png']" alt="" v-for="day in item.recv_count == 7 ? 6 : item.recv_count"
                    v-if="item.code == 'blindbox-energy'" />
                <img :src="$imgs['tick.png']" alt="" v-for="day in item.recv_count == 5 ? 4 : item.recv_count" v-else />
                <span class="noLogin" v-for="day in 6 - (item.recv_count == 7 ? 6 : item.recv_count)"
                    v-if="item.code == 'blindbox-energy'"></span>
                <span class="noLogin" v-for="day in 4 - (item.recv_count == 5 ? 4 : item.recv_count)" v-else></span>
                <img class="giftImg" :src="$imgs['loginGift.png']" alt="" />
            </div>
        </div>
        <!-- 额外奖赏 -->
        <div class="additionalCard" v-for="(item, index) in extraList">
            <div v-if="!item.expired_at && item.status != 1">
                <div class="additionTitle">
                    <div class="left">
                        {{ state.task.额外任务[index] }}{{ item.metadata[langValue['title' + lang]] }}
                    </div>
                    <div v-if="!item.expired_at && item.status == 2">
                        <img :src="$imgs['task/completeEn.png']" alt="" v-if="lang == 'en'" class="right" />
                        <img :src="$imgs['task/complete.png']" alt="" v-else class="right" />
                    </div>
                </div>
                <div class="additionRight">
                    <img :src="$imgs[`task/task${index + 1}.png`]" alt="" />
                    <div class="additionDes" v-html="item.metadata[langValue['content' + lang]].replace(
                        `${item.energy_value}g`,
                        `<span style='color:#22992C'>${item.energy_value}g</span>`
                    )
                        "></div>
                </div>
            </div>
        </div>
    </div>
    <!-- 常规任务 -->
    <div class="routineMain" v-else>
        <div class="box" v-for="(item, index) in state.task.routineTask" :key="item">
            <template v-if="index != 0 || actived_aigc_award_activity">
                <div class="routineList">
                    <div class="right">
                        <div class="xianshi" v-if="index === 0">{{ state.ai.限時 }}</div>
                        <img :src="$imgs[`task/r${index}.png`]" alt="" />
                        <p>{{ item }}</p>
                    </div>
                    <div class="routineBtn" @click="toRoutine(index)">{{ state.task.去完成 }}</div>
                </div>
            </template>
        </div>

    </div>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, watch, onMounted } from 'vue'
import { useTaskStore, useUserStore, useTreeStore } from '@/store'
import { TaskListType, TaskTimeType } from '@/store/modules/task/type'
import { useLang, useRouter, useToast, useEventBus, useEnvConfig, useLoading, useDialog } from 'hook'
import { getNowTime } from '@/util/unity'
import { logEventStatistics } from '@via/mylink-sdk'
import refresh from '@/components/refresh.vue'
import { jumpMyGame } from '@/util/mygame-sdk'
const { state, lang } = useLang()
const limitTask = ref(true)
const { router } = useRouter()
const loading = useLoading()
const props = defineProps({
    taskList: {
        type: Array<TaskListType>,
        required: true,
        default: []
    },
    batch: {
        type: Object,
        require: true
    }
})
let eventBus = useEventBus()
const dialog = useDialog({
    refresh
})

const langValue = {
    titletc: 'title',
    titlesc: 'titleSc',
    titleen: 'titleEn',
    contenttc: 'content',
    contentsc: 'contentSc',
    contenten: 'contentEn'
}

const { getTaskList, getAwards } = useTaskStore()

const taskList = ref<any[]>()
const extraList = ref()
// 进度条长度
const bars = ref({
    'blindbox-energy': '0',
    'game-playing-mdxy': '0',
    'game-playing-fkzc': '0'
})
// 完成任务个数
const successNum = ref<number>(0)
let countdownString = ref('')
const timer = ref()
const taskTimer = ref()
let taskTitleTime = ref('0')
let countdown = ref()
const actived_aigc_award_activity = ref(false)
onMounted(async () => {
    let resInfo = await useTaskStore().getInfo()
    actived_aigc_award_activity.value = resInfo.actived_aigc_award_activity
})

const init = async () => {
    // 没有限定任务
    if (!props.taskList.length) {
        taskList.value = []
        // 是否跳转到常规任务 false跳
        if (!useTaskStore().limitTask) {
            limitTask.value = false
        }
    }
    if (props.batch) {
        let dateNow
        // 获取网络时间
        await getNowTime()
            .then((now) => (now ? (dateNow = now) : ''))
            .catch((now) => (now ? (dateNow = now) : ''))
        let webTime = Math.floor(new Date(dateNow).getTime() / 1000)
        let time = Math.floor(new Date(props.batch.stop_at.replace(/-/g, '/')).getTime() / 1000)
        let countdown = +time - +webTime
        if (!taskTimer.value && props.batch.stop_at) {
            taskTimer.value = setInterval(() => {
                if (countdown) {
                    countdown--
                    taskTitleTime.value = formatCountdown(countdown, true)
                    if (countdown <= 0) {
                        if (countdown == 0) {
                            // 倒计时刚好结束
                            if (props.taskList) {
                                //有更新内容
                                let firstLoginDia = dialog.get('refresh')
                                firstLoginDia.show(
                                    { refresh: true, btn: state.task.刷新任務, tips: state.task.刷新任务文本 },
                                    { maskClose: false }
                                )
                            }
                        }
                        taskTitleTime.value = '0'
                        clearInterval(taskTimer.value)
                        taskTimer.value = null
                        return
                    }
                }
            }, 1000)
        }
    }
    successNum.value = 0
    // 切分任务
    taskList.value =
        props.taskList &&
        props.taskList.filter((item) => {
            if (bars.value[item.code] !== undefined) {
                if (item.code == 'blindbox-energy') {
                    bars.value[item.code] = ((item.recv_count - 1) / 6) * 100 + '%'
                } else {
                    bars.value[item.code] = ((item.recv_count - 1) / 4) * 100 + '%'
                }
            }
            // 完成任务数
            if (item.status != 0 && item.type == 0) {
                item.recv_count == item.req_count ? successNum.value++ : (successNum.value += 0)
            }
            return item.type == 0
        })
    console.log(props.taskList, '============extraListextraListextraListextraListextraList');

    extraList.value =
        props.taskList &&
        props.taskList.filter((item) => {
            let dateNow = Math.floor(new Date().getTime() / 1000)
            if (item.type == 1 && item.code == 'use-ticket-equity' && item.expired_at) {
                // 倒计时
                let date = Math.floor(new Date(item.expired_at.replace(/-/g, '/')).getTime() / 1000)
                countdown.value = +date - +dateNow
                if (countdown.value <= 0) {
                    countdown.value = 0
                    return
                }
                countdownString.value = formatCountdown(countdown.value, false)
            }
            if (!timer.value && item.expired_at) {
                timer.value = setInterval(() => {
                    if (countdown.value) {
                        countdown.value--
                        countdownString.value = formatCountdown(countdown.value, false)
                        if (countdown.value <= 0) {
                            countdownString.value = '0'
                            clearInterval(timer.value)
                            timer.value = null
                            return
                        }
                    }
                }, 1000)
            }

            return item.type == 1
        })

}
// 倒计时,all:是否是任务总计时
function formatCountdown(seconds, all) {
    if (isNaN(seconds) || seconds < 0) {
        return 'Invalid input'
    }
    let hours
    let formattedHours

    hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60

    formattedHours = hours.toString().padStart(2, '0')
    const formattedMinutes = minutes.toString().padStart(2, '0')
    const formattedSeconds = remainingSeconds.toString().padStart(2, '0')

    if (all) {
        const day = Math.floor(seconds / 3600 / 24)
        hours = Math.floor(seconds / 3600) % 24
        const formattedDay = day.toString()
        formattedHours = hours.toString().padStart(2, '0')
        if (Number(formattedDay) >= 7) {
            return `${formattedDay}${state.task.天}`
        } else {
            return `${formattedDay}${state.task.天}${formattedHours}:${formattedMinutes}:${formattedSeconds}`
        }
    } else {
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
    }
}

watch(
    props,
    () => {
        init()
    },
    {
        immediate: true
    }
)

// 领取奖励
const getClaim = async (item) => {
    // if (!useTreeStore().nowTree.tree) {
    //     useToast().toast(state.task.需開始種植才可進行任務)
    //     return
    // }
    if (item.status == 1) {
        logEventStatistics('garden_task01_collect_click')
        loading.loading('open')
        const result = await getAwards(item.task_id)
        loading.loading('close')
        console.log(result, '----------------------------------------------------')
        eventBus.emit('upLevelFun', result)
        if (result.user_actions) {
            if (item.code == 'login_days' || item.code == 'buy-commodity') {
                useToast().toast(state.task.領取雙倍卡)
            } else {
                item.energy_type == 0
                    ? useToast().toast(state.task.已成功領取 + item.energy_value + state.task.g減碳值)
                    : useToast().toast(state.task.已成功領取 + item.save_energy_value + state.task.g減碳值)
            }
            await useTaskStore()
                .getInfo()
                .then((res) => {
                    console.log(res.user.energy_total, '总数')
                    useTreeStore().changeEnergyTotal(res.user.energy_total)
                })
        } else {
            useToast().toast(state.task.發生異常)
        }
        eventBus.emit('rolandTaskState')
    }

    if (item.status == 0) {
        logEventStatistics('garden_task01_go_click')

        loading.loading('open')
        try {
            let linkUrl = item.metadata.linkUrl
            if (linkUrl.indexOf('internal-jump://') == -1) {
                if (item.metadata.linkType && item.metadata.linkType === 'mygame') {
                    jumpMyGame(useEnvConfig().RUN_ENV == 'uat' || useEnvConfig().RUN_ENV == 'develop', item.metadata.linkValue)
                } else {
                    location.href = linkUrl
                }
            } else {
                let jumpUrl = linkUrl.replace('internal-jump://', '')
                if (jumpUrl.indexOf('home') !== -1) {
                    eventBus.emit('closeTask')
                } else {
                    router.push(jumpUrl)
                }
            }
        } catch (e) {
            console.log(e)
        } finally {
            loading.loading('close')
        }
    }
}

// 点击限时任务
const clickLimit = () => {
    limitTask.value = false
    logEventStatistics('garden_task01_click')
}
// 常规任务点击
const toRoutine = (index) => {
    logEventStatistics('garden_task02_go_click')
    switch (index) {
        case 0:
            if (useEnvConfig().RUN_ENV == 'develop' || useEnvConfig().RUN_ENV == 'uat') {
                location.href = 'http://47.57.156.216/activity/2daigc/#/photo/profile?forceHideNavigationBar=true&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&isRecharge=1'
            } else {
                location.href = 'https://mylink.komect.com/activity/2daigc/#/photo/profile?forceHideNavigationBar=true&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&isRecharge=1'
            }
            break
        case 1:
            window.location.href = useEnvConfig().RUN_ENV == 'uat' ? 'cmcchkhsh://payBill' : 'cmcchkhsh://payBill'
            break
        case 2:
            window.location.href =
                useEnvConfig().RUN_ENV == 'uat'
                    ? 'openurl-modal://https://mymall.malluat.hk.chinamobile.com/?channel=1007&forceHideNavigationBar=true&token=<<cmcchkhsh_ssoToken>>&lang=<<cmcchkhsh_cmplang>>&fromPage=outside'
                    : 'openurl-modal://https://mymall.hk.chinamobile.com/?channel=1007&forceHideNavigationBar=true&token=<<cmcchkhsh_ssoToken>>&lang=<<cmcchkhsh_cmplang>>&fromPage=outside'
            break

        case 3:
            window.location.href = useEnvConfig().RUN_ENV == 'uat' ? 'cmcchkhsh://sgs_run' : 'cmcchkhsh://sgs_run'
            break
        case 4:
            eventBus.emit('closeTask')
            break
        case 5:
            window.location.href =
                useEnvConfig().RUN_ENV == 'uat'
                    ? `openurl-modal://http://47.57.156.216/activity/myCity/#/blindBox?lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true`
                    : 'openurl-modal://https://mylink.komect.com/activity/myCity/#/blindBox?lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true'
            break
        case 6:
            eventBus.emit('taskToNFT')
            break
        case 7:
            window.location.href = useEnvConfig().RUN_ENV == 'uat' ? 'openhkhshlogin://https://uatoss.mylinkapp.hk/mygame/center/web-uat/index.html?thirdPartyToken=<<cmcchkhsh_thirdparty_token_channel=mUBJ5xHj7Y>>&lang=<<cmcchkhsh_cmplang>>' : 'openhkhshlogin://https://cdn.mylinkapp.hk/mygame/center/index.html?thirdPartyToken=<<cmcchkhsh_thirdparty_token_channel=bU9JU0HAZG>>&lang=<<cmcchkhsh_cmplang>>';
            break;
    }
}
const clickNoLimit = () => {
    limitTask.value = true
    logEventStatistics('garden_task02_click')
}

onBeforeUnmount(() => {
    clearInterval(timer.value)
    timer.value = null
    useTaskStore().openTask = false
})
</script>

<style lang="less" scoped>
.taskTitle {
    width: 528px;
    height: 88px;
    background: url('@assets/imgs/friendTitle.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: -82px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    line-height: 28px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    z-index: 2;

    p {
        font-size: 36px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        color: #ffffff;
        line-height: 88px;
    }
}

.tabs {
    position: relative;

    .taskTab {
        width: 484px;
        height: 72px;
        background: #ffffff;
        box-shadow: inset 0px 0 12px 2px rgba(18, 116, 62, 0.29);
        border-radius: 36px 36px 36px 36px;
        opacity: 1;
        margin: 0 auto;
        font-size: 28px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        color: #22992c;

        .limitTask {
            display: flex;
            align-items: center;
            width: 484px;

            .lightHight {
                text-align: center;
                width: 256px;
                height: 72px;
                line-height: 72px;
                background: linear-gradient(123deg, #65e278 0%, #44c650 100%);
                border-radius: 32px 32px 32px 32px;
                opacity: 1;
                color: #ffffff;
            }

            .otherTask {
                padding-left: 36px;
                height: 72px;
                line-height: 72px;
                width: calc(100% - 256px);
            }
        }

        .routineTask {
            display: flex;
            align-items: center;

            .lightHight {
                opacity: 1;
                width: 256px;
                color: #22992c;
                padding-left: 72px;
                height: 72px;
                line-height: 72px;
            }

            .lightHightEn {
                padding-left: 42px;
            }

            .otherTask {
                width: 256px;
                height: 72px;
                line-height: 72px;
                background: linear-gradient(123deg, #65e278 0%, #44c650 100%);
                border-radius: 32px 32px 32px 32px;
                opacity: 1;
                text-align: center;
                color: #ffffff;
            }
        }
    }

    .taskTNC {
        width: 48px;
        height: 48px;
        background: url('@assets/imgs/taskTnc.png') no-repeat;
        background-size: 100% 100%;
        position: absolute;
        top: 16px;
        right: 18px;
    }
}

.extraCard {
    background: url('@assets/imgs/extraBg.png') no-repeat;
    background-size: 100% 100%;
    width: 702px;
    margin: 36px auto 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 32px;

    .title {
        display: flex;
        flex-direction: column;

        .subTitle {
            font-size: 32px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            color: #f06600;
        }

        .time {
            font-family: PingFang TC, PingFang TC;
            font-weight: 400;
            font-size: 20px;
            color: #4e5b7e;
            display: flex;
            align-items: center;
            margin-top: 5px;

            img {
                width: 24px;
                height: 24px;
                margin-right: 4px;
            }
        }
    }

    .taskProgress {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .proportion {
            font-size: 32px;
            font-family: PingFang TC, PingFang TC;
            font-weight: 500;
            color: #4e5b7e;
            display: flex;
            justify-content: center;
            align-items: center;

            p {
                font-size: 40px;
                font-family: PingFang TC, PingFang TC;
                font-weight: 500;
                color: #f06623;
            }
        }

        p {
            font-size: 20px;
            font-family: PingFang TC, PingFang TC;
            font-weight: 400;
            color: #4e5b7e;
        }
    }
}

.routineImg {
    background: url('@assets/imgs/task/routineBg.png') no-repeat;
    background-size: 100% 100%;
    height: 104px;
    line-height: 104px;
    text-align: center;
    margin: 32px 22px 24px;
    font-size: 28px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    color: #196f20;
}

.taskNone {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 156px 178px 0 180px;

    img {
        width: 178px;
        height: 144px;
    }

    p {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #4e5b7e;
        line-height: 40px;
        margin-top: 58px;
    }
}

.taskMain {
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: center;

    .taskList {
        width: 702px;
        // height: 232px;
        background: #ffffff;
        border-radius: 16px 16px 16px 16px;
        opacity: 1;
        padding: 24px;
        margin-bottom: 24px;

        .taskTop {
            display: flex;

            img {
                width: 100px;
                height: 100px;
                margin-right: 16px;
            }

            .listTitle {
                display: flex;
                flex-direction: column;
                width: 350px;
                margin-right: 44px;

                .headline {
                    font-size: 32px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 500;
                    color: #4e5b7e;
                    display: -webkit-box; //对象作为弹性伸缩盒子模型显示
                    overflow: hidden; //溢出隐藏
                    -webkit-box-orient: vertical; //设置伸缩盒子对象的子元素的排列方式
                    -webkit-line-clamp: 2; //设置 块元素包含的文本行数
                }

                .subtitle {
                    font-size: 24px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    color: rgba(78, 91, 126, 0.7);
                }
            }

            .listRight {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                .listBtn {
                    width: 144px;
                    height: 58px;
                    line-height: 58px;
                    text-align: center;
                    // background: linear-gradient(128deg, #50DD4C 0%, #3CBF48 100%);
                    border-radius: 30px 30px 30px 30px;
                    opacity: 1;
                    font-size: 24px;
                    font-family: PingFang TC, PingFang TC;
                    font-weight: 500;
                    color: #ffffff;
                }

                .listPace {
                    font-size: 18px;
                    font-family: PingFang TC, PingFang TC;
                    font-weight: 400;
                    color: #4e5b7e;
                }
            }

            .tofiniosh {
                color: #ffffff;
                background: linear-gradient(128deg, #50dd4c 0%, #3cbf48 100%);
            }

            .success {
                background: #acdeb0;
                color: #3c8e43;
            }

            .claim {
                color: #ffffff;
                background: linear-gradient(128deg, #ffdc7e 0%, #fbb629 100%);
            }
        }

        .loginBar {
            // width: 680px;
            height: 90px;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;

            img {
                width: 40px;
                height: 40px;
                z-index: 2;
                position: relative;

                &:last-child {
                    margin-right: 0;
                    width: 50px;
                    height: 50px;
                }
            }

            .noLogin {
                display: inline-block;
                width: 40px;
                height: 40px;
                background: #eaeaea;
                border-radius: 50%;
                z-index: 2;
                position: relative;
            }

            .taskTick {
                width: 654px;
                height: 12px;
                background: #eaeaea;
                border-radius: 12px 12px 12px 12px;
                position: absolute;
                top: calc(50% - 6px);
            }

            .tick {
                height: 12px;
                border-radius: 12px 12px 12px 12px;
                position: absolute;
                top: 0;
                left: 0;
                background: #ffd169;
            }

            .giftImg {
                left: 0;
            }
        }
    }

    .extraTask {
        padding: 0 1px 2px 1px;
        background: linear-gradient(270deg, #ffc279 0%, #f7a931 100%);
        border-radius: 16px 16px 16px 16px;
        opacity: 1;
        margin-bottom: 24px;

        p {
            font-size: 28px;
            font-family: PingFang TC, PingFang TC;
            font-weight: 600;
            color: #ffffff;
            height: 40px;
            line-height: 40px;
            margin: 8px 0 8px 24px;
        }

        .extraList {
            width: 700px;
            background: #ffffff;
            border-radius: 16px 16px 16px 16px;
            opacity: 1;
            display: flex;
            padding: 24px 22px 24px 24px;

            img {
                width: 100px;
                height: 100px;
            }

            .extraTitle {
                margin: 0 44px 0 16px;

                .title {
                    font-size: 32px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 500;
                    color: #4e5b7e;
                }

                .subtitle {
                    font-size: 24px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    color: rgba(78, 91, 126, 0.7);
                }
            }

            .extraRight {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-top: 24px;

                .listBtn {
                    width: 144px;
                    height: 58px;
                    line-height: 58px;
                    text-align: center;
                    // background: linear-gradient(128deg, #50DD4C 0%, #3CBF48 100%);
                    border-radius: 30px 30px 30px 30px;
                    opacity: 1;
                    font-size: 24px;
                    font-family: PingFang TC, PingFang TC;
                    font-weight: 500;
                    color: #ffffff;
                }

                .tofiniosh {
                    color: #ffffff;
                    background: linear-gradient(128deg, #50dd4c 0%, #3cbf48 100%);
                }

                .success {
                    background: #acdeb0;
                    color: #3c8e43;
                }

                .claim {
                    color: #ffffff;
                    background: linear-gradient(128deg, #ffdc7e 0%, #fbb629 100%);
                }

                .listPace {
                    font-size: 18px;
                    font-family: PingFang TC, PingFang TC;
                    font-weight: 400;
                    color: #4e5b7e;
                    text-align: center;
                }
            }
        }
    }

    .additionalCard {
        width: 702px;
        height: 188px;
        background: #fff1de;
        border-radius: 16px 16px 16px 16px;
        opacity: 1;
        margin-bottom: 30px;
        font-size: 0;

        .additionTitle {
            // width: 328px;
            display: flex;
            background: linear-gradient(133deg, #fcb562 0%, #f7a82c 100%);
            border-radius: 8px 8px 0px 0px;
            justify-content: space-between;
            padding-right: 20px;
            align-items: flex-end;

            .left {
                display: inline-block;
                line-height: 48px;
                height: 48px;
                opacity: 1;
                font-size: 28px;
                font-family: PingFang TC, PingFang TC;
                font-weight: 500;
                color: #ffffff;
                padding: 0 24px;
            }

            .right {
                height: 40px;
            }
        }

        .additionRight {
            display: flex;
            padding: 16px 22px 24px 26px;

            img {
                width: 100px;
                height: 100px;
            }

            .additionDes {
                font-size: 24px;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                color: #4e5b7e;
                margin-left: 24px;
            }
        }
    }
}

.routineMain {
    padding: 0 22px;
    overflow-y: auto;

    .routineList {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #ffffff;
        border-radius: 16px 16px 16px 16px;
        opacity: 1;
        margin-bottom: 24px;
        padding: 14px 36px 14px 24px;

        .right {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;

            .xianshi {
                position: absolute;
                font-weight: 400;
                font-size: 20px;
                color: #FFFFFF;
                height: 28px;
                padding: 0 5px;
                background: #E03131;
                border-radius: 24px 24px 24px 0px;
                display: flex;
                justify-content: center;
                align-items: center;
                top: -14px;
                left: -24px;
            }
        }

        img {
            width: 100px;
            height: 100px;
        }

        p {
            font-size: 32px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            color: #4e5b7e;
            margin-left: 24px;
        }

        .routineBtn {
            width: 144px;
            height: 58px;
            line-height: 58px;
            text-align: center;
            background: linear-gradient(128deg, #50dd4c 0%, #3cbf48 100%);
            border-radius: 30px 30px 30px 30px;
            opacity: 1;
            font-size: 24px;
            font-family: PingFang TC, PingFang TC;
            font-weight: 500;
            color: #ffffff;
        }
    }
}
</style>
