import { useTreeStore, useUserStore } from '@/store'
import { useDialog } from 'hook'
import upgradeNFTDialog from '@/components/upgradeNFTDialog.vue'
import reCasteDialog from '@/components/reCasteDialog.vue'
const dialog = useDialog({ upgradeNFTDialog, reCasteDialog })
export default function () {
    /** 本地nft提示锁，防止多次出现 */
    let nftUpgradeLock = false
    /** 是否首次nft升级弹窗 */
    let isFirstNFTUpgradeTips = 0
    /** 是否升级nft的提醒 */
    let nftUpgradeFlag = false
    /** 是否重铸的提醒 */
    let nftRecastFlag = false
    /** nft升级弹窗空白关闭回调 */
    let nftUpgradeMaskCloseCallback
    /** nft重铸弹窗空白关闭回调 */
    let nftRecasteMaskCloseCallback
    /** 打开nft相关提示提醒流程 */
    const toOpenNFTDialog = (
        value: {
            nftUpgradeMaskCloseCallback?: () => void
            nftRecasteMaskCloseCallback?: () => void
        } = {}
    ) => {
        value.nftRecasteMaskCloseCallback && (nftUpgradeMaskCloseCallback = value.nftRecasteMaskCloseCallback)
        value.nftRecasteMaskCloseCallback && (nftRecasteMaskCloseCallback = value.nftRecasteMaskCloseCallback)

        // 只弹一次
        const info = useUserStore().userInfo
        isFirstNFTUpgradeTips = info.webui_manage_values.first_nft_upgrade || 0

        // 是否已经出现过nft升级提示的弹窗
        if (isFirstNFTUpgradeTips === 1 || nftUpgradeLock) {
            return
        }

        nftUpgradeLock = true
        // TODO 更新后台接口，该弹窗已经出现过
        // useUserStore().setUpData('first_nft_upgrade', '1')

        // 判断是否有植物完成但是未铸造的状态
        const treeList = useTreeStore().treeList.trees
        for (const tree of treeList) {
            // 如果存在种成但是未铸造的状态则显示重铸弹窗
            if (tree.completed && !tree.nft_exists) {
                nftRecastFlag = true
            }
            // 如果存在种成但是未铸造的状态则显示升级弹窗
            if (tree.nft_upgrade_notice) {
                nftUpgradeFlag = true
            }
        }

        // 打开nft升级窗口的提醒
        openNFTUpgradeDialog()
    }

    /** 打开nft升级提示弹窗 */
    const openNFTUpgradeDialog = () => {
        if (!nftUpgradeFlag) {
            // 在不需要显示nft更新弹窗的情况下显示重新铸造的弹窗
            openReCasteDialog()
            return
        }
        const nftDialog = dialog.get('upgradeNFTDialog')
        nftDialog.on('mask-close', () => {
            nftRecasteMaskCloseCallback()
        })
        nftDialog.show({}, {})
    }

    /** 打开重新铸造弹窗 */
    const openReCasteDialog = () => {
        if (!nftRecastFlag) {
            return
        }
        const redialog = dialog.get('reCasteDialog')
        redialog.on('mask-close', () => {
            nftUpgradeMaskCloseCallback()
        })
        redialog.show({}, {})
    }

    return { toOpenNFTDialog }
}
