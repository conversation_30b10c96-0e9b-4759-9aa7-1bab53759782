import { BaseLinkVue } from '../../base/baseLikeVue';
import { useLoading } from '.';
const { app } = useLoading();
class Loading extends BaseLinkVue {
    use(plugin, ...options) {
        app.use(plugin, options);
        return this;
    }
    directive(name, directive) {
        app.directive(name, directive);
        return this;
    }
    component(name, component) {
        app.component(name, component);
        return this;
    }
}
export default new Loading();
