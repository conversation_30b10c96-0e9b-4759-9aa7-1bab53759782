<template>
    <div class="friend">
        <navComponent :returnType="2" :showTransmit="true"/>
        <div class="friendList">
            <friendDialog :friendSheetTab="1"/>
            <div class="go-top" @click="goTop">
                <img :src="$imgs['goTop.png']" alt="">
                <div class="go-top-text">
                    <span>回到頂部</span>
                    <img :src="$imgs['arrow.png']" alt="">
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import navComponent from '@/components/navComponent.vue';
import friendDialog from '@/components/friendDialog.vue';
//回到顶部
const goTop = ()=>{
    window.scrollTo(0,0)
}
</script>

<style scoped lang="less">
.friend{
    background: #F7F7F9;
    min-height:100vh
}
:deep(.nav){
    height:192px;
    background: linear-gradient(180deg, #D9F4E7 0%, #F7F7F9 100%);
    .transmit{
        display:none !important;
    }
}
:deep(.mgTitle),
:deep(.addfriend){
    display:none !important;
}
.friendList{
    margin:0 auto;
    width: 724px;
    background: #FFFFFF;
    border-radius: 48px 48px 48px 48px;
    padding:72px 24px 20px;
}
.bottom-tips{
    font-size: 24px;
    font-family: PingFang TC-Regular, PingFang TC;
    font-weight: 400;
    color: #A7AEC3;
    text-align:center;
    padding-bottom:72px;
}
.friend-title{
    display:flex;
    justify-content:center;
    align-items:center;
    flex-direction:column;
    .title-top{
        width:212px;
        height:14px;
        display:flex;
        margin-top:56px;
        img{
            width:100%;
            height:100%;
        }
    }
    .title-btm{
        display:flex;
        justify-content:center;
        span{
            font-size: 36px;
            font-family: PingFang HK-Semibold, PingFang HK;
            font-weight: 600;
            color: rgba(0,0,0,0.8);
            margin:0 16px;
        }
        img{
            width:40px;
            height:40px;
        }
    }
}

:deep(.provide){
    width: 260px;
    height: 58px;
    background: linear-gradient(152deg, #FFEBC4 0%, #FFE2A2 100%,0.15);
    box-shadow: 0px 2px 0px 2px rgba(242,192,99,0.15);
    border-radius: 12px 12px 12px 12px;
    text-align:center;
    color: rgba(239,138,0,0.15);
    img{
        display:none;
    }
}
:deep(.container .eneryitem .provide_active){
    width: 260px;
    color: rgba(239,138,0);
    background: linear-gradient(152deg, #FFEBC4 0%, #FFE2A2 100%);
    box-shadow: 0px 2px 0px 2px rgba(242,192,99);
}
:deep(.container .eneryitem .item){
    width: 260px;
    padding-left:0;
    justify-content:center;
    img{
        display:none;
    }
}
:deep(.get){
    width: 260px;
    height: 58px;
    background: rgba(78,202,84,0.15);
    border-radius: 12px 12px 12px 12px;
}
:deep(.container .eneryitem .get_active){
    width: 260px;
    height: 58px;
    background: linear-gradient(153deg, #BEF8C0 0%, #94EB98 100%);
    box-shadow: 0px 2px 0px 2px rgba(68,201,80,1);
    border-radius: 12px 12px 12px 12px;
    opacity: 1;
}
:deep(.container .eneryitem::before){
    clear: both;
    display: block;
    content:'';
    background: url('@assets/imgs/trends_blue.png') no-repeat;
    background-size: cover;
    margin-right: 20px;
    width: 60px;
    height: 60px;
}
.go-top{
    position: fixed;
    height: 84px;
    bottom: 60px;
    right: 32px;
    display: flex;
    justify-content: center;
    img{
        width: 80px;
        height: 80px;
    }
    .go-top-text{
        position: absolute;
        bottom: -4px;
        width: 96px;
        height: 24px;
        background: linear-gradient(360deg, #3CBF48 0%, #3DD13F 100%);
        box-shadow: 0px 2px 0px 2px rgba(56,178,60,1);
        border-radius: 24px 24px 24px 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        span{
            display: block;
            width: 100px;
            font-size: 16px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
            transform: scale(0.7);
            white-space: nowrap;
        }
        img{
            margin-left: -10px;
            width: 10px;
            height: 10px;
        }
    }
}
</style>