<template>
  <div class="wateringDiglog">
    <div class="shuihu-img">
        <img class="shuihu" :src="$imgs['shuihu.png']" alt="" @click="initWaterAdFun">
        <img class="close-shui" :src="$imgs['close-shui.png']" alt="" @click="emit('close')">
    </div>
    <div class="btn" @click="initWaterAdFun">
        <span>{{ state.advertisement.看廣告領減碳值 }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useLang, useToast, useEventBus,useDialog } from 'hook';
const bus = useEventBus()
const { state, lang } = useLang()
const emit = defineEmits(['close'])

const initWaterAdFun = ()=>{
    bus.emit('initWaterAd')
    emit('close')
}
</script>

<style lang="less" scoped>
.wateringDiglog{
    display: flex;
    flex-direction: column;
    color: #fff;
    .shuihu-img {
        .shuihu {
            width: 430px;
            height: 336px;
        }
        .close-shui{
            width: 32px;
            height: 32px;
            position: absolute;
            top: 0;
            left: 0;
        }
    }
    .btn{
        margin-top: 64px;
        width: 430px;
        height: 84px;
        background: linear-gradient( 180deg, #F1FFE6 0%, #8CDC88 100%);
        box-shadow: 0px 8px 0px 2px #61C87D, inset 0px 4px 0px 2px #FFFFFF;
        border-radius: 48px 12px 48px 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        span{
            font-size: 36px;
            color: #22992C;
            line-height: 32px;
            text-align: center;
        }
    }
}
</style>