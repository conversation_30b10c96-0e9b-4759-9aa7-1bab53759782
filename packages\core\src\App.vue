<script lang="tsx">
import { defineComponent, VNode, getCurrentInstance } from 'vue'
import { useApi, useLoading, useRouter, useEnvConfig,useLang } from 'hook'
import { useAppStore, useUserStore } from './store'
import { reactive } from 'vue'
import { configureShare, inApp } from '@via/mylink-sdk'
import { imgs } from '@/assets/imgs'
import loadingImg from '@/assets/imgs/loading.gif'
import loadingBg from '@/assets/imgs/loadingBg.png'
import { assign } from "@/i18n/index";
const { get } = useApi()

function loadImg(){
    return new Promise((resolve, reject) => {
        const arr = [loadingImg,loadingBg]
        let num = 0;
        let img = new Array()
        for (let index = 0; index < arr.length; index++) {
            img[index] = new Image()
            img[index].src = arr[index]
            img[index].onload = function(){
                num += 1
                if(num == 2) {
                    resolve(true)
                }
            }
        }
    })
}

export default defineComponent({
    name: 'App',
    setup() {
        assign()
        const appStore = useAppStore()
        const userStore = useUserStore()
        const envConfig = useEnvConfig()
        const  loading  = useLoading()
        const  { lang,state }  = useLang()
        const { router, currentRoute } = useRouter()
        console.log(router.currentRoute.value.path,'routerrouterrouterrouter');
        
        const cns = getCurrentInstance()
        async function init(){
            await loadImg()
            if (envConfig.RUN_ENV == 'develop' || inApp.value) {
                await userStore.login(false)
            }
            // if(!userStore.inLogin){
            //     loading.loading('close')
            //     router.replace({
            //         path:'/share',
            //         query: {
            //             "hideNavigationBar": 'true'
            //         }
            //     })
            // }
            cns.appContext.config.globalProperties.$reslove()
        }
        init()
        return () => (
            <div class="main" >
                { router.currentRoute.value.path == '/achievement' && <div class="goHome-app-btn" onClick={
                    ()=>router.replace({
                        path: '/home',
                        query: {
                            hideNavigationBar: 'true',
                        }
                    })
                }>{ state.back.text }</div> }
                {appStore.isFinishKV ? (
                    <router-view>
                        {{
                            default: (scope: { Component: VNode }) => {
                                 return scope.Component
                            }
                        }}
                    </router-view>
                ) : (
                    <div></div>
                )}
            </div>
        )
    }
})
</script>
<style lang="less">
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html,
body {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-appearance: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

li {
    list-style: none;
}

.main {
    .p-r();
    width: 100vw;
    min-height: 100vh;
}
.goHome-app-btn{
    width: 144px;
    height: 50px;
    background: linear-gradient( 180deg, rgba(255,255,255,0.9) 0%, rgba(232,255,188,0.9) 100%);
    box-shadow: 0px 6 12px 2px rgba(0,0,0,0.1);
    border-radius: 26px 0px 0px 26px;
    position: fixed;
    right: 0;
    top: 302px;
    font-family: PingFang TC, PingFang TC;
    font-size: 24px;
    color: #2A803D;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 21;
}
</style>
