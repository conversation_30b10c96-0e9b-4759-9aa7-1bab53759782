<template>
    <div class="updateNFTDilog-content">
        <h1>{{ state.nft.confirmUpgradeTitle }}</h1>
        <div class="showImg">
            <img :src="$imgs[`NTFDetail/upgrade/${lang}/${code}.png`]" />
        </div>
        <div class="text">{{ state.nft.confirmUpgradeContent1 }}</div>
        <div class="notice">
            <p>{{ state.nft.confirmUpgradeContent2 }}</p>
            <p>{{ state.nft.confirmUpgradeContent3 }}</p>
        </div>
        <div class="bottom">
            <div class="btn disactive" @click="emit('close')">
                {{ state.nft.cancelUpgradeBtn }}
            </div>
            <div class="btn active" @click="casting">
                {{ state.nft.confirmUpgradeBtn }}
            </div>
        </div>

        <button class="closeBtn" @click="emit('close')"></button>
    </div>
</template>

<script lang="ts" setup>
/** nft升级弹窗 */
import { useLang, useRouter,useEventBus } from 'hook'
import { logEventStatistics } from "@via/mylink-sdk";
import { defineProps } from "vue";
const { router, currentRoute } = useRouter()
const { state,lang } = useLang()
const eventBus = useEventBus()
let emit = defineEmits(['close'])
let props = defineProps({
    code:{
        type:String,
        required:true
    }
})

function casting() {
   logEventStatistics('garden_re_mint_nft')
   emit('close')
   eventBus.emit('casting')
}

</script>

<style lang="less" scoped>
.updateNFTDilog-content {
    padding: 50px;
    width: 544px;
    background: #ffffff;
    border-radius: 24px 24px 24px 24px;
    text-align: center;

    h1 {
        font-weight: 600;
        font-size: 32px;
        color: #4e5b7e;
    }

    .showImg {
        img {
            width: 480px;
        }
    }

    .text {
        font-size: 24px;
        color: #4e5b7e;
        text-align: left;
    }

    .notice {
        font-size: 24px;
        color: #ef8a00;
        text-align: left;
    }

    .bottom {
        display: flex;
        justify-content: space-between;;

        .btn {
            width: 200px;
            height: 56px;
            border-radius: 48px 48px 48px 48px;
            background: #fff;
            font-size: 28px;
            text-align: center;
            line-height: 56px;
        }

        .active {
            background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
            color: #ffffff;
        }

        .disactive {
            background: #ffffff;
            border: 2px solid #a7aec3;
            color: #a7aec3;
        }
    }

    > * {
        margin-bottom: 40px;
    }

    .closeBtn {
        position: absolute;
        width: 56px;
        height: 56px;
        background: url('@assets/imgs/closeIcon.png') no-repeat;
        background-size: 100% 100%;
        border: none;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -180px;
    }
}
</style>
