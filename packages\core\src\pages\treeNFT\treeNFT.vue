<template>
    <main class="treeNFT">
        <!-- <img @click="router.go(-1)" class="toHome" src="/src/assets/imgs/return2.png" alt="" /> -->
        <!--nft背景动效-->
        <div ref="sprite"></div>
        <div class="treeNFT__content">
            <!-- <div class="content__Issuer">
                    <img src="../../assets/imgs/logo.png" alt="">
                    <div>
                        <p class="color__mylink">{{state.nft.MyLink}}</p>
                        <p class="fs__25">{{ state.nft.發行方 }}</p>
                    </div>
            </div> -->
            <h1>{{ state.nft.作品名稱 }}:{{ state.nft.flowerList[flowerNum] }}</h1>
            <div class="equity">
                <div class="side">
                    <div class="left">{{ state.nft.equityTitle1 }}</div>
                    <div class="right">{{ state.nft.equityContent1 }}</div>
                </div>
                <div class="side">
                    <div class="left">{{ state.nft.equityTitle2 }}</div>
                    <div class="right">{{ state.nft.equityContent2 }}</div>
                </div>
            </div>
            <div class="issue">
                <div class="left">
                    <img src="@/assets/imgs/NTFDetail/logo.png" />
                </div>
                <div class="right">
                    <h2>MyLink</h2>
                    <p>{{ state.nft.發行方 }}</p>
                </div>
            </div>
            <div class="content">
                <div class="title firstTopTitle">
                    <h3>{{ state.nft.detailTitle }}</h3>
                </div>
                <ul class="title">
                    <li>{{ state.nft.關於作品 }}</li>
                    <li class="colors">{{ state.nft.MyGarden種成獎勵 }}</li>
                </ul>
                <ul class="title">
                    <li>{{ state.nft.發行時間 }}</li>
                    <li>{{ nowTime }}</li>
                </ul>
                <ul class="title">
                    <li>{{ state.nft.鑄造商 }}</li>
                    <li>{{ state.nft.中移鏈 }}</li>
                </ul>
                <ul class="title">
                    <li>{{ state.nft.協議標準 }}</li>
                    <li>{{ state.nft.ERC721 }}</li>
                </ul>
                <ul class="title">
                    <li class="firstBottomTitle">{{ state.nft.權益內容 }}</li>
                    <li class="lastBottomTitle">{{ state.nft.鑄造後在MyHome佈置此植物可每日生產1個減碳值 }}</li>
                </ul>

                <!-- <ul class="title__contnet">
                    <li class="first__contnet">123</li>
                    <li>{{state.nft.MyGarden種成獎勵}}</li>
                    <li>123</li>
                    <li>{{state.nft.中移鏈}}</li>
                    <li>{{state.nft.ERC721}}</li>
                    <li>{{state.nft.鑄造後在MyHome佈置此植物可每日生產1個減碳值}}</li>
                </ul> -->
            </div>
            <div class="confirm">
                <button @click="isCasting" class="btn__bottom">{{ state.nft.確認鑄造 }}</button>
            </div>
            <van-popup
                v-model:show="show"
                eleport="NFTDialog"
                @close="clickBlack"
                style="background: none"
                :close-on-click-overlay="result"
                close-on-popstate
            >
                <NFTDialog :results="result" :cause="cause" :code="code" :rarity="rarity"></NFTDialog>
            </van-popup>
        </div>
    </main>
</template>

<script lang="ts" setup>
import NFTDialog from '@/components/NFTDialog.vue'
import { onBeforeMount, onMounted, ref, nextTick, computed } from 'vue'
import { useDayjs, useRouter, useDialog, useLang, useEventBus } from 'hook'
import { useNFTStore, useTreeStore, useTaskStore } from '@/store'
import { logEventStatistics } from '@via/mylink-sdk'
import confirmUpgradeNFTDialog from '@/components/confirmUpgradeNFTDialog.vue'
import { imgs } from '@/assets/imgs'
const { currentRoute, router } = useRouter()
const { state, lang } = useLang()
const eventBus = useEventBus()
const nftStore = useNFTStore()
const treeStore = useTreeStore()
const taskStore = useTaskStore()
const dialog = useDialog({ confirmUpgradeNFTDialog })
const { dayjs } = useDayjs()
/**
 * 当前时间
 */
const nowTime = computed(() => {
    if (lang.value === 'en') {
        const months = [
            'January',
            'February',
            'March',
            'April',
            'May',
            'June',
            'July',
            'August',
            'September',
            'October',
            'November',
            'December'
        ]
        return dayjs().format(`DD * YYYY HH:mm`).replace('*', months[dayjs().month()])
    } else {
        return dayjs().format('YYYY年MM月DD日 HH:mm')
    }
})
const sprite = ref()
// 确认铸造
const show = ref(false) //弹窗显示
const result = ref<boolean | null>(null) //铸造结果
const cause = ref<string>('') //失败原因
const code = ref('')
const rarity = ref()

/** 确认铸造nft */
const isCasting = async () => {
    let tree = treeStore.treeList.trees.filter((item) => item.tree_id == currentRoute?.query.treeId)[0]
    let treeCasting = propsList.value.filter((item) => item.code.includes(tree.code))
    code.value = treeCasting[0].code
    rarity.value = treeCasting[0].rarity
    logEventStatistics('garden_confirm_mint_nft')
    if (tree.nft_upgrade_notice) {
        //二次铸造nft
        eventBus.once('casting', casting)
        let upgradeNFTDialog = dialog.get('confirmUpgradeNFTDialog')
        upgradeNFTDialog.show({ code: tree.code }, { maskClose: false })
        return
    }
    await casting()
}

const casting = async () => {
    const CastingResults = await nftStore.nftFoundryClick(currentRoute?.query.treeId as string)
    if (CastingResults.nft_group_id) {
        result.value = CastingResults.nft_group_id
    } else {
        result.value = false
        cause.value = CastingResults.msg
    }
    show.value = true
}

// 点击黑罩层
const clickBlack = () => {
    if (result.value === false) {
        return
    }
    //成就进入的页面
    if (currentRoute?.query.path === 'activity') {
        nftStore.nftsConfig.isActivityOpen = true
    }
    eventBus.emit('back', code.value)
    router.back()
}

let propsList = ref<Record<string, any>[]>([])
onMounted(async () => {
    dialog.closeAll()
    // 接收返回关闭弹窗
    eventBus.on('recover', () => {
        show.value = false
    })

    initSprite()
    let res = await taskStore.getPropList()
    propsList.value = res.items.filter((item) => item.code.includes('title_'))
})
onBeforeMount(() => {
    flowerContent()
})

/** 初始化精灵 */
const initSprite = async () => {
    const html = sprite.value
    if (html) {
        // 清除原来的样式
        html.classList.remove('sprite')
        const code = currentRoute?.query.treeCode
        if (code) {
            html.style['background-image'] = `url(${imgs[`NTFDetail/animation_big/${code}.jpg`]})`
            nextTick(() => html.classList.add('sprite'))
        }
    }
}
// 获取对应花的内容
let flowerNum = ref(0) //花的对应三语数组
const flowerContent = () => {
    const flowerId = currentRoute?.query.treeId
    switch (flowerId) {
        case '1000':
            flowerNum.value = 1
            break
        case '1001':
            flowerNum.value = 2
            break
        case '1002':
            flowerNum.value = 0
            break
        case '1003':
            flowerNum.value = 3
            break
        case '1004':
            flowerNum.value = 4
            break
        case '1005':
            flowerNum.value = 5
            break
        case '1006':
            flowerNum.value = 6
            break
    }
}
</script>

<style lang="less" scoped>
@import './treeNFT.scss';

// TODO 因为以下是less代码，不方便直接放入scss文件内，请写成scss文件
@spw: 100vw;
@sph: 375px;
.frame(@col:0, @row:0) {
    background-position: -@spw * @col -@sph * @row;
}
.sprite {
    width: 100vw;
    height: 100vw;
    animation: myAnimation 3s steps(1) /*infinite*/;
    background-size: 100vw * 6 100vw * 5;
    // 让动画保持在最后一帧
    // animation-fill-mode: forwards;
    // .frame(5,4);
    background-position: -500vw -400vw;
    @keyframes myAnimation {
        0% {
            background-position: 0px 0px;
        }
        3.45% {
            background-position: -100vw 0px;
        }
        6.90% {
            background-position: -200vw 0px;
        }
        10.34% {
            background-position: -300vw 0px;
        }
        13.79% {
            background-position: -400vw 0px;
        }
        17.24% {
            background-position: -500vw 0px;
        }
        20.69% {
            background-position: 0px -100vw;
        }
        24.14% {
            background-position: -100vw -100vw;
        }
        27.59% {
            background-position: -200vw -100vw;
        }
        31.03% {
            background-position: -300vw -100vw;
        }
        34.48% {
            background-position: -400vw -100vw;
        }
        37.93% {
            background-position: -500vw -100vw;
        }
        41.38% {
            background-position: 0px -200vw;
        }
        44.83% {
            background-position: -100vw -200vw;
        }
        48.28% {
            background-position: -200vw -200vw;
        }
        51.72% {
            background-position: -300vw -200vw;
        }
        55.17% {
            background-position: -400vw -200vw;
        }
        58.62% {
            background-position: -500vw -200vw;
        }
        62.07% {
            background-position: 0px -300vw;
        }
        65.52% {
            background-position: -100vw -300vw;
        }
        68.97% {
            background-position: -200vw -300vw;
        }
        72.41% {
            background-position: -300vw -300vw;
        }
        75.86% {
            background-position: -400vw -300vw;
        }
        79.31% {
            background-position: -500vw -300vw;
        }
        82.76% {
            background-position: 0px -400vw;
        }
        86.21% {
            background-position: -100vw -400vw;
        }
        89.66% {
            background-position: -200vw -400vw;
        }
        93.10% {
            background-position: -300vw -400vw;
        }
        96.55% {
            background-position: -400vw -400vw;
        }
        100.00% {
            background-position: -500vw -400vw;
        }
    }
}
</style>
