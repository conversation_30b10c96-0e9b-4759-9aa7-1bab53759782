import { defineConfig } from 'vite'
import path from 'path'
import vue from '@vitejs/plugin-vue'
import libInjectCss from './plugins/libInjectCss'
import { useAlias } from './config/useAlias'

export default defineConfig({
  plugins: [vue(), libInjectCss()],
  resolve: {
    alias: useAlias()
  },
  build: {
    lib: {
      entry: path.resolve(__dirname, 'src/index.ts'),
      name: 'Hook',
      formats: ['umd'],
      fileName: () => `hook.min.js`
    },
    rollupOptions: {
      external: ['vue'],
      output: {
        sourcemap: false,
        globals: {
          vue: 'Vue'
        }
      }
    },
    cssCodeSplit: false
  }
})
