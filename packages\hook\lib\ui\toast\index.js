import { createApp } from 'vue';
import ToastApp from './ToastApp.vue';
import { state } from './state';
const toastApp = createApp(ToastApp);
toastApp.mount('#app-toast');
const defaultOption = {
    type: 'center',
    duration: 2500,
    wordWrap: true,
    width: 'auto'
};
let toastTimer = -1;
function toast(tip, config = {}, icon = '', secondTip = '') {
    let option = {};
    Object.assign(option, defaultOption, config || {});
    if (toastTimer !== -1) {
        clearTimeout(toastTimer);
        toastTimer = -1;
    }
    state.tip = tip;
    state.wordWrap = option.wordWrap || false;
    state.type = option.type || 'center';
    state.icon = icon;
    state.secondTip = secondTip;
    state.extStyle.width = option.width || '';
    state.show = true;
    toastTimer = window.setTimeout(() => {
        state.show = false;
        toastTimer = -1;
    }, option.duration);
}
toast.top = (tip, config = {}) => toast(tip, Object.assign(config, { type: 'top' }));
toast.center = (tip, config = {}) => toast(tip, Object.assign(config, { type: 'center' }));
toast.bottom = (tip, config = {}) => toast(tip, Object.assign(config, { type: 'bottom' }));
toast.withIcon = (icon, tip, secondTip, config = {}) => toast(tip, Object.assign(config, {}), icon, secondTip);
export function useToast() {
    return {
        app: toastApp,
        toast
    };
}
