import { createApp } from 'vue'
import LoadingApp from './LoadingApp.vue'
import { state } from './state'

const loadingApp = createApp(LoadingApp)
loadingApp.mount('#app-loading')

function loading(type?: 'open' | 'close') {
  if (type === 'close') {
    state.show = false
    return
  }
  state.show = true
}

loading.open = () => loading('open')
loading.close = () => loading('close')

export function useLoading() {
  return {
    app: loadingApp,
    loading
  }
}
