export declare enum RuntimeMode {
    DEV = "dev",
    PROD = "prod"
}
export declare enum RuntimeEnv {
    web = "web",
    wx = "wx",
    wxMini = "wxMini",
    other = "other"
}
export declare enum MobileType {
    web = "web",
    android = "android",
    ios = "ios"
}
declare function setRuntimeEnv(env: RuntimeEnv): void;
export declare function useRuntimeEnv(): {
    runtimeEnv: RuntimeEnv;
    runtimeMode: RuntimeMode;
    mobileType: MobileType;
    setRuntimeEnv: typeof setRuntimeEnv;
};
export {};
