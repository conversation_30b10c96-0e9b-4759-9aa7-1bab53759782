<template>
  <div class="ad">
    <div class="title">
        {{ props.title }}
    </div>
    <div class="desc" @click="goRule" v-html="props.desc"></div>
    <div class="btn" @click="emit('close')">
        <span>{{ state.advertisement.知道了 }}</span>
    </div>
  </div>
  <img @click="emit('close')" :src="$imgs['closeIcon.png']" alt="">
</template>

<script lang="ts" setup>
import { useRouter,useLang } from "hook";
const { state, lang } = useLang()
const props = defineProps<{
    title:string
    desc:string
}>()
const emit = defineEmits(['close'])
const { router } = useRouter()
const goRule = ()=>{
    router.push({
        path:'/rule',
        query:{
            hideNavigationBar: 'true',
        }
    })
}
</script>

<style lang="less" scoped>
.ad{
    width: 528px;
    background: #FFFFFF;
    border-radius: 24px 24px 24px 24px;
    padding: 48px 0;
    .title {
        padding: 0 48px;
        font-weight: 600;
        font-size: 32px;
        color: #4E5B7E;
        line-height: 44px;
        text-align: center;
    }
    .desc {
        font-weight: 400;
        font-size: 24px;
        color: #4E5B7E;
        text-align: center;
        margin-top: 24px;
    }
    .btn {
        width: 220px;
        height: 56px;
        background: linear-gradient( 180deg, #FDDD3E 0%, #FBB629 100%);
        border-radius: 48px 48px 48px 48px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 32px auto 0;
        span {
            font-weight: bold;
            font-size: 28px;
            color: #FFFFFF;
            line-height: 32px;
            text-shadow: 0px 0px 12px #FBAC2E;
            text-align: center;
        }
    }
}
img{
    width: 56px;
    height: 56px;
    margin: 80px auto 0;
    display: block;
}
</style>