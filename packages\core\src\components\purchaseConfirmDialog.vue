<template>
    <div class="dialog-wrap">
        <div class="pic" v-if="target === 0"><img :src="$imgs['dialog/purchase-icon2.png']" /></div>
        <div class="title">{{ $lang.dialogs.purchaseConfirm.title }}</div>
        <div class="sub" v-if="target === 0">{{ $langFormat($lang.dialogs.purchaseConfirm.sub,[props.propBag]) }}</div>
        <div class="sub" v-else>{{ state.shopping.是否確認使用 }}<span style="color: #EF8A00;">{{ `${num}g` }}</span>{{ state.shopping.減碳值購買.replace('NNN',propBag) }}</div>
        <div class="tips" v-if="desc">{{ desc }}</div>
        <div class="btn-group">
            <button class="cancel" @click="cancel">{{ $lang.dialogs.purchaseConfirm.cancelBtnTxt }}</button>
            <button class="confirm" @click="confirm">{{ $lang.dialogs.purchaseConfirm.confirmBtnTxt }}</button>
        </div>
        <button class="close" @click="emit('close')"></button>
    </div>
</template>
<script setup lang="ts">
import { useDialog,useLang } from 'hook'
import { inject } from 'vue'
import { logEventStatistics } from '@via/mylink-sdk'
const { state } = useLang()
const props = withDefaults(defineProps<{
    propBag: string
    desc:string
    target:number
    num:number
}>(), {
    propBag: '道具包'
})
const emit = defineEmits(['close'])
const uuid = inject('uuid') as string
const confirm = () => {
    useDialog().getInstance(uuid)?.emit('confirm')
}
const cancel = () => {
    useDialog().getInstance(uuid)?.emit('cancel')
}
</script>
<style lang="less" scoped>
.dialog-wrap {
    width: 600px;
    background: #FFFFFF;
    border-radius: 24px 24px 24px 24px;
    padding: 48px 40px;
    position: relative;
    .pic {
        width: 120px;
        margin: auto;
        img {
            display: block;
            width: 100%;
        }
    }
    .title {
        font-size: 32px;
        font-weight: bold;
        color: #4E5B7E;
        text-align: center;
        margin-top: 16px;
    }
    .sub {
        font-size: 24px;
        color: #4E5B7E;
        text-align: center;
        margin: 16px 0 12px;
        line-height: 40px;
        span {
            color: #EF8A28;
        }
    }
    .tips {
        font-size: 18px;
        color: #196F20;
        text-align: center;
        margin-bottom: 28px;
    }
    .btn-group {
        display: flex;
        justify-content: space-around;
        button {
            width: 200px;
            height: 56px;
            font-size: 28px;
            background: #FFFFFF;
            border-radius: 48px 48px 48px 48px;
            border: 2px solid transparent;
            &.cancel {
                color: #A7AEC3;
                border: 2px solid #A7AEC3;
            }
            &.confirm {
                background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
                color: white;
            }
        }
    }
    .close {
        width: 56px;
        height: 56px;
        background: url('@/assets/imgs/closeIcon.png') no-repeat;
        background-size: 100% auto;
        position: absolute;
        display: block;
        border: none;
        left: 0;
        right: 0;
        margin: auto;
        bottom: -150px;
    }
}
</style>