<template>
    <!-- 活动 -->
  <div class="box" >
      <div class="titleImg">
          <img :src="$imgs['acttitle.png']" alt="">
          <p>{{state.home.活动中心}}</p>
      </div>
      <div class="container">
          <p class="num">{{ activityArr.length ? index+1 : 0}}/{{activityArr.length}}</p>
          <img v-if="index<=0" class="jiantou left1" :src="$imgs['activity/huise.png']" alt="">
          <img v-if="index>=activityArr.length-1" class="jiantou right1" :src="$imgs['activity/huise.png']" alt="">
          <img v-if="index>0" @click="lastPage" class="jiantou left2" :src="$imgs[`activity/${index>0?'liangse':'huise'}.png`]" alt="">
          <img v-if="index<activityArr.length-1" @click="nextPage" class="jiantou right2" :src="$imgs['activity/liangse.png']" alt="">
          <linkageAct v-if="indexArr[index] < 0" @toRule='toRule'/>
          <activity v-if="activityArr.length && indexArr[index] >= 0" :item='activityArr[index]' :actDig="true" @toRule='toRule' class="activityBox" />
          <div class="null" v-if="activityArr.length == 0">
              <img class="ai" :src="$imgs['ai.png']" alt="">
              <p>{{state.home.暂时没有活动}}</p>
          </div>
      </div>
      <button class="btn" @click="emit('close')"></button>
  </div>
</template>

<script setup lang='ts'>
import activity from './activity.vue'
import linkageAct from './linkageAct.vue'
import { useLang, useRouter } from 'hook'
import { ref, onBeforeMount } from 'vue'
import { useTaskStore, useUserStore } from '@store/index'
let emit = defineEmits(['close'])
const { router, currentRoute } = useRouter()

const { state } = useLang()
const props = defineProps({
  list: {
      type:  Array,
      required: true
  }
})


let index = ref(0)
let indexArr = ref([])
let activityArr = ref([])
onBeforeMount(() => {
    // if(props.list.includes(-1) && !useUserStore().isSlash){
    //     indexArr.value.push(-1)
    //     activityArr.value.push(-1)
    // }
    // console.log(activityArr.value);
    
    // debugger
    props.list.forEach(ele => {
        if(ele < 0) return
        let arr:Array<never> = state.activityArr.filter((item, index:never) => {
            if(ele == item.id){
                indexArr.value.push(index)
            }
            return ele == item.id
        })
        activityArr.value.push(...arr)
    })
    if(props.list.includes(-1) && !useUserStore().isSlash){
        indexArr.value.splice(2, 0, -1)
        activityArr.value.splice(2, 0, -1)
    }
    if(useUserStore().isSlash){
        activityArr.value = activityArr.value.filter((item) => {
            return item.id != 4
        })
    }
})

const lastPage = () => {
    if(index.value <= 0){
        index.value = 0
        return
    }
    index.value --
}

const nextPage = () => {
    if(index.value >= activityArr.value.length - 1){
        return
    }
    index.value ++
}

const toRule = (type?: any) =>{
    emit('close')
    router.push({
        path: '/rule',
        query: {
            "hideNavigationBar": 'true',
            "type": type
        }
    })
}

</script>

<style lang='less' scoped>
.box{
    position: relative;
    background: linear-gradient(94deg, #C3E8C0 0%, #C3E8C0 25%, #D7E8C8 57%, #7ECED8 100%);
    border-radius: 72px 32px 72px 32px;
    padding-top: 56px;
    >.titleImg{
        position: absolute;
        top: 0;
        width: 480px;
        z-index: 2;
        left: 50%;
        transform: translate(-50%, 0);
        top: -38px;
        img{
            width: 100%;
        }
        p{
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translate(-50%, 0);
            font-size: 36px;
            font-family: PingFang HK-Semibold, PingFang HK;
            font-weight: 600;
            color: #FFFFFF;
            white-space: nowrap;
        }
    }
    .container{
        width: 670px;
        min-height: 820px;
        background: #FFFFFF;
        box-shadow: 0px -4px 0px 2px rgba(108,147,80,0.22);
        border-radius: 72px 32px 64px 32px;
        padding-top: 100px;
        padding-bottom: 60px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        >.activityBox{
            // position: absolute;
            // left: 50%;
            // transform: translate(-50%, 0);
            // bottom: 64px;
        }
        >.null{
            display: flex;
            flex-direction: column;
            align-items: center;
            >.ai{
                width: 336px;
            }
            >p{
                margin-top: 64px;
                white-space: nowrap;
                font-size: 28px;
                font-family: PingFang HK-Medium, PingFang HK;
                font-weight: 500;
                color: #4E5B7E;
                line-height: 32px;
            }
        }
        >.num{
            background: linear-gradient(180deg, #D9EAE2 0%, #F5F5F5 100%);
            font-size: 24px;
            font-family: PingFang HK-Semibold, PingFang HK;
            font-weight: 600;
            color: #4E5B7E;
            display: inline-block;
            border-radius: 24px 24px 24px 24px;
            line-height: 30px;
            vertical-align: middle;
            position: absolute;
            top: 56px;
            left: 50%;
            transform: translate(-50%, 0);
            padding: 4px 50px;
        }
        >.jiantou{
            position: absolute;
            height: 48px;
            &.right1{
                transform: rotate(180deg);
                bottom: 424px;
                right: 16px;
            }
            &.left1{
                bottom: 424px;
                left: 16px;
            }
            &.right2{
                bottom: 424px;
                right: 16px;
            }
            &.left2{
                transform: rotate(180deg);
                bottom: 424px;
                left: 16px;
            }
        }
    }
    >.btn{
        position: absolute;
        width: 56px;
        height: 56px;
        background: url('@assets/imgs/closeIcon.png') no-repeat;
        background-size: 100% 100%;
        border: none;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -100px;
    }
}
</style>