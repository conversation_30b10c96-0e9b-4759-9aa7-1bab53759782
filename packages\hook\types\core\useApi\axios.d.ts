import { Method, AxiosRequestConfig } from 'axios';
export type AxiosParams = AxiosRequestConfig & {
    refresh?: boolean;
};
export type ReturnData<T = any> = {
    code: number;
    msg: string;
    data: T;
};
declare function request<T = any>(method: Method, url: string, opts?: AxiosParams): Promise<T>;
declare function get<T = any>(url: string, opts?: AxiosParams): Promise<T>;
declare function post<T = any>(url: string, opts?: AxiosParams): Promise<T>;
declare function registerResponseInterceptor(fn?: <T>(res: ReturnData<T>) => Promise<ReturnData<T>>): void;
export { request, post, get, registerResponseInterceptor };
