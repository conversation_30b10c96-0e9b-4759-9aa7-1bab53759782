const fs = require('fs')
const path = require('path')

function getProjects(filePath) {
    const projects = []
    const list = fs.readdirSync(filePath)
    list.forEach(item=> {
        const itemPath = path.join(filePath, item)
        if (item.startsWith('@')) {
            projects.push(...getProjects(itemPath))
        } else if (fs.statSync(itemPath).isDirectory()) {
            projects.push(itemPath)
        }
    })
    return projects
}
// vetur.config.js
/** @type {import('vls').VeturConfig} */
module.exports = {
    // **optional** default: `{}`
    // override vscode settings
    // Notice: It only affects the settings used by Vetur.
    settings: {
        "vetur.useWorkspaceDependencies": true,
        "vetur.experimental.templateInterpolationService": false
    },
    // **optional** default: `[{ root: './' }]`
    // support monorepos
    projects: getProjects(path.resolve('packages'))
}
