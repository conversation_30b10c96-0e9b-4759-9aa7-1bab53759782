.package {
    min-height: 100vh;
    width: 100vw;
    height: 100vh;
    background: #88E18B;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    .img {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        width: 100vw;
        height: 750px;
        position: relative;
        .sceneProps{
            bottom: 130px;
        }
        img {
            width: 100%;
            height: 1200px;
        }
    }
    
    .BearImg{
        width: 480px;
        position: absolute;
        top: 150px;
        left: 50%;
        transform: translate(-50%, 0);
        display: flex;
        align-items: center;
        justify-content: center;
        >img{
            width: 100%;
        }
    }
    .tree-entity {
        position: absolute;
        width: 100%;
        height: 595px;
        top: 0;
        z-index: 20;
        .nitu{
            position: absolute;
            bottom: -10px;
            left: 50%;
            width: 81px;
            transform:  translate(-50%, 0);
        }
        .yinying{
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translate(-50%, 0);
            width: 124px;
        }
        .xiu<PERSON>u{
            bottom: -12px;
            width: 144px;
        }
        .tree-item {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: 0;
        }
    }
    .shopping-layer{
        width: 100%;
        position: absolute;
        top: 550px;
        z-index: 0;
        left: 0;
        height: 228px;
        background: linear-gradient(180deg, rgba(255,255,255,0) 0%, #68D685 100%);
    }
    .positionTab{
        position: absolute;
        width: 100%;
        top: 750px;
        z-index: 100;
        .close {
            position: absolute;
            right: 32px;
            top: -20px;
            .wh(56,56);
        }
        .kindTab{
            display: flex;
            flex-direction: column;
            align-items: center;
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            top: -90px;
            // background: rgba(255,255,255,0.5);
            // box-shadow: inset 0px 0px 24px 2px rgba(25,75,31,0.13);
            font-size: 24px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            white-space: nowrap;
            .prompt{
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                position: absolute;
                top: -70px;
                // width: 204px;
                height: 46px;
                background: #4F8446;
                border-radius: 12px 12px 12px 12px;
                opacity: 1;
                z-index: 999;
                .promptText{
                    display: flex;
                    align-items: center;
                    height: 46px;
                    padding-top: 15px;
                    // flex-wrap: wrap;
                    // width: 204px;
                    img{
                        width: 32px;
                        height: 24px;
                        margin: 0 10px 0 28px;
                    }
                    p{
                        font-size: 24px;
                        font-family: PingFang SC, PingFang SC;
                        font-weight: 500;
                        color: #FFFFFF;
                        margin: 0 10px 0 0;
                    }
                }
                .triangle{
                    width: 22px;
                    height: 16px;
                    position: relative;
                    top:-2px;
                    // left: -220px;
                    z-index: 999;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
            }
            .tab-choose{
                display: flex;
            }
            .kinditem{
                color: #196F20;
                position: relative;
                .tm{
                    width: 116px;
                    position: absolute;
                    top: -16px;
                    &.l{
                        left: -8px;
                    }
                    &.r{
                        transform: rotateY(180deg);
                        right: -8px;
                    }
                }
                .redpoint{
                    display: block;
                    position: absolute;
                    right: -3px;
                    top: -5px;
                    width: 20px;
                    height: 20px;
                    background: #F05548;
                    border-radius: 50%;
                }
                .new{
                    position: absolute;
                    width: 86px;
                    height: 40px;
                    right: 40px;
                    top: -32px;
                }
            }
            .l1{
                padding: 10px 76px 10px 52px;
                color: #196F20;
                background-color: #fff;
                border-radius:  48px 0 0 48px;
                z-index: 0;
            }
            .l2{
                color: #fff;
                padding: 10px 26px 10px 26px;
                background: linear-gradient(1deg, #33B43F 0%, #47D048 100%);
                box-shadow: inset 0px 4px 0px 2px rgba(100,222,105,1);
                border-radius: 48px 48px 48px 48px;
                width: 240px;
                display: flex;
                justify-content: center;
                z-index: 1;
            }
            .r1{
                padding: 10px 26px 10px 52px;
                color: #196F20;
                background-color: #fff;
                border-radius: 0 48px 48px 0;
                margin-left: -40px;
                z-index: 0;
            }
            .r2{
                color: #fff;
                padding: 10px 26px 10px 26px;
                background: linear-gradient(1deg, #33B43F 0%, #47D048 100%);
                box-shadow: inset 0px 4px 0px 2px rgba(100,222,105,1);
                border-radius: 48px 48px 48px 48px;
                margin-left: -40px;
                z-index: 1;
            }
        }
        .shopping-kindTab{
            top: -112px;
        }
        .bktitle{
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            top: -5px;
            .mgTitle{
                position: relative;
                display: flex;
                align-items: center;
                // justify-content: center;
                font-size: 28px;
                font-family: PingFang SC-Semibold, PingFang SC;
                font-weight: 600;
                .wh(510, 68);
                .bglong{
                    .wh(100%, 100%);
                    position: absolute;
                    top: 0;
                    left: 0;
                }
                .jj{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    p{
                        display: inline-block;
                        >img{
                            height: 34px;
                            margin-right: 10px;
                            display: inline-block;
                        }
                    }
                }
                .cardPoint{
                    >p{
                        position: relative;
                        &::after{
                            content: '';
                            display: block;
                            position: absolute;
                            width: 20px;
                            height: 20px;
                            background: #FD4232;
                            top: -6px;
                            right: -10px;
                            border-radius: 50%;
                        }
                    }
                    
                }
                .cxk{
                    flex: 1;
                    position: relative;
                    color: #A7AEC3;
                    height: 100%;
                    >p{
                        position: relative;
                    }
                }
                .cxk1{
                    .wh(280, 80);
                    flex-shrink: 0;
                    position: relative;
                    >img{
                        position: absolute;
                        bottom: 5px;
                        .wh(100%, 100%);
                    }
                    p{
                        color: #fff;
                        position: relative;
                    }
                }
            }
            .vipTitle{
                display: flex;
                position: relative;
                align-items: center;
                justify-content: center;
                .wh(280, 80);
                >img{
                    top: -7px;
                    left: 0;
                    position: absolute;
                    .wh(100%, 100%);
                }
                >p{
                    position: relative;
                    font-size: 28px;
                    font-weight: 600;
                    color: #FFFFFF;
                    white-space: nowrap;
                }
            }
        }
    }
    .PropItem{
        flex: 1;
    }
    .container{
        width: 100vw;
        background: #FFFFFF;
        border-radius: 48px 48px 0px 0px;
        flex: 1;
        position: relative;
        padding-top: 92px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .tab-kind{
            width: 100%;
            // height: 70px;
            flex-shrink: 0;
            // overflow: hidden;
            position: relative;
            .clearTab{
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding-left: 28px;
                padding-right: 48px;
                padding-top: 16px;
                padding-bottom: 16px;
                p{
                    font-size: 24px;
                    font-family: PingFang SC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: #196F20;
                }
                .clearBtn{
                    font-size: 24px;
                    font-family: PingFang SC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: #4E5B7E;
                    padding: 8px 36px;
                    background: #FFFFFF;
                    border-radius: 40px 40px 40px 40px;
                    border: 2px solid #4E5B7E;
                }
            }
            .tab-k-container{
                width: 100%;
                display: flex;
                align-items: center;
                overflow-x: scroll;
                flex-wrap: nowrap;
                position: relative;
                .ljt{
                    position: absolute;
                    width: 32px;
                    height: 32px;
                    top: 50%;
                    transform: translate(0, -50%);
                    right: 48px;
                    z-index: 4;
                }
                .tab-item{
                    font-size: 28px;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #A7AEC3;
                    padding: 14px 28px;
                    white-space: nowrap;
                    position: relative;
                    &.tab-item1{
                        font-weight: 600;
                        color: #196F20;
                        position: relative;
                        &::after{
                            content: '';
                            display: block;
                            position: absolute;
                            left: 50%;
                            bottom: 0px;
                            transform: translate(-50%, 0);
                            width: 44px;
                            height: 8px;
                            background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
                            border-radius: 6px 6px 6px 6px;
                        }
                    }
                    span{
                        display: block;
                        width: 16px;
                        height: 16px;
                        background-color: #FD4232;
                        border-radius: 50%;
                        position: absolute;
                        top: 8px;
                        right: 14px;
                    }
                }
            }
            &::after{
                display: block;
                content: '';
                width: 10%;
                height: 100%;
                bottom: 0;
                background: linear-gradient(to left, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
                position: absolute;
                pointer-events: none;
                left: 0;
            }
            &::before{
                pointer-events: none;
                display: block;
                content: '';
                width: 10%;
                height: 100%;
                bottom: 0;
                background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
                position: absolute;
                right: 0;
            }
        }
    }
    .shopping-container{
        position: relative;
        z-index: 10;
        padding-top: 0;
        // overflow: initial;
    }
    .fade-bottom-enter-active {
        animation: slideContentUp 0.2s ease-out both;
    }
    .fade-bottom-leave-active {
        animation: slideContentDown 0.2s ease-in both;
    }
    @keyframes slideContentUp {
        from {
            height: 10px;
            opacity: 0;
        }

        to {
            height: 100%;
        }
    }
    @keyframes slideContentDown {
        from {
            height: 100%;
        }

        to {
            height: 10px;
            opacity: 0;
        }
    }
    .overLab {
        position: fixed;
        top: 280px;
        width: 100vw;
        height: 260px;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #68d685 100%);
    }

    // .red{
    //     position: relative;
    //     z-index: 999;
    //     &::after{
    //         content: '';
    //         display: block;
    //         position: absolute;
    //         width: 20px;
    //         height: 20px;
    //         background: #FD4232;
    //         top: -30px;
    //         right: -180px;
    //         border-radius: 50%;
    //     }
    // }

    .title-decorator {
        width: 498px;
        height: 200px;
        background: linear-gradient( 180deg, #53B6E0 0%, #80D0CB 39%, #CFFDA7 100%);
        border-radius: 24px 24px 24px 24px;
        border: 2px solid #4ECA54;
        padding: 6px 8px;
        top: 413px;
        z-index: 99;
        position: absolute;

        .inside {
            border-radius: 20px 20px 20px 20px;
            border: 2px solid #FFFFFF;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            .mask{
                position: absolute;
                left: 0;
                top: 0;
                right: 0;
                bottom: 0;
                z-index: 9;
            }
        }
    }
}