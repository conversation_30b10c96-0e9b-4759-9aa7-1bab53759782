<template>
    <div class="friend">
        <navComponent :returnType="2" :showTransmit="true"/>
        <div class="friendList">
            <div class="friend-title">
                <div class="title-top">
                    <img :src="$imgs['friend-top.png']">
                </div>
                <div class="title-btm">
                    <img :src="$imgs['left-yezi.png']">
                    <span>好友減碳值總榜</span>
                    <img :src="$imgs['right-yezi.png']">
                </div>
            </div>
            <friendDialog :friendSheetTab="friendSheetTab"/>
            <div class="bottom-tips">
                <span>到底啦</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import navComponent from '@/components/navComponent.vue';
import friendDialog from '@/components/friendDialog.vue';
</script>

<style scoped lang="less">
.friend{
    background: #F7F7F9;
    min-height:100vh
}
:deep(.nav){
    height:192px;
    background: linear-gradient(180deg, #D9F4E7 0%, #F7F7F9 100%);
    .transmit{
        display:none !important;
    }
}
:deep(.mgTitle),
:deep(.addfriend){
    display:none !important;
}
.friendList{
    margin:0 auto;
    width: 724px;
    background: #FFFFFF;
    border-radius: 48px 48px 48px 48px;
}
.bottom-tips{
    font-size: 24px;
    font-family: PingFang TC-Regular, PingFang TC;
    font-weight: 400;
    color: #A7AEC3;
    text-align:center;
    padding-bottom:72px;
}
.friend-title{
    display:flex;
    justify-content:center;
    align-items:center;
    flex-direction:column;
    .title-top{
        width:212px;
        height:14px;
        display:flex;
        margin-top:56px;
        img{
            width:100%;
            height:100%;
        }
    }
    .title-btm{
        display:flex;
        justify-content:center;
        span{
            font-size: 36px;
            font-family: PingFang HK-Semibold, PingFang HK;
            font-weight: 600;
            color: rgba(0,0,0,0.8);
            margin:0 16px;
        }
        img{
            width:40px;
            height:40px;
        }
    }
}
</style>