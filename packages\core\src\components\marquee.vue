<template>
    <div class="warper">
        <!-- <div class="down" @click="eventBus.emit('upAct')" v-if="data.length >1">
            <img :src="$imgs[`${up ? 'down1' : 'up' }.png`]" alt="">
        </div> -->
        <div id="show_swiper">
            <div class="container" :style="`
                transform: translate(0, -${(showBoxH * buffY).toFixed(2)}px); 
            `">
                <!-- 这里改了下面container_copy也要同步对应改 -->
                <div class="show_box" v-for="(item, index) in data" :key="index">
                    <div :class="`show_item ${index == (y + 1) % data.length ? 'show_item_choose' : ''}`">
                        <!-- 自己收好友 -->
                        <p v-if="item.energy && item.energy.linked_type == 1" @click="openFriendTab">
                            {{ state.friend.你共領取了 }}<span @click.stop="toFriendPage(item.energy.linked_friend.third_id)">【{{nameHandle(item.energy.linked_friend.name)}}】</span>{{item.energy.energy_value}}g >
                        </p>
                        <!-- 好友收自己 -->
                        <p v-if="item.energy && item.energy.linked_type == 2" @click="openFriendTab">
                            <span @click.stop="toFriendPage(item.energy.linked_friend.third_id)">【{{nameHandle(item.energy.linked_friend.name)}}】</span>{{ state.friend.收取你 }}{{item.energy.energy_value}}g <span v-if="lang == 'en'">{{ state.friend.收取你2 }}</span> >
                        </p>
                        <!-- 有人加好友 -->
                        <p v-if="item.notice" @click="openMyFriend">
                            <span>{{item.notice.phone.replace(new RegExp('(\\d{2})(\\d{4})(\\d+)', 'g'), '$1****$3')}}</span>{{ state.friend.想加你為好友 }}
                        </p>
                        <!-- 好友送花 -->
                        <p v-if="item.receive_flower" @click="openActSheet">
                            <span>{{item.receive_flower.name}}</span>{{ state.flower.送你一瓶 }} {{ treeName[item.receive_flower.flower_type] }}
                        </p>
                    </div>
                </div>
            </div>
            <!-- 锁住 -->
            <div class="container container_copy" v-show="buffY <= data.length && data.length >= 3" :style="`
                transform: translate(0, -${(showBoxH * buffY).toFixed(2)}px); 
            `">
                <div class="show_box" v-for="(item, index) in data.slice(0, 3)" :key="index">
                    <div :class="`show_item ${index == (y + 1) % data.length ? 'show_item_choose' : ''}`">
                        <!-- 自己收好友 -->
                        <p v-if="item.energy && item.energy.linked_type == 1" @click="openFriendTab">
                            {{ state.friend.你共領取了 }}<span @click.stop="toFriendPage(item.energy.linked_friend.third_id)">【{{nameHandle(item.energy.linked_friend.name)}}】</span>{{item.energy.energy_value}}g >
                        </p>
                        <!-- 好友收自己 -->
                        <p v-if="item.energy && item.energy.linked_type == 2" @click="openFriendTab">
                            <span @click.stop="toFriendPage(item.energy.linked_friend.third_id)">【{{nameHandle(item.energy.linked_friend.name)}}】</span>{{ state.friend.收取你 }}{{item.energy.energy_value}}g <span v-if="lang == 'en'">{{ state.friend.收取你2 }}</span> >
                        </p>
                        <!-- 有人加好友 -->
                        <p v-if="item.notice" @click="openMyFriend">
                            <span>{{item.notice.phone.replace(new RegExp('(\\d{2})(\\d{4})(\\d+)', 'g'), '$1****$3')}}</span>{{ state.friend.想加你為好友 }}
                        </p>
                        <!-- 好友送花 -->
                        <p v-if="item.receive_flower" @click="openActSheet">
                            <span>{{item.receive_flower.name}}</span>{{ state.flower.送你一瓶 }} {{ treeName[item.receive_flower.flower_type] }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang='ts'>
import { ref, onMounted, watch, onBeforeMount, getCurrentInstance, nextTick, onUnmounted,computed } from 'vue'
import { useTaskStore, useUserStore, useFriendStore, useTreeStore } from '@/store/index'
import { useLang, useEventBus, useRouter } from 'hook';
import { useEnvConfig } from 'hook';
import { logEventStatistics } from "@via/mylink-sdk";
const { state,lang } = useLang()
const friendStore = useFriendStore()
const { router, currentRoute } = useRouter()
let eventBus = useEventBus()
let y = ref(0)
let showBoxH = ref(0)
let buffY = ref(0)
let data = ref([])

let buffTimer:any = null

let treeName = ref({
    diaozhonghua:state.flower.吊鐘花,
    jieguojueming:state.flower.節果決明,
    lanhuaying:state.flower.藍花楹,
    xiuqiuhua:state.flower.繡球花,
    yangzijing:state.flower.洋紫荊
})
watch(y, () => {
    if(y.value > data.value.length){
        y.value = 0
        buffY.value = 0
    }
    clearInterval(buffTimer)
    if(buffY.value < y.value){
        buffTimer = setInterval(() => {
            buffY.value = buffY.value > y.value ? y.value : buffY.value + 0.025
            if(buffY.value > y.value){
                buffY.value = y.value
                if(y.value >= data.value.length){
                    y.value = 0
                    buffY.value = 0
                }
                clearInterval(buffTimer)
                return
            }
        }, 10)
    }
})

function nameHandle(name:string){
    if(name.length <= 8){
        return name
    }else{
        let str = name.replace(/^([^\o]{4})([^\o]+)([^\o])/g, '$1……$3')
        return str
    }
}

function openFriendTab(){
    logEventStatistics('garden_bar_total_collect_click')
    console.log("打开好友sheet")
    eventBus.emit('openFriendSheet', 1)
}

function toFriendPage(id){
    logEventStatistics('garden_bar_friends_name_click')
    if(!useTreeStore().hasPlanted){
        eventBus.emit('isPlant')
        return
    }
    console.log("跳转好友主页")
    eventBus.emit('closeFriendSheet')
    useFriendStore().changeIsOwn(false)
    router.push({
        path: "/home",
        query: {
            friendId: id,
            hideNavigationBar:'true'
        }
    })
}

function openMyFriend(){
    logEventStatistics('garden_bar_friends_invite_click')
    location.href = useEnvConfig().ADD_URL
}

function openActSheet() {
    eventBus.emit('openActSheetDig')
}

onMounted(async () => {
    await friendStore.friendMarquee()
    data.value = friendStore.marqueeList as any
    // 内地版删除送花提示
    if(useUserStore().isHK == 1){
        data.value = data.value.filter((item)=>{
            return item.receive_flower == null
        })
    }    
    await nextTick()
    let dom = document.querySelector('.show_box')
    if(dom){
        showBoxH.value = dom.getBoundingClientRect().height
        setInterval(() => {
            y.value += 1
        }, 1500)
    }
})

</script>

<style lang='less' scoped>
.warper{
    overflow: hidden;
}
.down{
    display: flex;
    width: 20px;
    height: 12px;
    margin: 10px auto;
    position: relative;
    img{
        position: absolute;
        z-index: 40;
        width: 30px;
        height: 18px;
    }
}
#show_swiper{
    height: 134px;
    padding-right: 30px;
    padding-top: 10px;
    border-radius: 0px 16px 16px 0px;
    // pointer-events: none;
    overflow: hidden;
    font-family: PingFang SC-Regular, PingFang SC;
    z-index: 40;
    .container{
        .show_box_hidden{
            position: absolute;
        }
        .show_box{
            padding-bottom: 14px;
            display: flex;
            flex-direction: column;
            .show_item{
                height: 32px;
                opacity: 0.7;
                background: #FFFFFF;
                border-radius: 0px 16px 16px 0px;
                transition: all 0.6s ease;
                display: flex;
                align-self: baseline;/* align-self 属性定义flex子项单独在侧轴（纵轴）方向上的对齐方式。align-self 属性可重写灵活容器的 align-items 属性。 */
                flex-direction: column;
                justify-content: center;
                padding-left: 12px;
                padding-right: 15px;
                >p{
                    font-size: 20px;
                    font-weight: 400;
                    color: #3CBF48;
                    span{
                        color: #4E5B7E;
                    }
                }
                &.show_item_choose{
                    transform: scale(1.14);
                    transform-origin: center left;
                    background: #FFFFFF;
                    border-radius: 0px 16px 16px 0px;
                    opacity: 1;
                }
            }
            .change-opacity{
                opacity: 1;
            }
        }
    }
}
.change-height{
    height: 134px !important;
}
</style>