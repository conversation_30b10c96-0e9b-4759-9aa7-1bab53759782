import scanner from 'sonarqube-scanner'
import packageJson from '../package.json'
import { useEnvConfig } from '../config/useEnvConfig'
const envConfig = useEnvConfig()
const version = envConfig.version

scanner(
    {
        serverUrl: 'https://sonar.via.cool',
        token: '****************************************',
        options: {
            'sonar.projectVersion': version,
            'sonar.branch.name': `release/uat`,
            // 'sonar.branch.name': `${envConfig.RUN_ENV}`,
            'sonar.projectKey': 'mylink-forest',
            'sonar.projectName': 'mylink-forest',
            'sonar.projectDescription': packageJson.description,
            'sonar.sources': 'src',
            'sonar.host.url': 'https://sonar.via.cool',
            'sonar.login': '****************************************'
        }
    },
    (error) => {
        if (error) {
            console.error(error)
        }
        process.exit()
    }
)
