[v-cloak] {
    display: none;
}

.home {
    width: 100vw;
    min-height: 100vh;
    position: relative;
    font-family: PingFang TC-Medium, PingFang TC;
    font-weight: 500;
    .no-operate {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 99;
        right: 0;
    }
    .all-hk {
        top: 380px !important;
    }
    .all {
        padding: 0 20px;
        height: 52px;
        background: #ffffff;
        border-radius: 28px 28px 28px 28px;
        position: absolute;
        top: 432px;
        left: (50 / 750 * 100vw);
        z-index: 2;
        display: flex;
        justify-content: center;
        align-items: center;
        .all-text {
            font-size: 20px;
            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
            font-weight: 500;
            color: #2a803d;
            span {
                display: flex;
                transform: scale(0.9);
            }
        }
        .all-btn {
            margin-left: -8px;
            height: 36px;
            background: #fdd83b;
            border-radius: 18px 18px 18px 18px;
            display: flex;
            position: relative;
            justify-content: center;
            span {
                transform: scale(0.8);
                font-size: 20px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #95510d;
            }
        }
        .all-btn::after {
            position: absolute;
            content: '';
            clear: both;
            display: block;
            width: 16px;
            height: 16px;
            background: #fd4232;
            right: -2px;
            top: -2px;
            border-radius: 50%;
        }
    }
    .all::after {
        content: '';
        clear: both;
        display: block;
        width: 30px;
        height: 20px;
        top: 46px;
        left: 56px;
        position: absolute;
        background: url('@assets/imgs/all-jt.png') no-repeat;
        background-size: cover;
    }
    .water {
        position: absolute;
        z-index: 99;
        top: 476px;
        left: calc(50vw - 120px);
        width: 240px;
        height: 320px;
    }
    .marqueeBox {
        position: absolute;
        bottom: 602px;
        z-index: 2;
        left: 0;
        border-radius: 0px 16px 16px 0px;
    }
    .findnextfrd {
        position: absolute;
        right: 0;
        bottom: 290px;
        z-index: 35;
        img {
            height: 68px;
        }
    }
    .findnextfrdTop {
        bottom: 400px;
    }
    .friendAchieve {
        position: absolute;
        bottom: 28px;
        left: 50%;
        transform: translate(-50%, 0);
        width: 650px;
        height: 296px;
        background: url('@assets/imgs/friendComplete.png') no-repeat;
        background-size: cover;
        .friendComplete {
            width: 100%;
            height: 100%;
            position: absolute;
        }
        .achContainer {
            display: flex;
            margin-top: 36px;
            margin-left: 38px;
            width: 610px;
            justify-content: space-around;
            flex-wrap: wrap;
            height: 200px;
            overflow-y: scroll;
        }
        .tip {
            font-size: 28px;
            font-weight: 600;
            color: #ffffff;
            margin-left: 92px;
            margin-top: 10px;
        }
    }
    #gif {
        position: absolute;
        top: 0;
        right: 0;
        // background-image: url('@imgs/baiyun.png');
        // background-repeat: no-repeat;
        // background-size: 4500px 10500px;
        width: 750px;
        height: 750px;
        overflow: hidden;
        z-index: 1;
        // background-position: 0px 0px;
        // animation: myAnimation 7s steps(1) infinite;
        > img {
            width: 4500px;
            height: 10500px;
            position: absolute;
            left: 0;
            top: 0;
            animation: myAnimation 7s steps(1) infinite;
        }
    }
    @keyframes myAnimation {
        0% {
            left: 0px;
            top: 0px;
        }
        1.20% {
            left: -750px;
            top: 0px;
        }
        2.41% {
            left: -1500px;
            top: 0px;
        }
        3.61% {
            left: -2250px;
            top: 0px;
        }
        4.82% {
            left: -3000px;
            top: 0px;
        }
        6.02% {
            left: -3750px;
            top: 0px;
        }
        7.23% {
            left: 0px;
            top: -750px;
        }
        8.43% {
            left: -750px;
            top: -750px;
        }
        9.64% {
            left: -1500px;
            top: -750px;
        }
        10.84% {
            left: -2250px;
            top: -750px;
        }
        12.05% {
            left: -3000px;
            top: -750px;
        }
        13.25% {
            left: -3750px;
            top: -750px;
        }
        14.46% {
            left: 0px;
            top: -1500px;
        }
        15.66% {
            left: -750px;
            top: -1500px;
        }
        16.87% {
            left: -1500px;
            top: -1500px;
        }
        18.07% {
            left: -2250px;
            top: -1500px;
        }
        19.28% {
            left: -3000px;
            top: -1500px;
        }
        20.48% {
            left: -3750px;
            top: -1500px;
        }
        21.69% {
            left: 0px;
            top: -2250px;
        }
        22.89% {
            left: -750px;
            top: -2250px;
        }
        24.10% {
            left: -1500px;
            top: -2250px;
        }
        25.30% {
            left: -2250px;
            top: -2250px;
        }
        26.51% {
            left: -3000px;
            top: -2250px;
        }
        27.71% {
            left: -3750px;
            top: -2250px;
        }
        28.92% {
            left: 0px;
            top: -3000px;
        }
        30.12% {
            left: -750px;
            top: -3000px;
        }
        31.33% {
            left: -1500px;
            top: -3000px;
        }
        32.53% {
            left: -2250px;
            top: -3000px;
        }
        33.73% {
            left: -3000px;
            top: -3000px;
        }
        34.94% {
            left: -3750px;
            top: -3000px;
        }
        36.14% {
            left: 0px;
            top: -3750px;
        }
        37.35% {
            left: -750px;
            top: -3750px;
        }
        38.55% {
            left: -1500px;
            top: -3750px;
        }
        39.76% {
            left: -2250px;
            top: -3750px;
        }
        40.96% {
            left: -3000px;
            top: -3750px;
        }
        42.17% {
            left: -3750px;
            top: -3750px;
        }
        43.37% {
            left: 0px;
            top: -4500px;
        }
        44.58% {
            left: -750px;
            top: -4500px;
        }
        45.78% {
            left: -1500px;
            top: -4500px;
        }
        46.99% {
            left: -2250px;
            top: -4500px;
        }
        48.19% {
            left: -3000px;
            top: -4500px;
        }
        49.40% {
            left: -3750px;
            top: -4500px;
        }
        50.60% {
            left: 0px;
            top: -5250px;
        }
        51.81% {
            left: -750px;
            top: -5250px;
        }
        53.01% {
            left: -1500px;
            top: -5250px;
        }
        54.22% {
            left: -2250px;
            top: -5250px;
        }
        55.42% {
            left: -3000px;
            top: -5250px;
        }
        56.63% {
            left: -3750px;
            top: -5250px;
        }
        57.83% {
            left: 0px;
            top: -6000px;
        }
        59.04% {
            left: -750px;
            top: -6000px;
        }
        60.24% {
            left: -1500px;
            top: -6000px;
        }
        61.45% {
            left: -2250px;
            top: -6000px;
        }
        62.65% {
            left: -3000px;
            top: -6000px;
        }
        63.86% {
            left: -3750px;
            top: -6000px;
        }
        65.06% {
            left: 0px;
            top: -6750px;
        }
        66.27% {
            left: -750px;
            top: -6750px;
        }
        67.47% {
            left: -1500px;
            top: -6750px;
        }
        68.67% {
            left: -2250px;
            top: -6750px;
        }
        69.88% {
            left: -3000px;
            top: -6750px;
        }
        71.08% {
            left: -3750px;
            top: -6750px;
        }
        72.29% {
            left: 0px;
            top: -7500px;
        }
        73.49% {
            left: -750px;
            top: -7500px;
        }
        74.70% {
            left: -1500px;
            top: -7500px;
        }
        75.90% {
            left: -2250px;
            top: -7500px;
        }
        77.11% {
            left: -3000px;
            top: -7500px;
        }
        78.31% {
            left: -3750px;
            top: -7500px;
        }
        79.52% {
            left: 0px;
            top: -8250px;
        }
        80.72% {
            left: -750px;
            top: -8250px;
        }
        81.93% {
            left: -1500px;
            top: -8250px;
        }
        83.13% {
            left: -2250px;
            top: -8250px;
        }
        84.34% {
            left: -3000px;
            top: -8250px;
        }
        85.54% {
            left: -3750px;
            top: -8250px;
        }
        86.75% {
            left: 0px;
            top: -9000px;
        }
        87.95% {
            left: -750px;
            top: -9000px;
        }
        89.16% {
            left: -1500px;
            top: -9000px;
        }
        90.36% {
            left: -2250px;
            top: -9000px;
        }
        91.57% {
            left: -3000px;
            top: -9000px;
        }
        92.77% {
            left: -3750px;
            top: -9000px;
        }
        93.98% {
            left: 0px;
            top: -9750px;
        }
        95.18% {
            left: -750px;
            top: -9750px;
        }
        96.39% {
            left: -1500px;
            top: -9750px;
        }
        97.59% {
            left: -2250px;
            top: -9750px;
        }
        98.80% {
            left: -3000px;
            top: -9750px;
        }
        100.00% {
            left: -3750px;
            top: -9750px;
        }
    }
    .buttonon {
        position: absolute;
        top: 0px;
        z-index: 9998;
        left: 0px;
    }
    .guideContainer {
        position: absolute;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        transition: all 0.6s ease;
        .stepInfo {
            max-width: 550px;
            text-align: center;
            position: absolute;
            padding: 36px 22px;
            background: #ffffff;
            border-radius: 28px;
            font-size: 32px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 500;
            color: #1b1b1b;
            display: none;
        }
        .stepInfo::after {
            content: '';
            display: block;
            position: absolute;
            width: 38px;
            height: 20px;
            background: url('@imgs/jianjian.png') no-repeat;
            background-size: 100% 100%;
            transform-origin: center center;
        }
        .ld::after {
            top: -18px;
            left: 50px;
            transform: rotate(180deg);
        }
        .lt::after {
            bottom: -18px;
            left: 50px;
        }
        .rt::after {
            bottom: -18px;
            right: 50px;
        }
        .rd::after {
            top: -18px;
            right: 50px;
            transform: rotate(180deg);
        }
        .guideBox {
            position: absolute;
            transition: all 0.3s;
            border-radius: 24px;
        }
        .guideBox1 {
            box-shadow: rgb(33 33 33 / 50%) 0px 0px 0px 2000px;
        }
        .lastStep {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 522px;
            height: 770px;
            background: url('@imgs/last-step.png') no-repeat;
            background-size: 100% 100%;
            animation: fdsseq1 0.3s ease;
            p {
                white-space: pre-line;
                width: 450px;
                height: 129px;
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                font-size: 32px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
        @keyframes fdsseq1 {
            from {
                transform: translate(-50%, -50%) scale(0);
            }
            to {
                transform: translate(-50%, -50%) scale(1);
            }
        }
        .guideH5 {
            position: absolute;
            width: 100%;
            height: 100%;
            .tiaoguo {
                position: absolute;
                right: 44px;
                top: 100px;
                padding: 6px 44px;
                border-radius: 46px 46px 46px 46px;
                border: 2px solid #f0f0f0;
                font-size: 36px;
                font-family: PingFang SC-Bold, PingFang SC, Arial, Helvetica, sans-serif;
                font-weight: 500;
                color: #ffffff;
            }
        }
    }
    .newImgtc {
        background: url('@assets/imgs/新手/stepPage_tc.png') no-repeat;
        background-size: cover;
    }
    .newImgsc {
        background: url('@assets/imgs/新手/stepPage_sc.png') no-repeat;
        background-size: cover;
    }
    .newImgen {
        background: url('@assets/imgs/新手/stepPage_en.png') no-repeat;
        background-size: cover;
    }
    .NFTtc {
        background: url('@assets/imgs/新手/NFT_tc.png') no-repeat;
        background-size: cover;
    }
    .NFTsc {
        background: url('@assets/imgs/新手/NFT_sc.png') no-repeat;
        background-size: cover;
    }
    .NFTen {
        background: url('@assets/imgs/新手/NFT_en.png') no-repeat;
        background-size: cover;
    }
    .background {
        position: absolute;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        .appear {
            animation: appear 10s;
            animation-fill-mode: forwards;
        }
        .vanish {
            animation: vanish 10s;
            animation-fill-mode: forwards;
        }
        @keyframes appear {
            0% {
                opacity: 0;
            }
            100% {
                opacity: 1;
            }
        }
        @keyframes vanish {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .bg-sky {
            position: relative;
            bottom: -1px;
            flex-shrink: 1;
            bottom: -1px;
            width: 100%;
            // 折叠屏
            // min-height: 950px;
            flex: 1;
            object-fit: cover;
            background: url('@assets/TT/background.png') no-repeat;
            background-position: bottom;
            background-size: cover; //修改特殊机型白边不匹配

            .treeType {
                position: absolute;
                left: 50px;
                top: 308px;
                z-index: 2;
                display: flex;
                flex-direction: column;
                align-items: start;
                .type {
                    display: flex;
                    align-items: center;

                    .type1 {
                        .typeLimit {
                            width: 112px;
                            height: 48px;
                            background: linear-gradient(92deg, #ffe1bf 0%, #dbaa69 100%);
                            border-radius: 10px 4px 4px 10px;
                            opacity: 1;
                            font-size: 22px;
                            font-family: PingFang SC, PingFang SC;
                            font-weight: 400;
                            color: #965f12;
                            line-height: 48px;
                            display: flex;
                            justify-content: center;
                            margin-right: 12px;
                        }
                        // min-width: 360px;
                        max-width: 485px;
                        display: flex;
                        align-items: center;
                        height: 52px;
                        box-sizing: border-box;
                        background: rgba(255, 255, 255, 0.5);
                        box-shadow: inset 0px -8px 0px 2px rgba(151, 216, 235, 1);
                        border: 2px solid #ffffff;
                        border-radius: 10px;
                        padding: 0 14px 0 2px;
                        .vipzs {
                            // flex-shrink: 0;
                            // display: inline-block;
                            // min-width: 104px;
                            // height: 48px;
                            padding: 8px 10px;
                            background: linear-gradient(180deg, #c18b6f 0%, #885a42 100%);
                            border-radius: 10px 4px 4px 10px;
                            opacity: 0.85;
                            font-size: 22px;
                            font-family: PingFang SC-Regular, PingFang SC;
                            font-weight: 400;
                            color: #ffffff;
                            white-space: nowrap;
                            margin-right: 10px;
                        }
                        .miao {
                            flex-shrink: 0;
                            margin-left: 20px;
                            margin-right: 20px;
                            width: 40px;
                            height: 40px;
                            background: url('@assets/TT/Icon/sapling.png') no-repeat;
                            background-size: 100% auto;
                        }
                        span:nth-child(2) {
                            // flex-shrink: 0;
                            margin-left: 0px;
                        }
                        p {
                            // display: inline-block;
                            font-size: 24px;
                            color: #4e5b7e;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                        .planting {
                            flex: 1;
                        }
                    }
                }

                .loginDay {
                    width: auto;
                    .toptx {
                        font-size: 28px;
                        font-weight: 600;
                        color: #4e5b7e;
                        :deep(span) {
                            font-style: italic;
                            font-size: 64px;
                            font-family: Arial-Bold Italic, Arial;
                            font-weight: normal;
                            color: #fa281c;
                        }
                    }
                    .bottomtx {
                        max-width: 550px;
                        font-size: 22px;
                        font-weight: 400;
                        color: #4e5b7e;
                    }
                }
                .usingCard {
                    // overflow: hidden;
                    margin-top: 7px;
                    height: 30px;
                    transition: all 0.3s ease-in-out;
                    width: 600px;
                    .marquee {
                        position: relative;
                    }
                    .bottomtx {
                        color: #4e5b7e;
                        display: flex;
                        justify-content: start;
                        align-items: center;
                        font-size: 24px;
                        .icon {
                            width: 24px;
                            height: 24px;
                            margin-right: 10px;
                            img {
                                width: 100%;
                                height: 100%;
                            }
                        }
                        img{
                            width: 28px;
                            height: 28px;
                            margin-right: 4px;
                        }
                    }
                }
                .usingCardEn {
                    height: 90px;
                    .bottomBox {
                        height: 90px;
                    }
                }
                .getAllBall {
                    display: flex;
                    padding: 5px 10px;
                    background-color: wheat;
                    border-radius: 20px;
                    font-size: 24px;
                    flex-shrink: 0;
                    &.show {
                        animation: showDivAni 0.3s forwards;
                    }
                    &.hide {
                        animation: hideDivAni 0.3s forwards;
                    }
                    @keyframes showDivAni {
                        0% {
                            opacity: 0;
                        }
                        100% {
                            display: flex;
                            opacity: 1;
                        }
                    }
                    @keyframes hideDivAni {
                        0% {
                            opacity: 1;
                        }
                        100% {
                            display: none;
                            opacity: 0;
                        }
                    }
                }
            }
            .treeType1 {
                width: 100%;
                height: 52px;
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                z-index: 9;
            }
            .waterIcon {
                width: 100px;
                height: 100px;
                position: absolute;
                animation: fdsseq 0.5s forwards, fudong 6.5s ease 0.5s infinite;
                left: 60%;
                bottom: -12px;
                z-index: 99;
            }
            .treeBox {
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                bottom: 30px;
                z-index: 1;
                .nitu {
                    position: absolute;
                    bottom: -10px;
                    left: 50%;
                    width: 81px;
                    transform: translate(-50%, 0);
                }
                .yinying {
                    position: absolute;
                    bottom: -8px;
                    left: 50%;
                    transform: translate(-50%, 0);
                    width: 124px;
                }
                .xiuqiu {
                    bottom: -12px;
                    width: 144px;
                }
                .treeEntity {
                    transform-origin: center bottom;
                }
                .shake {
                    animation: shake 1s ease;
                }
            }

            @keyframes shake {
                0% {
                    transform: scaleY(1);
                }

                35% {
                    transform: scaleY(0.82);
                }

                60% {
                    transform: scaleY(1.07);
                }

                80% {
                    transform: scaleY(0.97);
                }

                100% {
                    transform: scaleY(1);
                }
            }
            .null {
                position: absolute;
                bottom: 0px;
                left: 50%;
                width: 156px;
                transform: translate(-50%, 0);
                img {
                    width: 100%;
                    height: 100%;
                }
            }
            .treeTalk {
                pointer-events: none;
                position: absolute;
                bottom: 190px;
                left: 50%;
                width: 156px;
                transform: translate(-50%, 0);
                width: 320px;
                min-height: 96px;
                padding: 0 28px;
                box-sizing: border-box;
                background: linear-gradient(
                    180deg,
                    #ffffff 0%,
                    rgba(218, 244, 206, 0.87) 32%,
                    rgba(255, 255, 255, 0.06) 100%
                );
                border-radius: 16px 16px 16px 16px;
                border: 2px solid rgba(255, 255, 255, 0.5);
                font-size: 22px;
                font-weight: 400;
                color: #22992c;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 21;
            }

            .mountain {
                position: absolute;
                left: 0;
                bottom: 122px;
                width: 270px;
                height: 210px;
                background: url('@assets/TT/mountain.png') no-repeat;
                background-size: 100% auto;
            }
            .house {
                position: absolute;
                right: 0;
                bottom: 110px;
                width: 220px;
                height: 158px;
                background: url('@assets/TT/house.png') no-repeat;
                background-size: 100% auto;
                .house-tips{
                    width: 164px;
                    height: 34px;
                    background: url('@/assets/imgs/homepoints.png') no-repeat;
                    background-size: cover;
                    position: absolute;
                    top: -14px;
                    left: 44px;
                    font-size: 16px;
                    color: #FFFFFF;
                    line-height: 24px;
                    text-align: center;
                }
            }
            .bubBalls1 {
                .ball1 {
                    background: url('@assets/imgs/bub1.png') no-repeat;
                    background-size: 100% 100%;
                    width: 98px;
                    height: 98px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    font-size: 28px;
                    font-family: Arial-Bold Italic, Arial;
                    font-weight: 600;
                    color: #22992c;
                    font-style: italic;
                    img {
                        width: 30px;
                        height: 30px;
                        margin-bottom: 5px;
                    }
                }
                .tran {
                    opacity: 0.5;
                }
                .addEnergy {
                    font-size: 18px;
                    color: #2a803d;
                    position: absolute;
                    left: 50%;
                    top: -35px;
                    transform: translate(-50%, 0);
                }
                .move1 .addEnergy {
                    animation: mymove1 0.6s forwards;
                }
                @keyframes mymove1 {
                    0% {
                        opacity: 1;
                        transform: translate(-50%, 0);
                    }
                    100% {
                        opacity: 0;
                        transform: translate(-50%, -30px);
                    }
                }
            }
            .bubBalls {
                z-index: 42;
                position: relative;
                // .ani1{
                //     animation:fudong 6s infinite;
                // }
                // .ani2{
                //     animation:fudong 6.5s infinite;
                // }
                // .ani3{
                //     animation:fudong 6.25s infinite;
                // }
                .bub {
                    animation: fdsseq 0.5s forwards, fudong 6s ease 0.5s infinite;
                    position: absolute;
                    left: 60px;
                    top: 670px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    // animation:fudong 4s infinite;
                    .expire_time {
                        width: 200px;
                        // background-color: pink;
                        bottom: 100px;
                        position: absolute;
                        white-space: nowrap;
                        text-align: center;
                        // font-size: 28px;
                        font-size: 24px;
                        color: #2a803d;
                        line-height: 28px;
                        white-space: pre-line;
                    }
                    .ball {
                        width: 98px;
                        height: 98px;
                        background: url('@assets/TT/Icon/bub.png') no-repeat;
                        background-size: 100% 100%;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        font-size: 28px;
                        font-family: Arial-Bold Italic, Arial;
                        font-weight: 600;
                        color: #22992c;
                        font-style: italic;
                        img {
                            width: 30px;
                            height: 30px;
                            margin-bottom: 5px;
                        }
                    }
                    .text {
                        margin-top: 9px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 145px;
                        position: absolute;
                        top: 95px;
                        text-align: center;
                        font-size: 25px;
                        color: #2a803d;
                        line-height: 28px;
                        white-space: pre-line;
                        // word-break: break-all;
                        .xiushou {
                            display: inline-block;
                            width: 20px;
                            height: 20px;
                            background: url('@assets/imgs/xiushou.png') no-repeat;
                            background-size: 100% 100%;
                        }
                    }
                }
                .bub1 {
                    left: (160 / 750 * 100vw);
                    top: 530px;
                    animation: fdsseq 0.5s forwards, fudong 6.5s ease 0.5s infinite;
                }
                .bub2 {
                    left: (320 / 750 * 100vw);
                    top: 480px;
                    animation: fdsseq 0.5s forwards, fudong 6.25s ease 0.5s infinite;
                }
                .bub3 {
                    left: (480 / 750 * 100vw);
                    top: 530px;
                    animation: fdsseq 0.5s forwards, fudong 6s ease 0.5s infinite;
                }
                .bub4 {
                    left: (580 / 750 * 100vw);
                    top: 670px;
                    animation: fdsseq 0.5s forwards, fudong 6.5s ease 0.5s infinite;
                }
                .move {
                    animation: mymove 1s;
                    animation-fill-mode: forwards;
                }
                @keyframes fudong {
                    0% {
                        transform: translate(0, 0px);
                    }
                    50% {
                        transform: translate(0, -30px);
                    }
                    100% {
                        transform: translate(0, 0px);
                    }
                }
                @keyframes mymove {
                    to {
                        transform: scale(0);
                        top: 195px;
                        left: 50px;
                    }
                }
                @keyframes fdsseq {
                    from {
                        transform: scale(0);
                    }
                    to {
                        transform: scale(1);
                    }
                }
            }
        }
        .bottomBox {
            width: 100%;
            background: linear-gradient(180deg, #aef091 0%, #23825b 100%);
            position: relative;
            min-height: 600px;
            .addHp {
                position: absolute;
                left: 280px;
                top: 30px;
                z-index: 9;
                font-size: 30px;
                font-weight: 550;
                color: #fb7655;
                display: none;
            }
            .addFlo {
                display: block;
                animation: float 2.5s forwards;
            }
            @keyframes float {
                from {
                    transform: translate(0, 0) scale(1);
                    opacity: 1;
                }
                to {
                    transform: translate(0, -50px) scale(0.9);
                    opacity: 0;
                }
            }
            img {
                position: absolute;
                bottom: 0;
            }
            .bear {
                position: absolute;
                right: 60px;
                top: -135px;
                width: 180px;
                height: 180px;
                z-index: 32;
                .bearTalk {
                    z-index: 32;
                    width: 312px;
                    min-height: 96px;
                    padding: 8px 20px;
                    box-sizing: border-box;
                    background: linear-gradient(
                        180deg,
                        #ffffff 0%,
                        rgba(218, 244, 206, 0.87) 32%,
                        rgba(255, 255, 255, 0.06) 100%
                    );
                    border-radius: 16px 16px 16px 16px;
                    border: 2px solid rgba(255, 255, 255, 0.5);
                    position: absolute;
                    bottom: 150px;
                    right: 50px;
                    pointer-events: none;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    p {
                        font-size: 22px;
                        font-weight: 400;
                        color: #22992c;
                    }
                    .bbt {
                        background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
                        box-shadow: inset 0px 4px 0px 2px rgba(255, 242, 178, 1);
                        border-radius: 20px 20px 20px 20px;
                        padding: 2px 36px;
                        font-size: 24px;
                        font-family: PingFang SC-Bold, PingFang SC;
                        font-weight: bold;
                        color: #ffffff;
                        white-space: nowrap;
                        pointer-events: all;
                    }
                }
                img {
                    width: 100%;
                    margin-bottom: 12px;
                }
            }
            .mygameBox {
                width: 124px;
                position: absolute;
                top: 65px;
                left: 5%;
                img {
                    width: 124px;
                }
                .points{
                    position: absolute;
                    top: -154px;
                    left: -10px;
                    width: 134px;
                }
            }
            .ceng {
                z-index: 36;
            }
            .myEquip {
                z-index: 25;
                position: absolute;
                left: 50px;
                top: 220px;
                display: flex;

                .equip {
                    .new {
                        position: absolute;
                        background: #e03131;
                        right: 4px;
                        top: 4px;
                        font-size: 14px;
                        font-family: PingFang HK-Semibold, PingFang HK;
                        font-weight: 600;
                        color: #ffffff;
                        border-radius: 65px;
                        padding: 0 5px;
                        z-index: 9;
                    }
                    .state {
                        width: 64px;
                        height: 18px;
                        position: absolute;
                        right: 4px;
                        top: 4px;
                    }
                    flex-shrink: 0;
                    position: relative;
                    height: 80px;
                    margin-right: 15px;
                    display: flex;
                    > img {
                        display: inline-block;
                        position: relative;
                        height: 80px;
                    }
                    .num {
                        display: block;
                        position: absolute;
                        right: -3px;
                        top: 0px;
                        width: 20px;
                        height: 20px;
                        background: #f05548;
                        border-radius: 50%;
                    }

                    .prompt {
                        position: absolute;
                        top: 110px;
                        left: -130px;
                        width: 310px;
                        height: 46px;
                        background: #4f8446;
                        border-radius: 12px 12px 12px 12px;
                        opacity: 1;
                        z-index: 99;
                        .promptText {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 46px;
                            width: 100%;
                            img {
                                position: unset;
                                width: 32px;
                                height: 24px;
                                margin: 0px 5px 0px 8px;
                            }
                            .promptitle {
                                font-size: 20px;
                                font-family: PingFang SC, PingFang SC;
                                color: #fff;
                                line-height: 20px;
                            }
                        }
                        .triangle {
                            width: 22px;
                            height: 16px;
                            position: absolute;
                            top: -14px;
                            right: 115px;
                        }
                    }
                }
            }
            .equipPlated {
                top: 100px !important;
            }
            .treeCard1 {
                position: absolute;
                bottom: 50px;
            }
            .progressBox {
                position: absolute;
                bottom: 70%;
            }
            .treeCard {
                position: absolute;
                bottom: 66px;
                left: 50%;
                transform: translate(-50%, 0);
                width: 650px;
                height: 258px;
                background: #ffffff;
                box-shadow: inset 0px -6px 0px 2px rgba(109, 198, 159, 1);
                border-radius: 48px 16px 48px 16px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .tip {
                    font-size: 24px;
                    font-weight: 400;
                    color: #4e5b7e;
                    margin-bottom: 40px;
                    margin-top: 40px;
                    white-space: pre-line;
                    text-align: center;
                }
                .btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-width: 360px;
                    height: 76px;
                    background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
                    box-shadow: 0px 4px 0px 2px rgba(252, 175, 40, 1), inset 0px 2px 0px 2px rgba(255, 242, 178, 1);
                    border-radius: 36px 12px 36px 12px;
                    font-size: 32px;
                    font-weight: 600;
                    color: #ffffff;
                }
                .two-btn {
                    display: flex;
                    width: 100%;
                    align-items: center;
                    justify-content: space-around;
                    .btn {
                        min-width: 288px;
                    }
                    .l {
                        background: linear-gradient(360deg, #3cbf48 0%, #85e277 100%);
                        box-shadow: 0px 4px 0px 2px rgba(56, 178, 60, 1), inset 0px 2px 0px 2px rgba(146, 232, 152, 1);
                    }
                }
            }
        }
        .taskCarousel {
            position: absolute;
            bottom: 100px;
            width: 100%;
        }
    }
    .rightIcons {
        position: absolute;
        right: 30px;
        top: 188px;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 2;
        .topIcons {
            margin-bottom: 20px;
            display: flex;
        }
        .ach {
            width: 80px;
            height: 80px;
            margin-right: 8px;
            img {
                width: 100%;
            }
            .num {
                display: block;
                position: absolute;
                right: 92px;
                top: 0px;
                width: 20px;
                height: 20px;
                background: #f05548;
                border-radius: 50%;
            }
            .bubble-text {
                position: absolute;
                background: #ffffff;
                border-radius: 28px 28px 28px 28px;
                top: 96px;
                left: -192px;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 10px 20px;
                max-width: 250px;
                min-width: 115px;
                z-index: 32;
                opacity: 0.9;
                span {
                    font-size: 20px;
                    text-align: center;
                    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
                    font-weight: 500;
                    color: #4e5b7e;
                    line-height: 30px;
                }
            }
            .bubble-text::after {
                position: absolute;
                left: 200px;
                top: -16px;
                width: 30px;
                height: 20px;
                content: '';
                clear: both;
                display: block;
                background: url('@assets/imgs/all-jt.png') no-repeat;
                background-size: cover;
                transform: rotate(180deg);
            }
            .bubble-text_en {
                max-width: 300px !important;
                left: -252px !important;
            }
            .bubble-text_en::after {
                left: 260px !important;
            }
        }
        .rule {
            width: 80px;
            height: 80px;
            img {
                width: 100%;
            }
            // background: linear-gradient(360deg, #3CBF48 0%, #3DD13F 100%);
            // box-shadow: 0px 4px 0px 2px rgba(56,178,60,1), inset 0px 2px 0px 2px rgba(146,232,152,1);
            // border-radius: 24px 24px 24px 24px;
            // padding: 8px 18px;
            // display: flex;
            // justify-content: center;
            // align-items: center;
            // p{
            //     span{
            //         display: inline-block;
            //         margin-right: 8px;
            //         width: 18px;
            //         height: 20px;
            //         background: url('@imgs/rule-icon.png') no-repeat;
            //         background-size: 100% 100%;
            //     }
            //     transform: translate(0 ,3px);
            //     font-size: 20px;
            //     font-family: PingFang SC-Medium, PingFang SC;
            //     font-weight: 500;
            //     color: #FFFFFF;
            // }
        }
        .myMall {
            position: relative;
            width: 85px;
            height: 85px;
            border-radius: 50%;
            margin-bottom: 30px;
            background: url('@assets/TT/myMall.png') no-repeat;
            background-size: 100% 100%;
            box-shadow: 0px 0px 24px 2px rgba(0, 0, 0, 0.08);
            .promptEn {
                width: 410px !important;
                left: -250px !important;
                .triangle {
                    right: 105px !important;
                }
            }
            .prompt {
                position: absolute;
                top: 110px;
                left: -160px;
                width: 310px;
                height: 46px;
                background: #4f8446;
                border-radius: 12px 12px 12px 12px;
                opacity: 1;
                z-index: 99;
                .promptText {
                    display: flex;
                    align-items: center;
                    height: 46px;
                    width: 100%;
                    height: 100%;
                    margin-bottom: 7px;
                    img {
                        width: 32px;
                        height: 24px;
                        margin: 13px 5px 10px 8px;
                    }
                    .promptitle {
                        font-size: 20px;
                        font-family: PingFang SC, PingFang SC;
                        font-weight: 500;
                        background: none;
                        color: #fff;
                    }
                }
                .triangle {
                    width: 22px;
                    height: 16px;
                    position: absolute;
                    top: -14px;
                    right: 95px;
                }
            }
            p {
                position: absolute;
                bottom: -20px;
                left: 50%;
                transform: translate(-50%, 0);
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 120px;
                white-space: nowrap;
                padding: 6px 20px;

                // height: 32px;
                background: #ffffff;
                box-shadow: 0px 0px 16px 2px rgba(0, 0, 0, 0.16), inset 0px -2px 0px 2px rgba(0, 0, 0, 0.16);
                border-radius: 20px 20px 20px 20px;
                font-size: 20px;
                color: #4e5b7e;
            }
            .new {
                position: absolute;
                background: #e03131;
                right: 4px;
                top: 4px;
                font-size: 14px;
                font-family: PingFang HK-Semibold, PingFang HK;
                font-weight: 600;
                color: #ffffff;
                border-radius: 65px;
                padding: 0 5px;
            }
        }
        .navv {
            position: relative;
            width: 85px;
            height: 85px;
            border-radius: 50%;
            background: url('@assets/TT/myhome.png') no-repeat;
            background-size: 100% 100%;
            box-shadow: 0px 0px 24px 2px rgba(0, 0, 0, 0.08);
            .num {
                position: absolute;
                width: 16px;
                height: 16px;
                background: #fd4232;
                border-radius: 50%;
                right: 4px;
                top: 4px;
            }
            .content {
                position: absolute;
                width: 150px;
                background: #ffffff;
                border-radius: 16px;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 7px 12px;
                top: 117px;
                left: -33px;
                span {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 12px;
                    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
                    font-weight: 500;
                    color: #4e5b7e;
                }
            }
            .content::after {
                width: 18px;
                height: 12px;
                content: '';
                clear: both;
                display: block;
                background: url('@assets/imgs/all-jt.png') no-repeat;
                background-size: cover;
                position: absolute;
                top: -12px;
                transform: rotate(-180deg);
            }
            .new {
                position: absolute;
                background: #e03131;
                right: 4px;
                top: 4px;
                font-size: 8px;
                font-family: PingFang HK-Semibold, PingFang HK;
                font-weight: 600;
                color: #ffffff;
                border-radius: 65px;
                padding: 0 5px;
            }
        }
        .myBlindBottomIcon{
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        .myBlind {
            position: relative;
            width: 85px;
            height: 85px;
            border-radius: 50%;
            margin-bottom: 30px;
            background: url('@assets/TT/blindBox.png') no-repeat;
            background-size: 100% 100%;
            box-shadow: 0px 0px 24px 2px rgba(0, 0, 0, 0.08);
            p {
                position: absolute;
                bottom: -20px;
                left: 50%;
                transform: translate(-50%, 0);
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 120px;
                white-space: nowrap;
                padding: 6px 20px;

                // height: 32px;
                background: #ffffff;
                box-shadow: 0px 0px 16px 2px rgba(0, 0, 0, 0.16), inset 0px -2px 0px 2px rgba(0, 0, 0, 0.16);
                border-radius: 20px 20px 20px 20px;
                font-size: 20px;
                color: #4e5b7e;
            }
        }
        .nftBag {
            position: relative;
            width: 85px;
            height: 85px;
            img {
                width: 100%;
            }
        }
        .city {
            background: url('@assets/TT/city.png') no-repeat;
            background-size: 100% 100%;
        }
        .sport {
            background: url('@assets/TT/mysport.png') no-repeat;
            background-size: 100% 100%;
        }
        .grow {
            background: url('@assets/TT/water.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .sheetContent {
        width: 100vw;
        height: 650px;
        position: relative;
        .content {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 668px;
            background: #ffffff;
            border-radius: 48px 48px 0px 0px;
            padding-top: 120px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;

            .more {
                display: flex;
                justify-content: center;
                align-items: center;
                padding-bottom: 48px;
                span {
                    font-size: 24px;
                    font-family: PingFang TC-Regular, PingFang TC;
                    font-weight: 400;
                    color: #a7aec3;
                    padding-right: 8px;
                }
                img {
                    width: 12px;
                    height: 20px;
                }
            }
            .go-top {
                position: absolute;
                height: 84px;
                bottom: 60px;
                right: 32px;
                display: flex;
                justify-content: center;
                img {
                    width: 80px;
                    height: 80px;
                }
                .go-top-text {
                    position: absolute;
                    bottom: -4px;
                    width: 96px;
                    height: 24px;
                    background: linear-gradient(360deg, #3cbf48 0%, #3dd13f 100%);
                    box-shadow: 0px 2px 0px 2px rgba(56, 178, 60, 1);
                    border-radius: 24px 24px 24px 24px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    span {
                        display: block;
                        width: 100px;
                        font-size: 16px;
                        font-family: PingFang SC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #ffffff;
                        transform: scale(0.7);
                        white-space: nowrap;
                    }
                    img {
                        margin-left: -10px;
                        width: 10px;
                        height: 10px;
                    }
                }
            }
            .xx {
                position: absolute;
                right: 34px;
                top: -26px;
                width: 56px;
                height: 56px;
                background: url('@assets/TT/Icon/xx.png') no-repeat;
                background-size: 100% 100%;
            }
            .xx1 {
                position: absolute;
                right: 34px;
                top: 34px;
                width: 24px;
                height: 24px;
                background: url('@assets/TT/Icon/xx1.png') no-repeat;
                background-size: 100% 100%;
            }
            .xx2 {
                position: absolute;
                right: 34px;
                top: -100px;
                width: 56px;
                height: 56px;
                background: url('@assets/TT/Icon/xx2.png') no-repeat;
                background-size: 100% 100%;
            }
        }
        .title {
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            top: -20px;
            width: 316px;
            height: 80px;
            background: url('@assets/TT/Icon/mowTitle.png') no-repeat;
            background-size: 100% 100%;
            font-size: 28px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: bold;
            color: #ffffff;
            line-height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .achcontain {
            flex: 1;
            // overflow-y: scroll;
            display: flex;
            // padding: 20px 0px 100px 0px;
            flex-wrap: wrap;
            display: flex;
            justify-content: center;
            // overflow: hidden;
            .ach {
                margin: 0 35px 0;
            }
            .ach:nth-child(-n + 3) {
                margin-top: 0px;
            }
        }
    }
    .sheetContent2 {
        width: 100%;
        height: 1070px;
        .content {
            padding-top: 68px;
            height: 1070px;
        }
    }
    .sheetContent3 {
        width: 100%;
        height: 970px;
        .content {
            padding-top: 68px;
            height: calc(80vh);
            background: #f7f7f7;
        }
    }
}

:deep(.van-action-sheet),
:deep(.van-action-sheet__content) {
    overflow: inherit !important;
}

.backToTop {
    width: 70px;
    height: 70px;
    position: fixed;
    right: 30px;
    bottom: 20%;
    opacity: 0;
    border: 5px solid #3ccb41;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;

    img {
        width: 30px;
        height: 24px;
    }

    p {
        bottom: -15px;
        position: absolute;
        color: #3ccb41;
        height: 30px;
        padding: 5px;
        display: flex;
        align-items: center;
        text-align: center;
        justify-content: center;
        background-color: #f5ffeb;
        border-radius: 20px;
        font-size: 20px;
        white-space: nowrap;
    }
}

.undersideBox {
    position: relative;
    background: linear-gradient(180deg, rgba(16, 191, 107, 0) 0%, #5ebf71 5%, #f7f7f7 20%, #f7f7f7 100%);
    margin-top: -272px;
    padding-bottom: 20px;
    .friendComponent {
        margin: 60px auto 0;
    }
}

.headbox {
    position: absolute;
    top: 188px;
    left: 50px;
}
