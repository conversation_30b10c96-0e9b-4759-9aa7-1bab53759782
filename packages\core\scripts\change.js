const fs = require('fs')
const path = require('path')

const list = fs
  .readdirSync(path.resolve('../core/dist/assets'))
  .filter((item) => item.endsWith('js'))
  .map((item) => path.resolve(path.resolve('../core/dist/assets'), item))

list.forEach((item) => {
  let content = fs.readFileSync(item, 'utf-8')
  while (true) {
    const res = /import(.*?)from"vue";/i.exec(content)
    if (res && res[0]) {
      let data = res[0].replace('import', 'const').replace('from"vue"', '=Vue')
      while (data.includes(' as ')) {
        data = data.replace(' as ', ':')
      }
      content = content.replace(res[0], data)
    } else {
      break
    }
  }
  while (content.includes('import"vue";')) {
    content = content.replace('import"vue";', 'Vue;')
  }
  fs.writeFileSync(item, content)
})
