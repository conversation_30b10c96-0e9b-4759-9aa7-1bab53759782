<template>
    <div class="give-dig">
        <div class="give-box">
            <div class="title">
                <div class="head">
                    <img :src="head" alt="">
                </div>
                <div class="content">
                    <span>【{{ name }}】</span>
                </div>
            </div>
            <div class="give-name">
                <span>{{ state.flower.我送了你一瓶.replace('XX',treeName[treeCode]) }}</span>
            </div>
            <div class="give-tips">
                <span>{{ state.flower.快來收下 }}</span>
            </div>
            <div class="give-img">
                <img :src="$imgs[`flowers/${treeCode}.png`]" alt="">
            </div>
            <div class="give-btn">
                <div class="cancel-btn" @click="cancel">
                    <span>{{ state.flower.取消 }}</span>
                </div>
                <div class="confirm-btn" @click="giveSend">
                    <span>{{ state.flower.發送 }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useEventBus,useLang } from "hook";
import { useFriendStore } from "@/store";
import { logEventStatistics } from "@via/mylink-sdk";
const friendStore = useFriendStore()
const  { state } = useLang()
let treeName = ref({
    diaozhonghua:state.flower.吊鐘花,
    jieguojueming:state.flower.節果決明,
    lanhuaying:state.flower.藍花楹,
    xiuqiuhua:state.flower.繡球花,
    yangzijing:state.flower.洋紫荊,
    yumushu:state.flower.魚木樹,
    mumian:state.flower.木棉
})
let tree_id = ref({
    diaozhonghua:1001,
    jieguojueming:1003,
    lanhuaying:1000,
    xiuqiuhua:1004,
    yangzijing:1002,
    mumian:1005,
    yumushu:1006
})
const eventBus = useEventBus()
const props = defineProps({
    treeCode:{
        type:String,
        required:true
    },
    head:{
        type:String,
        required:true
    },
    name:{
        type:String,
        required:true
    },
    friend_third_id:{
        type:String,
        required:true
    }
})
const giveSend = async ()=>{
    logEventStatistics('garden_gift_send_frd_click')
    await friendStore.giveFlowers(props.friend_third_id,tree_id.value[props.treeCode])
    eventBus.emit('cancel')
}

const cancel = ()=>{
    logEventStatistics('garden_gift_cancel_send_click')
    eventBus.emit('cancel')
}
</script>

<style lang="less" scoped>
.give-dig{
    background: rgba(#959596,0.4);
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index:99;
    .give-box{
        width: 620px;
        background: #FFFFFF;
        border-radius: 32px 32px 32px 32px;
        margin: 428px auto;
        padding-bottom: 56px;
        overflow: hidden;
        .title{
            height: 60px;
            display: flex;
            margin-top: 80px;
            justify-content: center;
            align-items: center;
            border-bottom: 2px #EFEFEF solid;
            padding-bottom: 24px;
            width: 524px;
            margin: 80px auto 0;
            .head{
                display: flex;
                justify-content: center;
                align-items: center;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                margin-right: 24px;
                img{
                    border-radius: 50%;
                    width: 100%;
                    height: 100%;
                }
            }
            .content{
                display: flex;
                justify-content: center;
                align-items: center;
                max-width: 442px;
                span{
                    font-size: 28px;
                    font-family: PingFang HK-Regular, PingFang HK;
                    font-weight: 400;
                    color: #4E5B7E;
                }
            }
        }
        .give-name{
            font-size: 36px;
            font-family: PingFang HK-Semibold, PingFang HK;
            font-weight: 600;
            color: #4E5B7E;
            margin-top: 24px;
            text-align: center;
            padding: 0 60px;
        }
        .give-tips{
            font-size: 28px;
            font-family: PingFang HK-Regular, PingFang HK;
            font-weight: 400;
            color: rgba(78,91,126,0.8);
            margin-top: 12px;
            text-align: center;
            padding: 0 60px;
        }
        .give-img{
            width: 144px;
            height: 144px;
            margin: 40px auto 0;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .give-btn{
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 72px;
            .cancel-btn{
                width: 240px;
                height: 82px;
                background: #F4F4F4;
                border-radius: 40px 40px 40px 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 44px;
                span{
                    font-size: 36px;
                    font-family: PingFang HK-Regular, PingFang HK;
                    font-weight: 400;
                    color: #22992C;
                }
            }
            .confirm-btn{
                width: 240px;
                height: 82px;
                background: #3CBF48;
                border-radius: 40px 40px 40px 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                span{
                    font-size: 36px;
                    font-family: PingFang HK-Medium, PingFang HK;
                    font-weight: 500;
                    color: #FFFFFF;
                }
            }
        }
    }
}
</style>