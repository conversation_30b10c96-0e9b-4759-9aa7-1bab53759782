<template>
    <div class="box">
        <div class="container">
            <div class="title" >{{ nft_upgrade_notice ? state.nft.您確定要回收舊版NFT嗎 : state.nft.您確定要回收這個NFT嗎 }}</div>
            <div class="title" >{{ state.nft.可獲積分 }}</div>
            <div class="tip">{{ nft_upgrade_notice ? state.nft.回收舊版後相應的MyGarden權益 : state.nft.回收後相應的MyGarden權益 }}</div>
            <div class="bootom">
                <button class="cancel" @click="cancel">{{ state.nft.取消 }}</button>
                <button class="confirm" @click="seeNFT">{{ state.nft.確定 }}</button>
            </div>
        </div>
        <button class="btn" @click="cancel"></button>
    </div>
</template>

<script setup>
import { logEventStatistics } from '@via/mylink-sdk';
import { useLang, useStorage, useEnvConfig, useToast } from 'hook'
const { state } = useLang()
let emit = defineEmits(['close'])
const props = defineProps({
    nft_group_id: {
        type: String,
        required: false
    },
    nft_upgrade_notice:{
        type: Boolean,
        required: false,
        defualt: false
    }
})

const seeNFT = () => {
    logEventStatistics('garden_confirm_return')
    emit('close')
    if (useEnvConfig().RUN_ENV == 'develop' || useEnvConfig().RUN_ENV == 'uat') {
        location.href = `openurl-modal://https://*************/nftweb/#/third-party?origin=4&target=nftDetail&groupId=${props.nft_group_id}&forceHideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>`
    } else {
        location.href = `openurl-modal://https://cdn.mylink.hk.chinamobile.com/blockchain/link/nft/index.html#/third-party?origin=4&target=nftDetail&groupId=${props.nft_group_id}&forceHideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>`
    }
}

const cancel = () => {
    logEventStatistics('garden_cancel_return')
    emit('close')
}

</script>

<style lang="less" scoped>
.box {
    .container {
        width: 544px;
        background: #ffffff;
        border-radius: 24px 24px 24px 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .title {
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 32px;
            color: #4e5b7e;
            text-align: center;
            margin-top: 24px;
        }
        .tip {
            width: 480px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 24px;
            color: #ef8a00;
            line-height: 36px;
            margin-top: 24px;
        }
        .bootom {
            display: flex;
            justify-content: space-between;
            margin-top: 32px;
            margin-bottom: 48px;
            .cancel {
                width: 200px;
                height: 56px;
                font-family: PingFang SC, PingFang SC;
                font-weight: bold;
                font-size: 28px;
                color: #a7aec3;
                line-height: 32px;
                text-align: center;
                background: #ffffff;
                border-radius: 48px 48px 48px 48px;
                border: 2px solid #a7aec3;
            }
            .confirm {
                width: 200px;
                height: 56px;
                font-family: PingFang SC, PingFang SC;
                font-weight: bold;
                font-size: 28px;
                color: #ffffff;
                line-height: 32px;
                text-shadow: 0px 0px 12px #fbac2e;
                text-align: center;
                background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
                border-radius: 48px 48px 48px 48px;
                margin-left: 32px;
                border: 0px solid #a7aec3;
            }
        }
    }

    .btn {
        position: absolute;
        width: 56px;
        height: 56px;
        background: url('@assets/imgs/closeIcon.png') no-repeat;
        background-size: 100% 100%;
        border: none;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -100px;
    }
}
</style>
