.plantDetail {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #68d685;
    .background {
        position: absolute;
        top: 0;
        width: 100vw;
        z-index: 0;
        .img{
            width: 100%;
        }
        .left{
            width: 32px;
            height: 56px;
            background: url('@assets/imgs/left.png') no-repeat;
            background-size: cover;
            position: absolute;
            left: 96px;
            top: 304px;

        }
        .right{
            width: 32px;
            height: 56px;
            background: url('@assets/imgs/right.png') no-repeat;
            background-size: cover;
            position: absolute;
            right: 64px;
            top: 304px;
        }
    }
    .tree{
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        top: (196/1624*100vh);
        // bottom: 0;
    }
    .yangzijing{
        width: 345px;
        transform: translate(-5px, 0);
    }
    .lanhuaying{
        width: 412px;
        transform: translate(-10px, 0);
    }
    .diaozhonghua{
        width: 428px;
        transform: translate(-10px, 0);
    }
    .jieguojueming{
        width: 425px;
        transform: translate(0px, 0);
    }
    .xiuqiuhua{
        width: 368px;
        transform: translate(2px, 0);
    }
    .mumian{
        width: 396px;
        transform: translate(2px, 0);
    }
    .yumushu{
        width: 352px;
        transform: translate(2px, 0);
    }
    .shuishirong{
        width: 412px;
        transform: translate(2px, 0);
    }
    .huangzhongmu{
        width: 412px;
        transform: translate(2px, 0);
    }
    .intro {
        padding-bottom: 48px;
        margin-top: 486px;
        width: 650px;
        min-height: 540px;
        background: url('@imgs/plantDetail/tab.png') no-repeat center center;
        background-size: 100% 100%;
        z-index: 14;
        position: relative;
        .plant-tips{
            position: absolute;
            width: 156px;
            height: 64px;
            top: -64px;
            left: 34px;
            .plant-img{
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }
            p{
                position: absolute;
                left: 50px;
                top: 14px;
                font-size: 20px;
                font-family: PingFang SC-Bold, PingFang SC;
                color: #FFFFFF;
                width: 94px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
        .upStrategy{
            position: absolute;
            right: 0;
            top: -66px;
            display: flex;
            align-items: center;
            padding: 8px 12px 8px 66px;
            background: rgba(44,153,44,0.9);
            border-radius: 24px 24px 24px 24px;
            .strategyImg{
                position: absolute;
                width: 48px;
                height: 48px;
                left: 0px;
                top: 0;
            }
            >p{
                white-space: nowrap;
                font-size: 22px;
                line-height: 32px;
                vertical-align: middle;
                font-weight: bold;
                color: #FFFFFF;
            }
            .jiantou{
                margin-left: 9px;
                transform: translate(0, 1px);
                height: 20px;
            }
        }
        &.vip-img {
            background: url('@imgs/plantDetail/tab-vip.png') no-repeat center center;
            background-size: 100% 100%;
        }

        .title {
            display: flex;
            justify-content: space-between;
            margin-top: 24px;
            margin-left: 40px;
            margin-right: 32px;
            color: #ffffff;

            .name {
                line-height: 28px;
                font-size: 32px;
                display: flex;
                .vip {
                    text-align: center;
                    background: linear-gradient(90deg, #FFE1BF 0%, #DBAA69 100%);
                    border-radius: 16px 4px 16px 4px;
                    margin-left: 20px;
                    font-size: 22px;
                    font-family: PingFang SC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #965F12;
                    line-height: 28px;
                    padding: 2px 20px;
                }
                .vip_en{
                    padding: 2px 5px;
                }
            }
            

            .target {
                font-size: 22px;
                font-weight: 400;
                line-height: 28px;
                vertical-align: middle;
                white-space: nowrap;
            }
        }

        .next {
            margin-top: 14px;
            margin-left: 40px;
            display: flex;
            justify-content: left;
            font-size: 22px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 28px;

            .kind {
                margin-left: 8px;
                padding: 2px 16px;
                padding-bottom: 3px;
                background: #22992c;
                box-shadow: inset 0px 0px 12px 2px rgba(0, 0, 0, 0.08);
                border-radius: 16px 16px 16px 16px;
                &.vip-kind {
                    background: #9c7158;
                }
            }
        }

        .basic {
            margin: 70px 38px 0px 38px;
            padding-bottom: 20px;
            font-size: 24px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #6a6a6a;
            line-height: 32px;
            min-height: 132px;
            border-bottom: 2px dashed #e0e0e0;
        }

        .award-title {
            display: flex;
            justify-content: left;
            margin: 26px 40px 0 38px;

            .img {
                width: 32px;
                height: 32px;
            }

            .text {
                margin-left: 12px;
                font-size: 24px;
                font-family: PingFang SC-Bold, PingFang SC;
                color: #6a6a6a;
                line-height: 32px;
            }
        }

        .prop {
            display: flex;
            margin: 20px 0 0 40px;
            max-width: 600px;
            overflow-x: scroll;

            .item {
                margin-right: 18px;
                display: flex;
                flex-direction: column;
                align-items: center;
                .icon {
                    display: flex;
                    width: 80px;
                    height: 80px;
                    background: #EFEFEF;
                    opacity: 1;
                    border: 6px solid #E2E2E2;
                    border-radius: 50%;
                    align-items: center;
                    justify-content: center;
                    img{
                        max-width: 70%;
                        max-height: 70%;
                    }
                }

                .text {
                    text-align: center;
                    margin-top: 8px;
                    font-size: 22px;
                    font-family: PingFang SC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #6a6a6a;
                    line-height: 32px;
                }
            }
        }
    }

    .info {
        margin-top: 30px;
        width: 650px;
        min-height: 816px;
        background: #ffffff;
        border-radius: 24px 24px 24px 24px;
        margin-bottom: 169px;
        padding: 40px;
        padding-bottom: 8px;
        .item {
            margin-bottom: 32px;
            .title-box {
                display: flex;
                justify-content: left;
                .icon {
                    width: 12px;
                    height: 32px;
                    background: linear-gradient(360deg, #47d946 0%, #17b326 100%);
                    border-radius: 8px 4px 8px 4px;
                }
                .title {
                    font-size: 24px;
                    font-family: PingFang SC-Bold, PingFang SC;
                    font-weight: bold;
                    color: #6a6a6a;
                    line-height: 32px;
                    margin-left: 7px;

                }
            }

            .text {
                margin-top: 10px;
                font-size: 22px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #6a6a6a;
                line-height: 32px;
            }
        }
    }

    .bottom {
        position: fixed;
        bottom: 0;
        width: 750px;
        height: 135px;
        background: #ffffff;
        box-shadow: 0px -6px 24px 2px rgba(0, 0, 0, 0.08);
        border-radius: 40px 40px 0px 0px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-bottom: 10px;
        z-index: 99;

        .btn {
            width: 570px;
            height: 74px;
            border-radius: 48px 12px 48px 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: #ffffff;
            line-height: 32px;
            .text {
                margin-top: 7px;
            }
        }
        .login{
            background: linear-gradient(360deg, #3CBF48 0%, #85E277 100%);
            box-shadow: 0px 4px 0px 2px rgba(56,178,60,1), inset 0px 2px 0px 2px rgba(146,232,152,1);
        }
        .can {
            background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
            box-shadow: 0px 8px 0px 2px #fcaf28, inset 0px 4px 0px 2px #fff2b2;
        }

        .cannot {
            background: linear-gradient(180deg, #d3d3d3 0%, #848484 100%);
            box-shadow: 0px 8px 0px 2px #939393, inset 0px 4px 0px 2px #eaeaea;
        }
    }
}