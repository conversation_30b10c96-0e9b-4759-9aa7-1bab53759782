<template>
    <div class="dialog-wrap">
        <div class="pic" v-if="!target_value"><img :src="$imgs['dialog/purchase-icon1.png']" /></div>
        <div class="title">{{ $lang.dialogs.purchaseSuccessful.title }}</div>
        <div class="target-box" v-if="target_value">
            <div class="content">
                {{ state.shopping.道具券碼 }}：{{ target_value }}
                <img @click="copyTarget(target_value)" :src="$imgs['shopping/copyIcon.png']" alt="">
            </div>
            <div class="tips">
                {{ state.shopping.請先複製券碼後再前往使用 }}
            </div>
        </div>
        <div class="sub">{{ target_value ? state.shopping.購買後可在減碳值記錄 : $lang.dialogs.purchaseSuccessful.sub }}</div>
        <div class="btn-group">
            <button class="cancel" @click="cancel">{{ $lang.dialogs.purchaseSuccessful.cancelBtnTxt }}</button>
            <button class="confirm" @click="confirm">{{ $lang.dialogs.purchaseSuccessful.confirmBtnTxt }}</button>
        </div>
        <button class="close" @click="emit('close')"></button>
    </div>
</template>
<script setup lang="ts">
import { useDialog,useToast,useUtils,useLang } from 'hook'
import { inject } from 'vue'
import { logEventStatistics } from '@via/mylink-sdk'
const { state } = useLang()
const { copyText } = useUtils()
defineProps({
    target_value:{
        type:String,
        required:false
    },
    jump_url:{
        type:String,
        required:false
    }
})
const emit = defineEmits(['close'])
const uuid = inject('uuid') as string
const confirm = () => {
    logEventStatistics('garden_cr_mall_use')
    useDialog().getInstance(uuid)?.emit('confirm')
}
const cancel = () => {
    useDialog().getInstance(uuid)?.emit('cancel')
}
const copyTarget = (code)=>{
    copyText(code)
    useToast().toast(state.shopping.複製成功)
}
</script>
<style lang="less" scoped>
.dialog-wrap {
    width: 600px;
    background: #FFFFFF;
    border-radius: 24px 24px 24px 24px;
    padding: 48px 40px;
    position: relative;
    .pic {
        width: 120px;
        margin: auto;
        img {
            display: block;
        }
    }
    .title {
        font-size: 32px;
        font-weight: bold;
        color: #4E5B7E;
        text-align: center;
        margin-top: 16px;
    }
    .target-box{
        width: 446px;
        margin: 24px auto;
        background: rgba(167,174,195,0.18);
        border-radius: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        padding: 24px;
        .content{
            font-weight: 500;
            font-size: 28px;
            color: #000001;
            line-height: 36px;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            img{
                width: 32px;
                height: 32px;
                margin-left: 10px;
            }
        }
        .tips{
            font-weight: 400;
            font-size: 20px;
            color: #EF8A00;
            line-height: 32px;
        }
    }
    .sub {
        font-size: 24px;
        color: #4E5B7E;
        text-align: center;
        margin: 16px 0 28px;
        line-height: 40px;
    }
    .btn-group {
        display: flex;
        justify-content: space-around;
        button {
            width: 200px;
            height: 56px;
            font-size: 28px;
            background: #FFFFFF;
            border-radius: 48px 48px 48px 48px;
            border: 2px solid transparent;
            &.cancel {
                color: #A7AEC3;
                border: 2px solid #A7AEC3;
            }
            &.confirm {
                background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
                color: white;
            }
        }
    }
    .close {
        width: 56px;
        height: 56px;
        background: url('@/assets/imgs/closeIcon.png') no-repeat;
        background-size: 100% auto;
        position: absolute;
        display: block;
        border: none;
        left: 0;
        right: 0;
        margin: auto;
        bottom: -150px;
    }
}
</style>