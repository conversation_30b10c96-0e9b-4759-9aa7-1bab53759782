<template>
    <div class="upStrategyDialog">
        <div class="title">
            {{ props.title }}
        </div>
        <p v-html="props.detail"></p>
        <div v-if="type === 'recharge'" class="button recharge" @click="recharge">{{ state.ai.去充值 }}</div>
        <div
            v-else-if="type === 'linkMember'"
            class="button"
            :class="{ buyLinkMember: !useUserStore().amm_vip && useUserStore().isVip }"
        >
            <span v-if="useUserStore().amm_vip" @click="eventBus.emit('closeUpStrategyDialog')">
                {{ state.link会员.知道了 }}
            </span>
            <span
                v-else-if="!useUserStore().amm_vip && !useUserStore().isVip"
                @click="eventBus.emit('closeUpStrategyDialog')"
            >
                {{ state.link会员.仅支持后付用户开通Link着数会员 }}
            </span>
            <span @click="buyLinkMember" v-else>{{ state.link会员.成为Link着数会员 }}</span>
        </div>
        <div v-else class="button" @click="eventBus.emit('closeUpStrategyDialog')">{{ state.bearTalk.知道了 }}</div>
    </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store'
import { useLang, useEventBus, useEnvConfig } from 'hook'
const { state } = useLang()
const env = useEnvConfig()
const eventBus = useEventBus()
const props = defineProps({
    title: {
        type: String,
        require: true
    },
    detail: {
        type: String,
        require: true
    },
    type: {
        type: String,
        require: true
    }
})

const recharge = () => {
    eventBus.emit('closeUpStrategyDialog')
    if (env.RUN_ENV == 'develop' || env.RUN_ENV == 'uat') {
        location.href =
            'http://*************/activity/2daigc/#/photo/profile?forceHideNavigationBar=true&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&isRecharge=1'
    } else {
        location.href =
            'https://mylink.komect.com/activity/2daigc/#/photo/profile?forceHideNavigationBar=true&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&isRecharge=1'
    }
}

const buyLinkMember = () => {
    eventBus.emit('closeUpStrategyDialog')
    if (env.RUN_ENV == 'develop' || env.RUN_ENV == 'uat') {
        location.href =
            'openurl-modal://https://marketplaceuat.hk.chinamobile.com/marketHK/#/jetsoVip?token=<<cmcchkhsh_ssoToken>>&lang=<<cmcchkhsh_cmplang>>&shopCode=xxx'
    } else {
        location.href =
            'openurl-modal://https://marketplace.hk.chinamobile.com/marketHK/#/jetsoVip?token=<<cmcchkhsh_ssoToken>>&lang=<<cmcchkhsh_cmplang>>&shopCode=xxx'
    }
}
</script>

<style lang="less" scoped>
.upStrategyDialog {
    padding: 40px;
    font-family: PingFang SC, PingFang SC;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
        font-weight: 600;
        font-size: 32px;
        color: #4e5b7e;
        line-height: 40px;
        text-align: center;
        margin-bottom: 32px;
    }

    p {
        font-weight: 400;
        font-size: 28px;
        color: #4e5b7e;
        line-height: 40px;
        text-align: left;
        margin-bottom: 50px;
    }

    .button {
        width: 568px;
        height: 84px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
        box-shadow: 0px 8px 0px 2px #fcaf28, inset 0px 4px 0px 2px #fff2b2;
        border-radius: 48px 12px 48px 12px;
        font-weight: bold;
        font-size: 30px;
        color: #ffffff;
        text-align: center;
        span {
          transform: translateY(5px);
          display: block;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          width: 100%;
          height: 100%;
          padding: 0 30px;
        }
    }
    .recharge {
        background: linear-gradient(180deg, #9f78f5 0%, #7d55f5 100%);
        box-shadow: 0px 8px 0px 2px #6338df, inset 0px 4px 0px 2px #bab2ff;
    }
    .buyLinkMember {
        background: linear-gradient(180deg, #9f78f5 0%, #7d55f5 100%);
        box-shadow: 0px 8px 0px 2px #6338df, inset 0px 4px 0px 2px #bab2ff;
    }
}
</style>
