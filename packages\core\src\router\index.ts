import { useEnvConfig, useLang, useRouter } from 'hook'
import { configureShare, getSystem, logEventStatistics,getVersionForAosAndIos,showHeaderRight,inApp } from '@via/mylink-sdk'
import { useAppStore } from '@/store'
import { imgs } from "@/assets/imgs/index";

const { addRoutes, listener, router, currentRoute } = useRouter()
const { state, lang } = useLang()

addRoutes([
    {
        path: '/',
        redirect: 'home'
    },
    {
        path: '/home',
        name: 'home',
        component: () => import('@pages/home/<USER>'),
        meta: {
            title: {
                tc: 'MyGarden',
                sc: 'MyGarden',
                en: `MyGarden`
            }
        }
    },
    {
        path: '/loading',
        name: 'loading',
        component: () => import('@pages/loading/loading.vue'),
        meta: {
            title: {
                tc: 'MyGarden',
                sc: 'MyGarden',
                en: `MyGarden`
            }
        }
    },
    {
        path: '/friendPage',
        name: 'friendPage',
        component: () => import('@pages/home/<USER>'),
        meta: {
            title: {
                tc: 'MyGarden',
                sc: 'MyGarden',
                en: `MyGarden`
            }
        }
    },
    {
        path: '/mall',
        name: 'mall',
        component: () => import('@pages/mall/mall.vue'),
        meta: {
            title: {
                tc: 'MyGarden',
                sc: 'MyGarden',
                en: `MyGarden`
            }
        }
    },
    {
        path: '/upStrategy',
        name: 'upStrategy',
        component: () => import('@pages/upStrategy/upStrategy.vue'),
        meta: {
            title: {
                tc: '升級攻略',
                sc: '升级攻略',
                en: `Upgrade Tips`
            }
        }
    },
    {
        path: '/test',
        name: 'test',
        component: () => import('@pages/test.vue'),
        meta: {
            title: {
                tc: '植物詳情',
                sc: '植物详情',
                en: `Detailed information`
            }
        }
    },
    {
        path: '/plantDetail/:id',
        name: 'plantDetail',
        component: () => import('@pages/plantDetail/plantDetail.vue'),
        meta: {
            title: {
                tc: '植物詳情',
                sc: '植物详情',
                en: `Detailed information`
            }
        },
        props: true
    },
    {
        path: '/plantSelection',
        name: 'plantSelection',
        component: () => import('@pages/plantSelection/plantSelection.vue'),
        meta: {
            title: {
                tc: '選擇植物',
                sc: '选择植物',
                en: `Select a plant`
            }
        }
    },
    {
        path: '/package',
        name: 'package',
        component: () => import('@pages/Package/Package.vue'),
        meta: {
            title: {
                tc: 'MyGarden',
                sc: 'MyGarden',
                en: `MyGarden`
            }
        }
    },
    {
        path: '/record',
        name: 'record',
        component: () => import('@pages/record/record.vue'),
        meta: {
            title: {
                tc: '減碳值記錄',
                sc: '减碳值记录',
                en: `Record`
            }
        }
    },
    {
        path: '/share',
        name: 'share',
        component: () => import('@pages/share/share.vue'),
        meta: {
            title: {
                tc: 'MyGarden',
                sc: 'MyGarden',
                en: `MyGarden`
            }
        }
    },
    {
        path: '/rule',
        name: 'rule',
        component: () => import('@pages/rule/rule.vue'),
        meta: {
            title: {
                tc: '條款及細則',
                sc: '条款及细则',
                en: 'Terms and Conditions'
            }
        }
    },
    {
        path: '/friendList',
        name: 'friendList',
        component: () => import('@pages/friend/friendList.vue'),
        meta: {
            title: {
                tc: '選擇好友',
                sc: '选择好友',
                en: 'Choose Friend'
            }
        }
    },
    {
        path: '/treeNFT/:id',
        name: 'treeNFT',
        component: () => import('@/pages/treeNFT/treeNFT.vue'),
        meta: {
            title: {
                tc: 'NFT',
                sc: 'NFT',
                en: 'NFT'
            }
        }
    },
    {
        // 成就页
        path: '/achievement',
        name: 'achievement',
        component: () => import('@pages/achievement/achievement.vue'),
        meta: {
            title: {
                tc: '成就',
                sc: '成就',
                en: `Achievement`
            }
        }
    },
    {
        path: '/:pathMatch(.*)*',
        redirect: 'home'
    }
])

listener.listen(async (route) => {
    if (route.meta.title) {
        if (typeof route.meta.title === 'object') {
            if (route.query.friendname && route.name == 'home') {
                document.title = route.query.friendname as string
            }else{
                document.title = (route.meta.title as Record<string, any>)[lang.value]
            }
        } else {
            if (route.query.friendname && route.name == 'home') {
                document.title = route.query.friendname as string
            }else{
                document.title = route.meta.title as string
            }
        }
    } else {
        document.title = state.title.home
    }
    if(inApp.value) {
        const version = await getVersionForAosAndIos()
        if(version < 1060) {
          if(route.name == 'home') {
            const urlObj = new URL(location.href.split('#')[0])
            const shareUrl = urlObj.toString()
              configureShare({
                url: shareUrl,
                title: state.share.title,
                content: state.share.desc,
                img: imgs['share.png'],
            })
          }
        } else {
          if(route.name == 'home' && !route.query.friendId) {
              showHeaderRight(location.origin + location.pathname + '#/home')
          } else if(route.name == 'plantSelection') {
              showHeaderRight(location.origin + location.pathname + '#/plantSelection')
          } else {
              showHeaderRight('cmcchkhsh://home')
          }
        }
      }
}, true)

router.beforeEach((to, from, next) => {
    const appStore = useAppStore()
    if (!to.query.friendId && to.name == 'home') {
        if (typeof to.meta.title === 'object' && document.querySelector('.nav .title')) {
            document.querySelector('.nav .title')!.innerText = (to.meta.title as Record<string, any>)[lang.value]
        }
    }
    console.log(to.name, appStore.loading, '路由拦截')
    if (to.name != 'loading' && !appStore.loading) {
        appStore.loading = true
        next({
            path: '/loading',
            query: {
                name: to.name?.toString(),
                hideNavigationBar: 'true'
            }
        })
    } else {
        next()
    }
    // 全局变量储存加载状态
    // 未加载跳转loading页面
    // 跳转时候记录跳转前的页面
    // 加载后跳转到该页面
})
export default router
