<template>
  <div class="box">
    <img class="hhaa ll" @click.stop="iknew" v-if="step > 0" :src="$imgs['j1.png']" alt="">
    <img class="hhaa rr" @click.stop="iknow" v-if="step < 2-1" :src="$imgs['j1.png']" alt="">
    <div class="titlell" v-if="userStore.isVip">
      <p class="title">{{state.联动奖赏.会员限定奖励}}</p>
      <p class="num">{{ "(" + (step+1) + "/" + 2 + ")"}}</p>
    </div>
    <div v-if="step == 0" class="boxxxx">
        <!-- TODO NFT -->
        <!-- <div class="left center">
          <achieve class="ach" :item='item' type='small' />
          <p class="p">{{state.home.x成就.replace('{x}', state.plant[item.code]?.plantName)}}</p>
        </div> -->
        <template v-if="item.code == 'shuishirong' || item.code == 'huangzhongmu'">
          <div class="left center">
            <achieve class="ach" :item='item' type='small' />
            <p class="p">{{state.home.x成就.replace('{x}', state.plant[item.code]?.plantName)}}</p>
          </div>
        </template>
        <template v-else>
          <div class="left ">
            <achieve class="ach" :item='item' type='small' />
            <p class="p">{{state.home.x成就.replace('{x}', state.plant[item.code]?.plantName)}}</p>
          </div>
          <div class="right">
            <img class="decorate" :src="$imgs[`decorate/${item.code}.png`]" alt="">
            <p class="p">{{state.home.MyHome布置}}</p>
          </div>
          <img class="jiantou" :src="$imgs[`yellow-jiantou.png`]" alt="">
          <p class="tips">{{state.home.完成种植不仅.replace('XXXX',treeName[item.code])}}</p>
        </template>
    </div>
    <gifPropDialog :isInfo="false" :propInstance="reword" v-if="step == 1" />
    <button class="btn" @click="emit('close')"></button>
    </div>
    
</template>

<script setup>
import achieve from './achieve.vue'
import gifPropDialog from './gifPropDialog.vue'
import { useTaskStore, useTreeStore, useUserStore, usePropStore, useAppStore } from '@/store'
import { useLang } from 'hook'
import { ref } from 'vue'
const { state } = useLang()
const userStore = useUserStore()
const props = defineProps({
    item:{
        type: Object,
        required: false,
    },
    reword: {
      type: Object,
      required: false,
    }
})
let emit = defineEmits(['close'])
let step = ref(0)
let stepNum = ref(2)
let treeName = ref({
    diaozhonghua:state.flower.吊鐘花,
    jieguojueming:state.flower.節果決明,
    lanhuaying:state.flower.藍花楹,
    xiuqiuhua:state.flower.繡球花,
    yangzijing:state.flower.洋紫荊,
    yumushu:state.flower.魚木樹,
    mumian:state.flower.木棉
})
const iknow = () => {
    if(step.value == stepNum.value-1){
    }else{
        step.value ++
    }
}

const iknew = () => {
    if(step.value == 0){
    }else{
        step.value --
    }
}
</script>

<style lang='less' scoped>
    .box{
        
        position: relative;
        .boxxxx{
          width: 542px;
          height: 540px;
          background: #FFFFFF;
          border-radius: 72px 24px 72px 24px;
        }
        .hhaa{
          // width: 38px;
          height: 48px;
          position: absolute;
          top: 250px;
          z-index: 2;
          &.ll{
              left: -60px;
          }
          &.rr{
              right: -60px;
              transform: rotate(180deg);
          }
          &.ll1{
              left: 100px;
              transform: rotate(180deg);
          }
          &.rr1{
              right: 100px;
          }
      }
        .titlell{
          position: absolute;
          display: flex;
          flex-direction: column;
          align-items: center;
          left: 50%;
          transform: translate(-50%, 0);
          top: -120px;
          .title{
            
            font-size: 48px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: bold;
            color: #FFFFFF;
            line-height: 32px;
            white-space: nowrap;
          }
          .num{
            margin-top: 20px;
            font-size: 32px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: bold;
            color: #FFFFFF;
            line-height: 32px;
            white-space: nowrap;
          }
        }
        .btn{
            position: absolute;
            width: 56px;
            height: 56px;
            background: url('@assets/imgs/closeIcon.png') no-repeat;
            background-size: 100% 100%;
            border: none;
            left: 50%;
            transform: translate(-50%, 0);
            bottom: -100px;
        }
        .p{
          position: absolute;
          font-size: 24px;
          color: #4E5B7E;
          left: 50%;
          transform: translate(-50%, 0);
          white-space: nowrap;
          font-weight: 550;
        }
        .right{
          position: absolute;
          top: 130px;
          right: 48px;
          >.decorate{
            height: 187px;
          }
          .p{
            bottom: 235px;
          }
        }
      .left{
          position: absolute;
          top: 138px;
          left: 48px;
          .p{
            bottom: 216px;
            width: 240px;
            height: 50px;
            text-align: center;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            // background-color: pink;
            white-space: normal;
          }
        }
        .center{
          left: 50%;
          top: 50%;
          transform: translateX(-50%) translateY(-50%);
        }
        .jiantou{
          width: 70px;
          height: 43px;
          position: absolute;
          top: 214px;
          left: 243px;
        }
        .tips{
          width: 100%;
          padding: 0 48px;
          box-sizing: border-box;
          position: absolute;
          text-align: center;
          left: 50%;
          transform: translate(-50%, 0);
          font-size: 24px;
          color: #4E5B7E;
          top: 390px;
        }
    }
</style>