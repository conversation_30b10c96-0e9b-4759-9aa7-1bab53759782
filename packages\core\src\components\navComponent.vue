<template>
    <div v-if="isApp || envConfig.RUN_ENV == 'develop'" class="nav">
        <!-- <div @click="nativeBackClick()" v-if="returnType == 2" class="return2box">
            <img class="return2" :src="$imgs['return2.png']" alt="">
        </div>
        <img @click="nativeBackClick()" v-if="returnType == 1" class="return1" :src="$imgs['return1.png']" alt="">
        
        <img @click="upShare()" v-if="showTransmit && useFriendStore().isOwn" class="transmit" :src="$imgs['transmit.png']" alt="">
        <img @click="toRoute('upStrategy')" v-if="showStrategy" class="strategyIcon" :src="$imgs['strategyIcon.png']" alt="">
        <p class="title">{{currentRoute.meta.title[lang]}}</p> -->
    </div>
</template>

<script lang='ts' setup>
import { ref } from 'vue'
import { useRouter, useLang, useEnvConfig } from 'hook';
import { nativeBackClick, upShare } from '@unity/unity'
import { useFriendStore } from '@/store'
import { inApp } from '@via/mylink-sdk'
const envConfig = useEnvConfig()
const { currentRoute, router } = useRouter()
const { lang } =  useLang()
const props = defineProps({
  returnType: {
    type: Number
  },
  showTransmit:{
    type: Boolean
  },
  showStrategy:{
    type: Boolean
  }
})
let isApp = ref(inApp.value)
const toRoute = (name:string) => {
    router.push({name: name, query: {"hideNavigationBar": 'true'}})
}
console.log(inApp.value, isApp.value, 'inApp')
</script>

<style lang='less' scoped>
// .nav{
//     z-index: 60;
//     img{
//         z-index: 60;
//     }
//     .return1{
//         position: fixed;
//         width: 80px;
//         top: 90px;
//         left: 54px;
//     }
    
//     .return2box{
//         position: fixed;
//         top: 90px;
//         left: 16px;
//         width: 80px;
//         height: 80px;
//         display: flex;
//         align-items: center;
//         justify-content: center;
//         z-index: 60;
//         .return2{
//             width: 17px;
//         }
//     }
//     .transmit{
//         position: fixed;
//         width: 30px;
//         top: 112px;
//         right: 54px;
//     }
//     .strategyIcon{
//         position: fixed;
//         width: 32px;
//         top: 116px;
//         right: 54px;
//     }
//     .title{
//         position: fixed;
//         left: 50%;
//         transform: translate(-50%, 0);
//         top: 108px;
//         font-size: 36px;
//         font-family: PingFangSC-Medium-, PingFangSC-Medium;
//         font-weight: 600;
//         color: #1B1B1B;
//         z-index: 1;
//         white-space: nowrap !important;
//     }
// }
</style>