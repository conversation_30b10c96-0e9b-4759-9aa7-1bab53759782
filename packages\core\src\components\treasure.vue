<template>
  <div class="box">
    <div id="treasure">
      <img id='img' :class="{isLaod: isLaod}" :src="$imgs['Open_treasure.png']" alt="">
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, onUnmounted } from 'vue'
import { useDialog } from 'hook';
import { imgs } from '@assets/imgs'
const uuid = inject('uuid')

const trsClose = () =>{
    useDialog().getInstance(uuid)?.emit('trsClose')
} 

let frameId = 0;
let status = ref(0)
let isLaod = ref(false)

onMounted(() => {
  changeImg()
})

onUnmounted(() => {
  // clearTimeout(frameId)
})

function changeImg() {
  document.querySelector('#img').onload = () => {
    isLaod.value = true
  }
  document.querySelector('#img').addEventListener('animationend', () =>{
    trsClose()//动画结束执行的函数
  })
}


</script>

<style lang='less' scoped>
#treasure{
  transform: scale(1.5);
  width: 240px;
  height: 240px;
  position: relative;
  overflow: hidden;
  >img{
    position: absolute;
    width: 4800px;
    height: 480px;
    &.isLaod{
      animation: myAnimation1 2s steps(1) forwards;
    }
  }
}
@keyframes myAnimation1 {
	0% { left: 0px; top: 0px; }
	3.57% { left: -240px; top: 0px; }
	7.14% { left: -480px; top: 0px; }
	10.71% { left: -720px; top: 0px; }
	14.29% { left: -960px; top: 0px; }
	17.86% { left: -1200px; top: 0px; }
	21.43% { left: -1440px; top: 0px; }
	25.00% { left: -1680px; top: 0px; }
	28.57% { left: -1920px; top: 0px; }
	32.14% { left: -2160px; top: 0px; }
	35.71% { left: -2400px; top: 0px; }
	39.29% { left: -2640px; top: 0px; }
	42.86% { left: -2880px; top: 0px; }
	46.43% { left: -3120px; top: 0px; }
	50.00% { left: -3360px; top: 0px; }
	53.57% { left: -3600px; top: 0px; }
	57.14% { left: -3840px; top: 0px; }
	60.71% { left: -4080px; top: 0px; }
	64.29% { left: -4320px; top: 0px; }
	67.86% { left: -4560px; top: 0px; }
	71.43% { left: 0px; top: -240px; }
	75.00% { left: -240px; top: -240px; }
	78.57% { left: -480px; top: -240px; }
	82.14% { left: -720px; top: -240px; }
	85.71% { left: -960px; top: -240px; }
	89.29% { left: -1200px; top: -240px; }
	92.86% { left: -1440px; top: -240px; }
	96.43% { left: -1680px; top: -240px; }
	100.00% { left: -1920px; top: -240px; }
}
</style>