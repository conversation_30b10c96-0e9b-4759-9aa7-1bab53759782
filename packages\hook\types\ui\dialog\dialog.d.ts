import { UseDialog } from './type';
import { ComponentInternalInstance } from 'vue';
export declare class Dialog<T extends Record<string, any>> {
    private state;
    componentOptions: any;
    el: any;
    parent: ComponentInternalInstance | null;
    cacheOpts: UseDialog.DialogOpt;
    componentRegisterOpts: UseDialog.DialogOpt;
    private isSingle;
    beforeCloseFn: () => Promise<boolean>;
    constructor(isSingle: boolean, parent: ComponentInternalInstance | null, name: string, opts?: UseDialog.DialogOpt);
    show(props?: T, opts?: UseDialog.DialogOpt): Promise<unknown>;
    close(): Promise<unknown>;
    beforeClose(cb: () => Promise<boolean>): void;
    on(type: string, handler: any): this;
    off(type: string, handler: any): void;
    emit(type: string, ...params: any): void;
}
