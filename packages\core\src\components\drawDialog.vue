<template>
  <div class="box">
        <p class="tip">{{state.home.抽奖奖励}}</p>
        
        <div class="cardContainer">
            <div class="cardItem" v-if="awordArr.length == 1">
                <p class="timeOut">{{dayjs(awordArr[0].destory_at).format('YYYY.MM.DD')}}{{state.home.过期}}</p>
                <img class="card" :src="$imgs['awordbg.png']" alt="">
                <img class="typeImg" :src="$imgs[`${awordArr[0].code == 'x2wewalk' ? 'Double_step' : 'reduction_card'}.png`]" alt="">
                <p class="cardname">{{
                    awordArr[0].code == 'x2wewalk' ? state.home.步数单日双倍卡 : state.home.减碳值单日双倍卡    
                }}</p>
            </div>
            <div class="twoItem" v-if="awordArr.length >= 2">
                <div class="cardItem cardItem1" v-for="(item, index) in awordArr" :key="index">
                    <p class="timeOut">{{dayjs(item.destory_at).format('YYYY-MM-DD')}}{{state.home.过期}}</p>
                    <img class="card" :src="$imgs['awordbg.png']" alt="">
                    <img class="typeImg" :src="$imgs[`${item.code == 'x2wewalk' ? 'Double_step' : 'reduction_card'}.png`]" alt="">
                    <p class="cardname">{{
                         item.code == 'x2wewalk' ? state.home.步数单日双倍卡 : state.home.减碳值单日双倍卡
                        }}</p>
                </div>
            </div>
        </div>
        <p class="bb">
            {{state.dialog.奖励已经放进背包了哦}}
        </p>
        <div class="knowbox">
            <div @click.stop="toPackage" class="know">
                {{state.dialog.前往背包}}
            </div>
            <div v-if="awordArr.length == 1" @click.stop="useCard(awordArr[0])" class="know">
                {{state.dialog.立即使用}}
            </div>
        </div>
        
        <p class="fanhui">{{state.dialog.点击屏幕返回}}</p>
  </div>
</template>

<script setup lang='ts'>
import { useLang, useDialog, useDayjs } from 'hook';
import { logEventStatistics } from '@via/mylink-sdk'
import { inject } from 'vue'
const { state } = useLang()
const { dayjs } = useDayjs()
const props = defineProps({
  awordArr: {
      type: Array<any>,
      required: true
  }
})

const uuid = inject('uuid')

const toPackage = () => {
    logEventStatistics('garden_blindbox_backpack_click')
    useDialog().getInstance(uuid)?.emit('toPackage')
}

const useCard = (item) => {
    logEventStatistics('garden_blindbox_use_click')
    useDialog().getInstance(uuid)?.emit('useCard', item)
}

</script>

<style lang='less' scoped>
.box{
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: column;
    .tip{
        white-space: pre-line;
        text-align: center;
        font-size: 48px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        margin-bottom: 50px;
    }
    .cardContainer{
        display: flex;
        justify-content: center;
        width: 750px;
        .twoItem{
            width: 750px;
            display: flex;
            // justify-content: center;
            overflow: scroll;
            >.cardItem1{
                flex-shrink: 0;
            }
        }
        .cardItem{
            width: 334px;
            height: 488px;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            &.cardItem1{
                width: 314px;
                height: 458px;
                margin-left: 24px;
                margin-right: 12px;
                flex-shrink: 0;
                &:first-child{
                    margin-left: 40px;
                }
                .typeImg{
                    margin-top: 50px;
                }
            }
            .card{
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
            .typeImg{
                position: relative;
                height: 252px;
                margin-top: 70px;
            }
            .cardname{
                transform: translate(0, -30px);
                position: relative;
                font-size: 32px;
                font-family: PingFang TC-Semibold, PingFang TC;
                font-weight: 600;
                color: #4E5B7E;
                white-space: pre-line;
                text-align: center;
                display: flex;
                align-items: center;
                height: 155px;
                // background-color: pink;
            }
            .timeOut{
                z-index: 1;
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                font-size: 24px;
                font-family: PingFang SC-Semibold, PingFang SC;
                font-weight: 600;
                color: #FFFFFF;
                top: 10px;
                white-space: nowrap;
            }
        }
    }
    .bb{
        font-size: 28px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        padding-bottom: 34px;
        padding-top: 24px;
        text-align: center;
    }
    .knowbox{
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        >.know{
            margin-bottom: 40px;
        }
        >.know:nth-child(2){
            background: linear-gradient(180deg, #56D3EB 0%, #34AADF 100%);
            box-shadow: 0px 8px 0px 2px rgba(56,120,185,1), inset 0px 4px 0px 2px rgba(140,239,247,1);
            margin-bottom: 0px;
        }
    }
    .know{
        min-width: 430px;
        height: 84px;
        background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
        box-shadow: 0px 8px 0px 2px rgba(252,175,40,1), inset 0px 4px 0px 2px rgba(255,242,178,1);
        border-radius: 48px 12px 48px 12px;
        font-size: 36px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        line-height: 32px;
        text-shadow: 0px 0px 12px #FBAC2E;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .fanhui{
        position: absolute;
        font-size: 28px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -160px;
        white-space: nowrap;
    }
}
</style>