import { useApi } from 'hook'

const { post, authPost, authGet, authRequest } = useApi()

// 登录
function login(token:String,zone:number){
    return post('/api/sdk/user/login', {
      data: {
        token,
        zone
      }
    })
}

// 完成新手引导
function guildCom(){
  return authRequest('PATCH', '/api/sdk/treetask/update/guild_completed', {
    params: {}
})
}

// 设置每日活动提醒标志
function updateNotifiedActivity(){
  return authRequest('PATCH', '/api/sdk/treetask/update/notified_activity', {
    params: {}
})
}

// 设置背包提醒标志
function updatePackageFlag(){
  return authRequest('PATCH', '/api/sdk/treetask/update/flag_on_received_item', {
    params: {}
})
}

// 设置背包提醒标志(功能卡)
function updatePackageFuncFlag(){
  return authRequest('PATCH', '/api/sdk/treetask/update/flag_on_received_func_item', {
    params: {}
})
}

// 设置背包提醒标志(vip功能卡)
function updatePackageFuncFlag1(){
  return authRequest('PATCH', '/api/sdk/treetask/update/flag_on_received_vip_item', {
    params: {}
})
}

// 设置首次登陆提醒标志
function updateFirstLogin(){
  return authRequest('PATCH', '/api/sdk/treetask/update/notified_first_login', {
    params: {}
})
}

// 设置二期活动首次登陆提醒标志
function updateFirstLogin1(){
  return authRequest('PATCH', '/api/sdk/treetask/update/notified_first_logic1', {
    params: {}
})
}

// 设置三期活动首次登陆提醒标志
function updateFirstLogin2(){
  return authRequest('PATCH', '/api/sdk/treetask/update/notified_first_logic2', {
    params: {}
})
}

function updateFirstLogin3(){
  return authRequest('PATCH', '/api/sdk/treetask/update/notified_first_logic3', {
    params: {}
})
}

function updateFirstLogin4(){
  return authRequest('PATCH', '/api/sdk/treetask/update/notified_first_logic4', {
    params: {}
})
}

function update(manage_key,manage_value) {
  return authRequest('PATCH', 'api/sdk/treetask/update/webui_manage_values',{
    params:{
      manage_key,
      manage_value
    }
  })
}

//浇水
function watering() {
  return authRequest('PATCH', '/api/sdk/treetask/watering')
}


export { watering,update,login, guildCom, updateNotifiedActivity, updateFirstLogin, updatePackageFlag, updatePackageFuncFlag, updateFirstLogin1, updatePackageFuncFlag1, updateFirstLogin2, updateFirstLogin3, updateFirstLogin4 }
