<template>
    <div class="progressBox">
        <div class="prev" @click="progressBar.prev()"></div>
        <van-swipe :show-indicators="false" class="swiperBox" :loop="false" :initial-swipe="level-1" ref="progressBar">
            <van-swipe-item v-for="(item, index) in rewardConfigs" class="sliderList" :key="index">
                <div class="progressBar">
                    <!-- 进度条文字 -->
                    <span v-if="index == level - 1">{{ tree_energy }}/{{ demand[level] }}</span>
                    <span v-else-if="index < level - 1">{{ state.home.已完成 }} {{ item.energy_value }}</span>
                    <span v-else>{{ state.home.未达成 }} {{ item.energy_value }}</span>
                    <!-- 进度条 -->
                    <div class="schedule-mask">
                        <div class="schedule" ref="schedule"></div>
                    </div>
                    <!-- 图片 -->
                    <div class="img-area"
                        @click="() => { index < 9 ? emit('showProp', props.rewardConfigs[index].reward_items[0]) : emit('showAchieve', props.rewardConfigs[index - 1].vip_reward_items[0]) }">
                        <img v-if="index < 9" :class="{ 'acticity': index < level - 1 }"  class="scheduleImg"
                            :src="$imgs[`props/${state.propsReward[props.rewardConfigs[index].reward_items[0]]?.propType}/${props.rewardConfigs[index].reward_items[0]}.png`]">
                        <img v-else :src="$imgs['chuizi.png']" alt="" class="scheduleImg">
                        <img :src="$imgs['lockPro.png']" alt="" v-if="index >= level - 1" class="scheduleImg">
                    </div>
                </div>
                <div class="progressText">
                    <div class="progressLeft" :class="{'progressLeftText':lang == 'en'}">
                        <p class="progressNo">Lv.{{ index + 1 }}</p> <p class="progressNo">|</p> <p class="progressDes">{{ state.plantStage[index] }}</p>
                    </div>
                    <div class="progressRight" @click="toRouter">
                        <img :src="$imgs['updatePro.png']" alt="">
                        <p>{{ state.home.升级攻略 }}</p>
                    </div>
                </div>
            </van-swipe-item>
        </van-swipe>
        <div class="next" @click="progressBar.next()"></div>
    </div>
</template>

<script setup lang='ts'>
import { ref, onMounted, watch, nextTick } from 'vue'
import { useLang, UseLang } from 'hook'
const { state,lang } = useLang()

const props = defineProps<{
    level: number,
    progress: number,
    rewardConfigs: Array<Record<string, any>>,
    tree_code: string,
    propList: Record<string, Boolean>,
    tree_energy: number,
    tree_total_energy: number,
    demand: number[],
}>()
const emit = defineEmits(['showProp', 'toRouter', 'showAchieve'])
const progressBar = ref()

onMounted(() => {
})

const schedule = ref()
watch(props, () => {
    // 进度条
    nextTick(() => {
        if (!schedule.value[props.level - 1]) return
        schedule.value[props.level - 1].style.left = '-' + (100 - (props.progress * 100)) + '%'
        for (let i = 0; i < props.demand.length; i++) {
            if (i != props.level - 1 && i < props.level - 1) {
                schedule.value[i].style.left = '0%'
            } else if (i > props.level - 1) {
                schedule.value[i].style.left = '-100%'
            }
        }
        progressBar.value.swipeTo(props.level - 1)
    })
}, { immediate: true })

const toRouter = () => {
    emit('toRouter', '/upStrategy')
}
</script>

<style lang='less' scoped>
.progressBox {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .swiperBox {
        width: 60%;
        overflow: hidden;
        height: 130px;
        .sliderList {
            background: linear-gradient(284deg, #9FDF6E 0%, #97DE6A 13%, #3DCE41 69%, #58CF57 100%);
            border-radius: 18px;
            border: 1px solid rgba(255, 255, 255, 0.5);
            height: 130px;

            .progressBar {
                width: 90%;
                height: 40px;
                margin: 20px auto 10px;
                background: linear-gradient(90deg, #51B95B 0%, #298846 100%);
                box-shadow: inset 0px 1px 0px 1px rgba(0, 0, 0, 0.16);
                border-radius: 16px;
                position: relative;
                color: #fff;
                font-size: 24px;
                text-align: center;

                span {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 40px;
                    text-align: center;
                    line-height: 40px;
                    z-index: 9;
                }

                img {
                    position: absolute;
                    right: 0;
                    top: -5px;
                    width: 50px;
                    height: 50px;
                    border-radius: 50%;
                    object-fit: contain; 
                }
                .schedule-mask {
                    width: 100%;
                    height: 40px;
                    border-radius: 16px;
                    overflow: hidden;
                }
                .schedule {
                    width: 100%;
                    height: 40px;
                    background: linear-gradient(180deg, #FECA67 0%, #F8A836 36%, #FB9939 75%, #FD8F3C 100%);
                    box-shadow: inset 0px -1px 0px 1px rgba(255, 171, 87, 0.36);
                    border-radius: 16px;
                    position: relative;
                    left: -100%;
                }

                .acticity {
                    border: 1px solid;
                    background-color: #fff;
                }

                .img-area {
                    img {
                        z-index: 10;
                    }
                    
                }
            }

            .progressText {
                width: 90%;
                margin: 0 auto;
                display: flex;
                justify-content: space-between;
                
                .progressLeftText{
                    font-size: 20px !important;
                }
                .progressLeft {
                    font-family: PingFang TC, PingFang TC;
                    font-weight: 600;
                    font-size: 24px;
                    color: #FFFFFF;
                    font-style: normal;
                    text-transform: none;
                    display: flex;
                    align-items: center;
                    .progressNo {
                        color: #16741E;
                        font-size: 24px;
                        margin-right: 5px;
                    }
                    .progressDes{
                    }
                }

                .progressRight {
                    background: linear-gradient(360deg, #3CBF48 0%, #3DD13F 100%);
                    box-shadow: 0px 1px 0px 1px #38B23C, inset 0px 1 0px 1px #92E898;
                    border-radius: 17px;
                    width: 143px;
                    height: 38px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    img {
                        width: 25px;
                        height: 25px;
                    }

                    p {
                        font-family: PingFang SC, PingFang SC;
                        font-weight: 500;
                        font-size: 24px;
                        color: #FFFFFF;
                    }
                }
            }
        }

    }
    .prev {
        left: 12%;
        top: 30%;
        width: 50px;
        height: 50px;
        background: url('@assets/imgs/rightPro.png') no-repeat;
        background-size: 100% 100%;
    }

    .next {
        right: 12%;
        top: 30%;
        width: 50px;
        height: 50px;
        background: url('@assets/imgs/leftPro.png') no-repeat;
        background-size: 100% 100%;
    }
}

</style>
