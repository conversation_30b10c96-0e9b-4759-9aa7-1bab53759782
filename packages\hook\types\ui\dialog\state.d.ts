import { Dialog } from './dialog';
import { UseDialog } from './type';
export declare const rawState: {
    refs: Map<string, any>;
    dialogMapper: Map<string, UseDialog.DialogRegisterType>;
    dialogMap: Map<string, Dialog<any>>;
    singleDialogMap: Map<string, Dialog<any>>;
};
export declare const state: {
    showList: {
        uuid: string;
        name: string;
        componentName: string;
        props: Record<string, any>;
        opts: {
            maskClose?: boolean | undefined;
            animName?: string | undefined;
            maskAnimName?: string | undefined;
            maskBgColor?: string | undefined;
            alignX?: UseDialog.AlignX | undefined;
            alignY?: UseDialog.AlignY | undefined;
        };
        isLocal: boolean;
        isShowing: boolean;
    }[];
};
