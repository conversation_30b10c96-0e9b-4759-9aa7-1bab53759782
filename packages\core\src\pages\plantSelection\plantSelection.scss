.plantSelection {
    width: 100vw;
    min-height: 100vh;
    background: #22992C;
    padding: 226px 50px 50px 50px;

    .row {
        margin-bottom: 24px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .item {
            width: 310px;
            // height: 552px;
            height: 530px;
            background: #ffffff;
            box-shadow: 0px 6px 0px 2px rgba(0, 0, 0, 0.08);
            border-radius: 64px 16px 16px 16px;
            box-sizing: border-box;
            padding: 16px 14px 16px 16px;
            margin-bottom: 20px;
            position: relative;
            .new{
                position: absolute;
                background: url('@/assets/imgs/plantNew.png') no-repeat;
                background-size: cover;
                width: 78px;
                height: 40px;
                z-index: 2;
                font-weight: 600;
                font-size: 20px;
                color: #FFFFFF;
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                right: 0;
                top: -16px;
                p{
                    margin-left: 10px;
                    margin-top: 10px;
                }
            }
            .img-box {
                position: relative;
                width: 280px;
                height: 280px;
                .vipIcon{
                    position: absolute;
                    left: 0;
                    top: 0;
                    >img{
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 112px;
                        height: 100px;
                    }
                    >p{
                        z-index: 1;
                        position: absolute;
                        left: 24px;
                        top: 13px;
                        font-size: 20px;
                        white-space: nowrap;
                        font-weight: bold;
                        color: #FFFFFF;
                    }
                }
                .VIP {
                    position: absolute;
                    width: 112px;
                    height: 100px;
                }
                .img {
                    width: 280px;
                    height: 280px;
                }
                .tree{
                    width: 170px;
                    position: absolute;
                    bottom: 15px;
                    left: 50%;
                    transform: translate(-50%, 0);
                    .yangzijing{
                        width: 170px;
                        transform: translate(-2px, 0);
                    }
                    .lanhuaying{
                        width: 208px;
                        transform: translate(-25px, 0);
                    }
                    .diaozhonghua{
                        width: 190px;
                        transform: translate(-15px, 0);
                    }
                    .xiuqiuhua{
                        width: 180px;
                        transform: translate(-5px, 0);
                    }
                    .jieguojueming{
                        width: 205px;
                        transform: translate(-15px, 0);
                    }
                    .mumian{
                        width: 180px;
                        transform: translate(-5px, 0);
                    }
                    .yumushu{
                        width: 180px;
                        transform: translate(0,5px);
                    }
                    .shuishirong{
                        width: 225px;
                        transform: translate(-25px,5px);
                    }
                    .huangzhongmu{
                        width: 205px;
                        transform: translate(-10px,0);
                    }
                }
                
                .text {
                    position: absolute;
                    font-size: 20px;
                    font-family: PingFang SC-Bold, PingFang SC;
                    color: #ffffff;
                    line-height: 20px;
                    z-index: 100;
                    top: 16px;
                    left: 30px;
                }
            }
            .info {
                margin-top: 16px;
                margin-left: 10px;
                .name {
                    font-size: 26px;
                    font-family: PingFang SC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #4e5b7e;
                    line-height: 28px;
                    // height: 28px;
                }
                .kind {
                    font-size: 22px;
                    font-family: PingFang SC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #a7aec3;
                    line-height: 28px;
                    margin-top: 5px;
                }
            }
            .price {
                position: absolute;
                bottom: 90px;
                margin-left: 10px;
                margin-top: 35px;
                font-size: 22px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #4e5b7e;
                line-height: 28px;
                .value {
                    .star {
                        width: 22px;
                        height: 20px;
                        position: relative;
                        top: 2px;
                    }
                }
            }
            .btn {
                position: absolute;
                width: 280px;
                bottom: 30px;
                margin-top: 16px;
                height: 44px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 28px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                line-height: 28px;
                &.can {
                    background: url('@imgs/plantSelection/btn.png') no-repeat center center;
                    background-size: 100% 100%;
                }
                &.cannot {
                    background: linear-gradient(180deg, #d3d3d3 0%, #848484 100%);
                    box-shadow: 0px 4px 0px 2px #7c7c7c, inset 0px 2px 0px 2px #f0f0f0;
                    border-radius: 24px 8px 24px 8px;
                }
                &.log{
                    background: linear-gradient(180deg, #85E277 0%, #3EC04A 100%);
                    box-shadow: 0px 4px 0px 2px rgba(56,178,60,1), inset 0px 2px 0px 2px rgba(146,232,152,1);
                    border-radius: 24px 8px 24px 8px;
                }
            }
        }
    }
}