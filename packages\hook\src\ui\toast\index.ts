import { createApp } from 'vue'
import ToastApp from './ToastApp.vue'
import { state } from './state'
import { UseToast } from './type'

const toastApp = createApp(ToastApp)
toastApp.mount('#app-toast')

const defaultOption: Partial<UseToast.ToastOption> = {
  type: 'center',
  duration: 2500,
  wordWrap: true,
  width: 'auto'
}

let toastTimer = -1

function toast(tip: string, config: Partial<UseToast.ToastOption> = {}, icon = '', secondTip = '') {
  let option: Partial<UseToast.ToastOption> = {}
  Object.assign(option, defaultOption, config || {})

  if (toastTimer !== -1) {
    clearTimeout(toastTimer)
    toastTimer = -1
  }
  state.tip = tip
  state.wordWrap = option.wordWrap || false
  state.type = option.type || 'center'
  state.icon = icon
  state.secondTip = secondTip
  state.extStyle.width = option.width || ''
  state.show = true
  toastTimer = window.setTimeout(() => {
    state.show = false
    toastTimer = -1
  }, option.duration)
}
toast.top = (tip: string, config: Partial<UseToast.ToastOption> = {}) =>
  toast(tip, Object.assign(config, { type: 'top' }))
toast.center = (tip: string, config: Partial<UseToast.ToastOption> = {}) =>
  toast(tip, Object.assign(config, { type: 'center' }))
toast.bottom = (tip: string, config: Partial<UseToast.ToastOption> = {}) =>
  toast(tip, Object.assign(config, { type: 'bottom' }))

toast.withIcon = (icon: string, tip: string, secondTip: string, config: Partial<UseToast.ToastOption> = {}) =>
  toast(tip, Object.assign(config, {}), icon, secondTip)

export function useToast() {
  return {
    app: toastApp,
    toast
  }
}
