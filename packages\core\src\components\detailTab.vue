<template>
    <div class="detailImgTab">
        <div class="control">
            <div class="left" @click="changeTab(false)">
                <img :src="leftImg" />
            </div>
            <div class="right" @click="changeTab(true)">
                <img :src="rightImg" />
            </div>
        </div>
        <!--在content下直接添加元素即可自动完成切换-->
        <div class="content" ref="tabContent">
            <div class="ach"><achieve type="big" :treeCode="$attrs.treeCode" /></div>
            <div class="ach"><nftDetailImg :treeCode="$attrs.treeCode"></nftDetailImg></div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, defineEmits } from 'vue'
import achieve from './achieve.vue'
import nftDetailImg from './nftDetailImg.vue'
import { imgs } from '@/assets/imgs'
const emit = defineEmits(['changetab'])

const tabContent = ref()
/** tab最大值 */
let tabMax = 0
/** 当前显示的tab */
let curTabIndex = 0
/** 上一个显示的tab */
let preTabIndex = 0

const leftImg = ref('')
const rightImg = ref('')

/** 左方向激活的箭头 */
const leftActiveImg = imgs['NTFDetail/arrow_left_active.png']
/** 右方向激活的箭头 */
const rightActiveImg = imgs['NTFDetail/arrow_right_active.png']
/** 左方向禁止的箭头 */
const leftDisabledImg = imgs['NTFDetail/arrow_left_disabled.png']
/** 左方向禁止的箭头 */
const rightDisabledImg = imgs['NTFDetail/arrow_right_disabled.png']

onMounted(() => {
    // 初始化详情图tab
    initDetailImageTab()
})
/**
 * 初始化详情图
 */
const initDetailImageTab = () => {
    // 获取子节点个数作为tab的长度
    tabMax = tabContent.value.childNodes.length - 1
    if (tabMax <= 0) {
        return
    }
    // 对第一个子节点添加active类作为显示
    tabContent.value.childNodes[curTabIndex].classList.add('active')

    // 根据当前位置设置箭头状态
    leftImg.value = leftDisabledImg
    rightImg.value = rightActiveImg
}

/**
 * 切换植物或者nft详情图
 * @param flag 切换标志，false为左，true为右
 */
const changeTab = (flag: boolean) => {
    if (!flag) {
        // 往左
        if (curTabIndex <= 0) {
            return
        }
        preTabIndex = curTabIndex
        curTabIndex -= 1
    } else {
        // 往右
        if (curTabIndex >= tabMax) {
            return
        }
        preTabIndex = curTabIndex
        curTabIndex += 1
    }

    // 根据当前的位置设置箭头状态
    if (curTabIndex <= 0) {
        leftImg.value = leftDisabledImg
        if (curTabIndex >= tabMax) {
            rightImg.value = rightDisabledImg
        } else {
            rightImg.value = rightActiveImg
        }
    } else if (curTabIndex >= tabMax) {
        rightImg.value = rightDisabledImg
        if (curTabIndex <= 0) {
            leftImg.value = leftDisabledImg
        } else {
            leftImg.value = leftActiveImg
        }
    } else {
        leftImg.value = leftActiveImg
        rightImg.value = rightActiveImg
    }

    emit('changetab', curTabIndex)

    tabContent.value.childNodes[preTabIndex].classList.remove('active')
    tabContent.value.childNodes[curTabIndex].classList.add('active')
}
</script>

<style lang="less">
.detailImgTab {
    .ach {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 230px;
    }

    .content {
        > * {
            display: none;
        }

        .active {
            display: unset;
        }
    }
    .control {
        .left {
            position: absolute;
            left: 55px;
            top: 320px;
        }
        .right {
            position: absolute;
            left: 475px;
            top: 320px;
        }
    }
}
</style>
