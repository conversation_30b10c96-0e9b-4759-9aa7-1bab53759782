<template>
    <div :class="`treeStatus ${treeCode}`" @click.stop="shake()">
        <!-- 树本体 -->
        <div :class="`flex s${level}`">
            <!-- <img class="img" v-if="treeCode" :src="$imgs[`TT/state/${treeCode}/s${props.level}.png`]" alt="" /> -->
            <van-image :src="$imgs[`TT/state/${treeCode}/s${props.level}.png`]" class="img" v-if="treeCode" @load="loadFun" :show-loading="false" />
            <!-- 挂饰 -->
            <img v-show="props.level>=3 && propStore.propsConfig['挂饰'][0]" :class="`hanging first ${propStore.propsConfig['挂饰'][0]?.code}`" :src="$imgs[`props/挂饰/${propStore.propsConfig['挂饰'][0]?.code}.png`]" alt="">
            <img v-show="props.level >= 6 && propStore.propsConfig['挂饰'][1]" :class="`hanging second ${propStore.propsConfig['挂饰'][1]?.code}`" :src="$imgs[`props/挂饰/${propStore.propsConfig['挂饰'][1]?.code}.png`]" alt="">
            <img v-show="props.level >= 6 && propStore.propsConfig['挂饰'][2]" :class="`hanging third ${propStore.propsConfig['挂饰'][2]?.code}`" :src="$imgs[`props/挂饰/${propStore.propsConfig['挂饰'][2]?.code}.png`]" alt="">
        </div>

    </div>
</template>

<script lang="ts" setup>
import { usePropStore } from '@/store'
import { ref,watch } from "vue";
const propStore = usePropStore()
let emit = defineEmits(['treeTalk'])
const props = defineProps({
    level: {
        type: Number,
        required: true
    },
    prop: {
        type: Array,
        required: false
    },
    shake: {
        //点击是否抖动
        type: Boolean,
        default: true,
        required: false
    },
    treeCode: {
        type: String,
        required: true
    }
})
let level = ref(props.level)

const loadFun = ()=>{
    level.value = props.level
}

const shake = () => {
    if(props.shake){
        emit('treeTalk')
    }
}
</script>

<style lang="less" scoped>
@keyframes fdsseq { 
    from {
        transform: scale(0);
    }
    to { 
        transform: scale(1);
     }
}
@keyframes fdssep { 
    from {
        transform: scale(1);
    }
    to { 
        transform: scale(0);
     }
}
// .undefined{
//     animation: fdssep .5s forwards;
// }
.treeStatus {
    display: flex;
    position: relative;
    flex-direction: column;
    justify-content: end;
}
.img{
    width: 100%;
    flex-shrink: 0;
}
.flex{
    display: flex;
    flex-direction: column;
    justify-content: end;
    flex-shrink: 0;
}

.hanging{
    position: absolute;
    animation: fdsseq .3s forwards;
}
.item1{
    width: 36px;
}
.item2{
    width: 44px;
}
.item3{
    width: 36px;
}

.item4{
    width: 36px;
}
.item5{
    width: 36px;
}
.item6{
    width: 76px;
}
.item7{
    width: 76px;
}
.item8{
    width: 76px;
}
.item9{
    width: 76px;
}
.item10{
    width: 76px;
}
.item11{
    width: 56px;
}
.item12{
    width: 46px;
}
.item13{
    width: 76px;
}
.item14{
    width: 46px;
}
.item15{
    width: 46px;
}
.item16{
    width: 76px;
}
.item17{
    width: 76px;
}
.item18{
    width: 46px;
}
.item19{
    width: 46px;
}
.item20{
    width: 76px;
}
.item21{
    width: 76px;
}
.item22{
    width: 46px;
}
.item23{
    width: 46px;
}
.item24{
    width: 46px;
}
.item25{
    width: 46px;
}
.item26{
    width: 46px;
}

.diaozhonghua{
    .s1 {
        width: 48px;
        transform: translate(10px, 0);
    }
    .s2 {
        width: 64px;
    }
    .s3 {
        width: 59px;
        .first{
            width: auto;
            height: 85px;
            top: 30px;
            left: -15px;
        }
    }
    .s4 {
        width: 85px;
        .first{
            top: 30px;
            left: -15px;
        }
    }
    .s5 {
        width: 173px;
        transform: translate(30px, 0);
        .first{
            top: 30px;
            left: -15px;
        }
    }
    .s6 {
        width: 256px;
        transform: translate(-15px, 0);
        .first{
            top: 130px;
            right: 15px;
        }
        .second{
            top: 100px;
            left: 40px;
        }
        .third{
            top: 90px;
            left: 100px;
        }
    }
    .s7 {
        width: 396px;
        transform: translate(-10px, 0);
        .first{
            top: 170px;
            right: 15px;
        }
        .second{
            top: 140px;
            left: 40px;
        }
        .third{
            top: 140px;
            left: 120px;
        }
    }
    .s8 {
        width: 428px;
        transform: translate(-10px, 0);
        .first{
            top: 180px;
            right: 15px;
        }
        .second{
            top: 190px;
            left: 40px;
        }
        .third{
            top: 180px;
            left: 120px;
        }
    }
    .s9 {
        width: 428px;
        transform: translate(-10px, 0);
        .first{
            top: 180px;
            right: 15px;
        }
        .second{
            top: 190px;
            left: 40px;
        }
        .third{
            top: 180px;
            left: 120px;
        }
    }
}

.jieguojueming{
    .s1 {
        width: 43px;
    }
    .s2 {
        width: 75px;
        transform: translate(-6px, 0);
    }
    .s3 {
        width: 83px;
        transform: translate(-6px, 0);
        .first{
            width: auto;
            height: 85px;
            top: 30px;
            left: -15px;
        }
    }
    .s4 {
        width: 94px;
        transform: translate(6px, 0);
        .first{
            width: auto;
            height: 100px;
            top: 30px;
            left: -15px;
        }
    }
    .s5 {
        width: 141px;
        transform: translate(6px, 0);
        .first{
            top: 30px;
            left: -15px;
        }
    }
    .s6 {
        width: 291px;
        transform: translate(11px, 0);
        .first{
            top: 110px;
            right: 15px;
        }
        .second{
            top: 120px;
            left: 10px;
        }
        .third{
            top: 110px;
            left: 80px;
        }
    }
    .s7 {
        width: 299px;
        transform: translate(6px, 0);
        .first{
            top: 140px;
            right: 15px;
        }
        .second{
            top: 150px;
            left: 30px;
        }
        .third{
            top: 140px;
            left: 110px;
        }
    }
    .s8 {
        width: 384px;
        transform: translate(11px, 0);
        .first{
            top: 140px;
            right: 15px;
        }
        .second{
            top: 150px;
            left: 30px;
        }
        .third{
            top: 140px;
            left: 120px;
        }
    }
    .s9 {
        width: 425px;
        .first{
            top: 180px;
            right: 15px;
        }
        .second{
            top: 190px;
            left: 40px;
        }
        .third{
            top: 190px;
            left: 140px;
        }
    }
}

.yangzijing{
    .s1 {
        width: 50px;
        transform: translate(10px, 0);
    }
    .s2 {
        width: 43px;
        transform: translate(6px, 0);
    }
    .s3 {
        width: 92px;
        transform: translate(0px, 0);
        .first{
            width: auto;
            height: 85px;
            top: 15px;
            right: 40px;
        }
    }
    .s4 {
        width: 134px;
        transform: translate(-7px, 0);
        .first{
            top: 15px;
            left: 0px;
        }
    }
    .s5 {
        width: 192px;
        .first{
            top: 50px;
            left: 10px;
        }
    }
    .s6 {
        width: 308px;
        .first{
            top: 130px;
            right: 15px;
        }
        .second{
            top: 130px;
            left: 40px;
        }
        .third{
            top: 110px;
            left: 100px;
        }
    }
    .s7 {
        width: 308px;
        transform: translate(-2px, 0);
        .first{
            top: 130px;
            right: 15px;
        }
        .second{
            top: 140px;
            left: 40px;
        }
        .third{
            top: 140px;
            left: 110px;
        }
    }
    .s8 {
        width: 356px;
        transform: translate(-6px, 0);
        .first{
            top: 130px;
            right: 15px;
        }
        .second{
            top: 150px;
            left: 40px;
        }
        .third{
            top: 140px;
            left: 110px;
        }
    }
    .s9 {
        width: 345px;
        .first{
            top: 130px;
            right: 15px;
        }
        .second{
            top: 150px;
            left: 40px;
        }
        .third{
            top: 140px;
            left: 110px;
        }
    }
}

.xiuqiuhua{
    .s1 {
        width: 48px;
        transform: translate(-4px, 0);
    }
    .s2 {
        width: 78px;
    }
    .s3 {
        width: 90px;
        transform: translate(2px, 0);
        .first{
            width: auto;
            height: 85px;
            top: 30px;
            left: -15px;
        }
    }
    .s4 {
        width: 120px;
        transform: translate(3px, 0);
        .first{
            top: 30px;
            left: -20px;
        }
    }
    .s5 {
        width: 135px;
        transform: translate(8px, 0);
        .first{
            top: 40px;
            left: -3px;
        }
    }
    .s6 {
        width: 173px;
        transform: translate(14px, 0);
        .first{
            top: 80px;
            right: -15px;
        }
        .second{
            top: 60px;
            left: 30px;
        }
        .third{
            top: 100px;
            left: 80px;
        }
    }
    .s7 {
        width: 227px;
        transform: translate(-2px, 0);
        .first{
            top: 80px;
            right: 15px;
        }
        .second{
            top: 100px;
            left: 30px;
        }
        .third{
            top: 100px;
            left: 100px;
        }
    }
    .s8 {
        width: 265px;
        transform: translate(6px, 0);
        .first{
            top: 100px;
            right: 15px;
        }
        .second{
            top: 100px;
            left: 30px;
        }
        .third{
            top: 120px;
            left: 110px;
        }
    }
    .s9 {
        width: 265px;
        .first{
            top: 100px;
            right: 15px;
        }
        .second{
            top: 100px;
            left: 30px;
        }
        .third{
            top: 120px;
            left: 110px;
        }
    }
}

.lanhuaying{
    .s1 {
        width: 36px;
        transform: translate(4px, 0);
    }
    .s2 {
        width: 60px;
    }
    .s3 {
        width: 87px;
        transform: translate(10px, 0);
        .first{
            width: auto;
            height: 85px;
            top: 15px;
            right: -15px;
        }
    }
    .s4 {
        width: 188px;
        transform: translate(7px, 0);
        .first{
            top: 45px;
            right: 15px;
        }
    }
    .s5 {
        width: 272px;
        .first{
            top: 80px;
            right: 15px;
        }
    }
    .s6 {
        width: 350px;
        transform: translate(5px, 0);
        .first{
            top: 170px;
            right: 15px;
        }
        .second{
            top: 180px;
            left: 40px;
        }
        .third{
            top: 140px;
            left: 100px;
        }
    }
    .s7 {
        width: 355px;
        transform: translate(6px, 0);
        .first{
            top: 170px;
            right: 15px;
        }
        .second{
            top: 180px;
            left: 40px;
        }
        .third{
            top: 140px;
            left: 100px;
        }
    }
    .s8 {
        width: 412px;
        transform: translate(-13px, 0);
        .first{
            top: 180px;
            right: 15px;
        }
        .second{
            top: 230px;
            left: 60px;
        }
        .third{
            top: 170px;
            left: 140px;
        }
    }
    .s9 {
        width: 412px;
        transform: translate(-13px, 0);
        .first{
            top: 180px;
            right: 15px;
        }
        .second{
            top: 230px;
            left: 60px;
        }
        .third{
            top: 170px;
            left: 140px;
        }
    }
}

.mumian{
    .s1{
        width: 64px;
        transform: translate(10px,5px);
    }
    .s2{
        width: 80px;
        transform: translate(5px,5px);
    }
    .s3{
        width: 86px;
        transform: translate(0px,0px);
        .first{
            width: auto;
            height: 85px;
            top: 60px;
            left: 40px;
        }
    }
    .s4{
        width: 138px;
        transform: translate(5px,0px);
        .first{
            top: 80px;
            left: 15px;
        }
    }
    .s5{
        width: 152px;
        transform: translate(0,0px);
        .first{
            top: 90px;
            left: 15px;
        }
    }
    .s6{
        width: 354px;
        transform: translate(5px,5px);
        .first{
            top: 130px;
            right: 15px;
        }
        .second{
            top: 100px;
            left: 40px;
        }
        .third{
            top: 90px;
            left: 100px;
        }
    }
    .s7{
        width: 354px;
        transform: translate(10px,5px);
        .first{
            top: 170px;
            right: 15px;
        }
        .second{
            top: 140px;
            left: 40px;
        }
        .third{
            top: 140px;
            left: 120px;
        }
    }
    .s8{
        width: 412px;
        transform: translate(0,0px);
        .first{
            top: 180px;
            right: 15px;
        }
        .second{
            top: 170px;
            left: 40px;
        }
        .third{
            top: 180px;
            left: 120px;
        }
    }
    .s9{
        width: 412px;
        transform: translate(5px,5px);
        .first{
            top: 180px;
            right: 15px;
        }
        .second{
            top: 170px;
            left: 40px;
        }
        .third{
            top: 180px;
            left: 120px;
        }
    }
}
.yumushu{
    .s1{
        width: 64px;
        transform: translate(0,5px);
    }
    .s2{
        width: 80px;
        transform: translate(0,5px);
    }
    .s3{
        width: 110px;
        transform: translate(-5px,0px);
        .first{
            width: auto;
            height: 85px;
            top: 70px;
            left: 30px;
        }
    }
    .s4{
        width: 116px;
        transform: translate(0px,5px);
        .first{
            top: 80px;
            left: 30px;
        }
    }
    .s5{
        width: 188px;
        transform: translate(15px,0px);
        .first{
            top: 70px;
            left: 15px;
        }
    }
    .s6{
        width: 354px;
        transform: translate(5px,0px);
        .first{
            top: 50px;
            right: 40px;
        }
        .second{
            top: 100px;
            left: 40px;
        }
        .third{
            top: 90px;
            left: 100px;
        }
    }
    .s7{
        width: 354px;
        transform: translate(0,5px);
        .first{
            top: 130px;
            right: 15px;
        }
        .second{
            top: 140px;
            left: 40px;
        }
        .third{
            top: 140px;
            left: 120px;
        }
    }
    .s8{
        width: 354px;
        transform: translate(10px,0px);
        .first{
            top: 150px;
            right: 15px;
        }
        .second{
            top: 180px;
            left: 40px;
        }
        .third{
            top: 170px;
            left: 120px;
        }
    }
    .s9{
        width: 344px;
        transform: translate(10px,0px);
        .first{
            top: 150px;
            right: 15px;
        }
        .second{
            top: 180px;
            left: 40px;
        }
        .third{
            top: 170px;
            left: 120px;
        }
    }
}
.shuishirong{
    .s1{
        width: 64px;
        transform: translate(0,5px);
    }
    .s2{
        width: 80px;
        transform: translate(0,5px);
    }
    .s3{
        width: 86px;
        transform: translate(0,0px);
        .first{
            width: auto;
            height: 85px;
            top: 40px;
            left: 30px;
        }
    }
    .s4{
        width: 138px;
        transform: translate(0px,-1px);
        .first{
            top: 20px;
            left: 15px;
        }
    }
    .s5{
        width: 152px;
        transform: translate(0,0px);
        .first{
            top: 70px;
            left: 15px;
        }
    }
    .s6{
        width: 354px;
        transform: translate(0,0px);
        .first{
            top: 130px;
            right: 100px;
        }
        .second{
            top: 160px;
            left: 100px;
        }
        .third{
            top: 90px;
            left: 100px;
        }
    }
    .s7{
        width: 354px;
        transform: translate(5px,5px);
        .first{
            top: 130px;
            right: 80px;
        }
        .second{
            top: 140px;
            left: 60px;
        }
        .third{
            top: 140px;
            left: 120px;
        }
    }
    .s8{
        width: 412px;
        transform: translate(0px,0px);
        .first{
            top: 130px;
            right: 100px;
        }
        .second{
            top: 140px;
            left: 60px;
        }
        .third{
            top: 160px;
            left: 140px;
        }
    }
    .s9{
        width: 412px;
        transform: translate(0,0px);
        .first{
            top: 150px;
            right: 80px;
        }
        .second{
            top: 150px;
            left: 80px;
        }
        .third{
            top: 160px;
            left: 160px;
        }
    }
}
.huangzhongmu{
    .s1{
        width: 76px;
        transform: translate(5px,5px);
    }
    .s2{
        width: 80px;
        transform: translate(5px,5px);
    }
    .s3{
        width: 92px;
        transform: translate(0px,0px);
        .first{
            width: auto;
            height: 85px;
            top: 20px;
            left: 10px;
        }
    }
    .s4{
        width: 138px;
        transform: translate(0px,0px);
        .first{
            top: 70px;
            left: 20px;
        }
    }
    .s5{
        width: 152px;
        transform: translate(0px,0px);
        .first{
            top: 100px;
            left: 15px;
        }
    }
    .s6{
        width: 354px;
        transform: translate(5px,0px);
        .first{
            top: 80px;
            right: 60px;
        }
        .second{
            top: 100px;
            left: 80px;
        }
        .third{
            top: 150px;
            left: 120px;
        }
    }
    .s7{
        width: 354px;
        transform: translate(5px,0px);
        .first{
            top: 130px;
            right: 60px;
        }
        .second{
            top: 110px;
            left: 60px;
        }
        .third{
            top: 140px;
            left: 120px;
        }
    }
    .s8{
        width: 412px;
        transform: translate(10px,0px);
        .first{
            top: 120px;
            right: 60px;
        }
        .second{
            top: 140px;
            left: 60px;
        }
        .third{
            top: 120px;
            left: 120px;
        }
    }
    .s9{
        width: 412px;
        transform: translate(10px,0px);
        .first{
            top: 150px;
            right: 40px;
        }
        .second{
            top: 180px;
            left: 40px;
        }
        .third{
            top: 170px;
            left: 120px;
        }
    }
}
</style>
