import axios from 'axios'

axios.defaults.timeout = 100000

axios.interceptors.response.use(
    function (response) {
        console.log(response.data)
        return response
    },
    function (error) {
        console.log(error)
        return Promise.reject(error)
    }
)

const get = function (url: string, params?: object, loading?: any) {
    return new Promise((resolve, reject) => {
        axios
            .get(url, { params })
            .then((res) => {
                resolve(res.data)
            })
            .catch((err) => {
                reject(err)
            })
    })
}

const post = function (url: string, data: object) {
    return new Promise((resolve, reject) => {
        axios
            .post(url, data)
            .then((res) => {
                resolve(res.data)
            })
            .catch((err) => {
                reject(err)
            })
    })
}

export default { axios, get, post }
