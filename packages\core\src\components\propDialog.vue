<template>
    <div class="propBox propBox1" >
        <p v-if="props.isInfo" class="isInfo">{{ state.dialog.达到x期可获得.replace('{x}', $lang.plantStage[noIndex]) }}</p>
        <p v-if="!props.isInfo" class="isInfo">{{ state.dialog.稀有度 }}</p>
        <p class="back" v-if="props.back">{{state.dialog.点击屏幕返回}}</p>

        <div class="value">
            <img
                v-for="i in Number(propInstance.propValue)"
                :key="i"
                class="star"
                :src="$imgs['plantSelection/star.png']"
            />
        </div>

        <div class="image">
            <img :src="propInstance.icon" alt="" />
        </div>

        <p class="name">{{ propInstance.propName }}</p>
        <div class="tips">{{ getText(propInstance.code) }}</div>
    </div>
</template>

<script lang="ts" setup>
import { useDialog, useLang } from 'hook';
const { state } = useLang() 
const props = defineProps({
    isInfo: {
        type: Boolean,
        required: false
    },

    //道具对象
    propInstance: {
        type: Object,
        required: true
    },

    //该植物的第几期
    noIndex: {
        type: Number,
        default: 0
    },
    back:{
        type:Boolean,
        default:true
    }
})

const getText = (code:string) => {
    console.log(code);
    switch (code) {
        case 'item22':
            return state.shopNft.在LinkNFT頁購買洋紫荊限量版NFT即可獲得
        case 'item23':
            return state.shopNft.在LinkNFT頁購買繡球花限量版NFT即可獲得
        case 'item24':
            return state.shopNft.在LinkNFT頁購買吊鐘花限量版NFT即可獲得
        case 'item25':
            return state.shopNft.在LinkNFT頁購買節果決明限量版NFT即可獲得
        case 'item26':
            return state.shopNft.在LinkNFT頁購買藍花楹限量版NFT即可獲得
        default:
            break;
    }
}

</script>

<style lang="less" scoped>
.propBox {
    width: 422px;
    min-height: 560px;
    background: url('@assets/TT/prop1.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    box-sizing: border-box;
    padding-bottom: 20px;
    .isInfo {
        height: 70px;
        font-size: 28px;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #ffffff;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .back {
        white-space: nowrap;
        font-size: 28px;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 28px;
        position: absolute;
        bottom: -76px;
        left: 50%;
        transform: translateX(-50%);
    }
    .value {
        display: flex;
        justify-content: center;
        margin-top: 30px;
        .star {
            .wh(44,42);
            display: block;
            margin-right: 10px;
            &:last-of-type {
                margin-right: 0px;
            }
        }
    }
    .image {
        margin: 80px auto 0;
        width: 192px;
        height: 192px;
        background: #EFEFEF;
        opacity: 1;
        border: 8px solid #E2E2E2;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        img{
            max-width: 90%;
            max-height: 90%;
        }
    }
    .name {
        font-size: 32px;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #4e5b7e;
        text-align: center;
        line-height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100px;
        margin-top: 16px;
    }
    .tips{
        font-weight: 400;
        font-size: 20px;
        color: #4E5B7E;
        line-height: 28px;
        text-align: center;
        width: 252px;
        margin: 4px auto 0;
    }
}
.propBox1 {
    background: url('@assets/TT/prop.png') no-repeat;
    background-size: 100% 100%;
}
</style>
