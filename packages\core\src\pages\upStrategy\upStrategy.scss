.home{
    width: 100vw;
    min-height: 100vh;
    padding-top: 230px;
    box-sizing: border-box;
    position: relative;
    font-family: PingFang TC-Medium, PingFang TC;
    font-weight: 500;
    background: #4F3E24;
    display: flex;
    flex-direction: column;
    align-items: center;

    overflow: hidden;
    .banner{
        z-index: 0;
        position: absolute;
        top:  -15px;
        width: 100%;
        height: 488px;
        background: url('@assets/TT/treeRoot.png') no-repeat;
        background-size: 100% 100%;
    }
    .bubbleAlerts{
        z-index: 1;
        width: 686px;
        height: 120px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 24px;
        color: #4B4B4B;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        margin-bottom: 40px;
        img{
            width: 84px;
            height: 84px;
            margin-right: 30px;
        }

        .bubble{
            flex: 1;
            line-height: 32px;
            padding: 20px;
            padding-left: 32px;
            background: url('@assets/imgs/bubble.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .title{
        min-width: 316px;
        height: 80px;
        padding-top: 12px;
        padding-left: 20px;
        padding-right: 20px;
        box-sizing: border-box;
        background: url('@assets/TT/Icon/greentitle.png') no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        font-size: 28px;
        font-family: PingFang SC-Bold, PingFang SC;
        color: #FFFFFF;
        display: flex;
        justify-content: center;
        margin-bottom: 25px;
    }
    .topBox{
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-bottom: 40px;
        .walk{
            // padding: 0 50px;
            box-sizing: border-box;
            overflow: hidden;
            width: 686px;
            display: flex;
            // flex-direction: column;
            // justify-content: space-between;
            // align-items: center;
            flex-wrap: wrap;
            .walkitem{
                flex-shrink: 0;
                display: flex;
                align-items: flex-start;
                width: 50%;
                margin-bottom: 32px;
                position: relative;
                .icon{
                    width: 80px;
                    height: 80px;
                }
                .text{
                    margin-left: 15px;
                    .top{
                        width: 220px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        font-size: 24px;
                        font-weight: 600;
                        color: #FFFFFF;
                        span {
                            display: -webkit-box;
                            overflow: hidden;
                            -webkit-box-orient: vertical; 
                            -webkit-line-clamp: 1; 
                        }
                        img{
                            width: 20px;
                            height: 20px;
                            margin-left:8px ;
                        }
                    }
                    .bot{
                        // max-width: 250px;
                        font-size: 24px;
                        font-weight: 400;
                        color: #FFFFFF;
                        opacity: 0.6;
                        display: -webkit-box;
                        overflow: hidden;
                        -webkit-box-orient: vertical; 
                        -webkit-line-clamp: 2; 
                    }
                }
                .new{
                    position: absolute;
                    height: 24px;
                    padding: 0 5px;
                    background: #E03131;
                    border-radius: 110px;
                    top: 0;
                    left: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    span{
                        font-size: 14px;
                        font-family: PingFang HK-Semibold, PingFang HK;
                        font-weight: 600;
                        color: #FFFFFF;
                    }
                }
            }
        }
        .tips{
            width: 650px;
            // height: 184px;
            background: rgba(70, 55, 32, 0.7);
            border-radius: 48px 24px 48px 24px;
            position: relative;
            // padding: 84px 30px 30px 46px;
            padding: 30px 30px 30px 46px;
            // margin-top: 40px;
            margin-bottom: 32px;
            .ttips{
                position: absolute;
                top: 24px;
                left: 46px;
                padding: 4px 48px;
                background: #574731;
                border-radius: 32px 8px 32px 0px;
                font-size: 24px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #FFFFFF;
            }
            .ttext{
                font-size: 24px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #FFFFFF;
                opacity: 0.6;
                white-space: pre-line;
            }
        }
        .cardInfo{
            display: flex;
            flex-direction: column;
            // align-items: center;
            padding-left: 224px;
            padding-right: 20px;
            padding-bottom: 30px;
            z-index: 1;
            width: 686px;
            // height: 216px;
            background: linear-gradient(131deg, #22B728 0%, #2E8D30 100%);
            border-radius: 24px 24px 24px 24px;
            position: relative;
            .card22{
                position: absolute;
                width: 220px;
                height: 220px;
                left: 0;
                top: 0;
            }
            .titleC{
                margin-top: 20px;
                font-size: 32px;
                font-weight: 600;
                color: #FFFFFF;
            }
            .contentC{
                margin-top: 10px;
                font-size: 20px;
                font-weight: 400;
                color: #8FE28C;
                line-height: 28px;
            }
            .shouBtn{
                margin-top: 20px;
                padding: 5px 20px;
                align-self: flex-start;
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 0px;
                border-radius: 22px 22px 22px 22px;
                border: 2px solid #FFFFFF;
                bottom: 20px;
                >p{
                    display: inline-block;
                    font-size: 20px;
                    font-family: PingFang SC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #FFFFFF;
                }
                >img{
                    display: inline-block;
                    width: 24px;
                    height: 12px;
                    &.fanzhuan{
                        transform: rotate(180deg);
                    }
                }
            }
        }
        .cardInfoo{
            padding: 45px 0 26px 0;
            transform: translate(0, -18px);
            width: 686px;
            // height: 386px;
            // background: #A4DBA6;
            border-radius: 0px 0px 24px 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
            .cardInfoo-item{
                margin-bottom: 16px;
                width: 656px;
                // height: 164px;
                padding-top: 20px;
                padding-bottom: 20px;
                background: #FFFFFF;
                border-radius: 16px 16px 16px 16px;
                position: relative;
                padding-left: 112px;
                &:last-child{
                    margin-bottom: 0;
                }
                >img{
                    position: absolute;
                    width: 84px;
                    height: 84px;
                    left: 16px;
                    top: 24px;
                }
            }
            .titleU{
                font-size: 32px;
                font-weight: 600;
                color: #4B4B4B;
                margin-bottom: 10px;
            }
            .contentU{
                font-size: 24px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #4B4B4B;
                line-height: 32px;
                white-space: pre-line;
                opacity: 0.7;
            }
            
        }
        .cardInfo2{
            margin-top: 40px;
            width: 686px;
            padding-top: 50px;
            background: #E4F7E5;
            border-radius: 24px 24px 24px 24px;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;

            .prompt{
                font-family: PingFang SC, PingFang SC;
                font-weight: 500;
                font-size: 24px;
                color: #4E5B7E;
                line-height: 36px;
                text-align: center;
                padding-left: 20px;
                padding-right: 20px;
            }
        }
        .taskCard{
            margin: 76px 32px 0;
            width: 688px;
            // height: 294px;
            padding-bottom: 20px;
            background: #FFFFFF;
            border-radius: 24px 24px 24px 24px;
            opacity: 1;
            border: 2px solid #707070;
            position: relative;

            .taskDes{
                display: flex;
                margin-top: 68px;
                .task{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    margin-right: 90px;
                    &:last-child{
                        margin-right: 0;
                    }
                    &:first-child{
                        margin-left: 44px;
                    }
                    img{
                        width: 112px;
                        height: 108px;
                    }
                    p{
                        font-size: 24px;
                        font-family: PingFang SC, PingFang SC;
                        font-weight: 500;
                        color: #4B4B4B;
                        width: 144px;
                        text-align: center;
                    }
                }
                .arrow{
                    display: flex;
                    position: absolute;
                    top: 104px;
                    left: 194px;
                    img{
                        width: 52px;
                        height: 36px;
                        &:first-child{
                            margin-right: 176px;
                        }
                    }
                }
            }
        }

        .taskHead{
            background: url('@assets/imgs/taskHead.png') no-repeat;
            background-size: 100% 100%;
            font-size: 28px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            color: #FFFFFF;
            width: 440px;
            height: 68px;
            line-height: 68px;
            text-align: center;
            position: absolute;
            top: -34px;
            left: 50%;
            transform: translate(-50%);
        }
        .resource{
            margin-top: 24px;
            width: 100%;
            padding: 0 50px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            .line{
                flex: 1;
                height: 0.5px;
                opacity: 0.12;
                border-top: 2px solid #FFFFFF;
            }
            .retext{
                margin: 0 14px;
                font-size: 22px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #FFFFFF;
                line-height: 36px;
                opacity: 0.6;
            }
        }
    }
    .botBox{
        flex: 1;
        padding: 50px 0;
        width: 100%;
        background: #FFFFFF;
        border-radius: 48px 48px 0px 0px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .pic1{
            position: relative;
            width: 700px;
            height: 550px;
            .status{
                position: absolute;
                top: 0;
                left: 104px;
                display: flex;
                align-items: center;
                flex-direction: column;
                
                img{
                    width: 112px;
                }
                p{
                    position: absolute;
                    top: 100%;
                    font-size: 24px;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #4E5B7E;
                    width: 280px;
                    text-align: center;
                    span{
                        transform: translate(0, -2px);
                        display: inline-block;  
                        width: 38px;
                        height: 38px;
                        border-radius: 50%;
                        background: #3CCB42;
                        font-size: 24px;
                        font-family: Arial-Bold Italic, Arial;
                        color: #FFFFFF;
                        line-height: 38px;
                        font-style: italic;
                        vertical-align: middle;
                        text-align: center;
                    }
                }
            }
            .status2{
                top: 102px;
                left: 330px;
            }
            .status3{
                top: 150px;
                left: 560px;
            }
            .status4{
                top: 342px;
                left: 400px;
            }
            .status5{
                top: 342px;
                left: 104px;
            }
            .line{
                position: absolute;
            }
            .line1{
                top: 88px;
                left: 230px;
                width: 87px;
                height: 57px;
                background: url('@assets/TT/Icon/line1.png') no-repeat;
                background-size: 100% 100%;
            }
            .line2{
                top: 136px;
                left: 455px;
                width: 106px;
                height: 23px;
                background: url('@assets/TT/Icon/line2.png') no-repeat;
                background-size: 100% 100%;
            }
            .line3{
                top: 335px;
                left: 520px;
                width: 111px;
                height: 97px;
                background: url('@assets/TT/Icon/line3.png') no-repeat;
                background-size: 100% 100%;
            }
            .line4{
                top: 352px;
                left: 240px;
                width: 146px;
                height: 25px;
                background: url('@assets/TT/Icon/line4.png') no-repeat;
                background-size: 100% 100%;
            }
        }
        .pic2{
            width: 700px;
            position: relative;
            height: 700px;
            .r1{
                top: 0;
            }
            .r2{
                top: 253px;
            }
            .r3{
                top: 504px;
            }
            .c1{
                left: 90px;
            }
            .c2{
                left: 318px;
            }
            .c3{
                left: 544px;
            }
            .cc1{
                left: 130px;
            }
            .cc2{
                left: 240px;
            }
            .cc3{
                left: 470px;
            }
            .cc4{
                left: 590px;
            }
            .rr1{
                top: 60px;
            }
            .rr2{
                top: 200px;
            }
            .rr3{
                top: 320px;
            }
            .rr4{
                top: 450px;
            }
            .rr5{
                top: 560px;
            }
            .arr{
                position: absolute;
                width: 37px;
                height: 24px;
                background: url('@assets/TT/Icon/grayarr.png') no-repeat;
                background-size: 100% 100%;
            }
            .left{
                transform: rotate(180deg);
            }
            .down{
                transform: rotate(90deg);
            }
            .stage{
                position: absolute;
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 116px;
                text-align: center;
                div{
                    width: 116px;
                    height: 116px;
                    // background: linear-gradient(180deg, #3CBF48 0%, #3DD13F 100%);
                    // border-radius: 48px 24px 48px 24px;
                    margin-bottom: 12px;
                    img{
                        width: 100%;
                    }
                }
                p{
                    font-size: 24px;
                    color: #4E5B7E;
                }
            }
        }
    }
    .sheetContent{
        width: 100%;
        background: #FFFFFF;
        box-shadow: 0px -6px 24px 2px rgba(0,0,0,0.08);
        border-radius: 40px 40px 0px 0px;
        position: relative;
        
        .xx{
            position: absolute;
            right: 30px;
            top: -26px;
            width: 56px;
            height: 56px;
            background: url('@assets/TT/Icon/xx2.png') no-repeat;
            background-size: 100% 100%;
        }
    }

    .backToTop{
        width:70px;
        height: 70px;
        position: fixed;
        right: 30px;
        bottom: 20%;
        opacity: 0;
        border: 5px solid #3CCB41;
        border-radius: 50%;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;

        img{
            width: 30px;
            height: 24px;
        }

        p{
            bottom: -15px;
            position: absolute;
            color: #3CCB41;
            height: 30px;
            padding: 5px;
            display: flex;
            text-align: center;
            align-items: center;
            justify-content: center;
            background-color: #F5FFEB;
            border-radius: 20px;
            font-size: 20px;
            white-space: nowrap;
        }
    }

    :deep(.van-action-sheet),
    :deep(.van-action-sheet__content){
        overflow: inherit !important;
    }
}