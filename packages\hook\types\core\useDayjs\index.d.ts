import dayjs from 'dayjs';
export interface DayjsExtends {
    isSameOrBefore: (date?: string | number | CustomDayjs | Date | undefined) => boolean;
    isSameOrAfter: (date?: string | number | CustomDayjs | Date | undefined) => boolean;
}
export type CustomDayjs = dayjs.Dayjs & DayjsExtends;
declare function setCurrentDay(dayjs: CustomDayjs): void;
export declare function useDayjs(date?: dayjs.ConfigType | undefined, option?: dayjs.OptionType | undefined, locale?: string | undefined): {
    setCurrentDay: typeof setCurrentDay;
    dayjs: (date?: dayjs.ConfigType | undefined, option?: dayjs.OptionType | undefined, locale?: string | undefined) => CustomDayjs;
    current: CustomDayjs;
};
export {};
