import 'babel-polyfill'
import { createApp, ComponentPublicInstance } from 'vue'
import App from './App.vue'
import './i18n'
import store from './store'
import * as hook from 'hook'
import './router'
import '@styles/iconfont.css'
import * as imgs from '@imgs/index'
import { AliLog } from '@via/mylink-sdk'
import { useEnvConfig, useWindow, useLoader } from 'hook'
import Vant from 'vant'
import 'vant/lib/index.css'


const { mQuery } = useWindow()
const { loadJs } = useLoader()

AliLog.setEnvironment(useEnvConfig().LOG_ENV)

const app = createApp(App)
app.config.errorHandler = (err: unknown, instance: ComponentPublicInstance | null, info: string) => {
    console.error('app-catch-error', err, instance, info)
}

function startDebug() {
    if (mQuery.debug) {//链接后面加&debug=true
        loadJs('https://cdn.mylink.hk.chinamobile.com/via/resource/activity/2025-05-15/7f246b20769e4ac8871494a89fd2cef2.js').then(() => {
            new (window as any).VConsole()
        })
    }
}

startDebug()

// 开发环境关闭arms
useEnvConfig().RUN_ENV == 'develop' && AliLog.disabled()

hook.install(app).use(imgs).use(store).use(Vant)
app.config.globalProperties.$onWait = new Promise((resolve) => {//先执行App.vue内的异步语句
    app.config.globalProperties.$reslove = resolve;
})
app.mount('#app')

hook.useApi().registerResponseInterceptor(async (res) => {
    return res
})
