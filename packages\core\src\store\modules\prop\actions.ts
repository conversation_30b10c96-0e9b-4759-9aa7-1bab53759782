import { PropStoreType } from './type'
import { useApi, useToast } from 'hook'
import { logEventStatistics } from '@via/mylink-sdk'

const { authRequest, authGet, authPost, get } = useApi()

async function wearProp(actived: boolean, item_id: number) {
    if (actived) {
        logEventStatistics('garden_reward_use_icon_click')
    }
    return await authRequest('PATCH', '/api/sdk/treetask/item/active', {
        params: {
            actived,
            item_id
        }
    })
}

async function cleanProp(bag: number) {
    return await authRequest('PATCH', '/api/sdk/treetask/item/disabled/clean', {
        params: {
            bag
        }
    })
}

// 使用双倍卡道具
async function useCard(id) {
    return await authRequest('PATCH', '/api/sdk/treetask/item/active/use', {
        params: {
            user_item_id: id
        }
    })
        .then((res) => {
            return res
        })
        .catch((err) => {
            return err
        })
}

// 拉取会员每月双倍卡
async function vipPull() {
    return await authGet('/api/sdk/treetask/vippull')
        .then((res) => {
            return res
        })
        .catch((err) => {
            return err
        })
}

async function propsHandle(this: PropStoreType, propsArr: Array<any>) {
    console.log(propsArr, 'propsArr')
    let arr = propsArr.filter((item) => item.code.replace(/(\d)+/g, '') == 'item')
    if (arr.length == 0) this.propsConfig['挂饰'] = [null, null, null]
    this.propsConfig['铲子'] = null
    this.propsConfig['围栏'] = null
    this.propsConfig['灯柱'] = null
    this.propsConfig['稻草人'] = null
    this.propsConfig['木头车'] = null
    this.propsConfig['喷水池'] = null
    this.propsConfig['桌子'] = null
    this.propsConfig['椅子'] = null
    this.propsConfig['GIF'] = null
    this.propsConfig['标题装饰'] = null
    propsArr.forEach((item) => {
        let code = item.code
            .replace(/(\d)+/g, '')
            // 针对装饰标题的特殊处理，对_号后所有的字符删除
            .replace(/_[^_]*$/, '')
        switch (code) {
            case 'item':
                for (let mm in this.propsConfig['挂饰']) {
                    if (this.propsConfig['挂饰'][mm] == null) continue
                    let len = arr.filter((i) => i.code == this.propsConfig['挂饰'][mm].code)
                    if (len.length == 0) {
                        this.propsConfig['挂饰'][mm] = null
                    }
                }
                for (let tt of arr) {
                    let len = this.propsConfig['挂饰'].filter((i) => {
                        if (i == null) return
                        return i.code == tt.code
                    })
                    if (len.length == 0) {
                        for (let t in this.propsConfig['挂饰']) {
                            if (this.propsConfig['挂饰'][t] == null) {
                                this.propsConfig['挂饰'][t] = tt
                                break
                            }
                        }
                    }
                }
                break
            case 'chanzi':
                this.propsConfig['铲子'] = item.code
                break
            case 'weilan':
                this.propsConfig['围栏'] = item.code
                break
            case 'dengzhu':
                this.propsConfig['灯柱'] = item.code
                break
            case 'daocaoren':
                this.propsConfig['稻草人'] = item.code
                break
            case 'mutouche':
                this.propsConfig['木头车'] = item.code
                break
            case 'penshuichi':
                this.propsConfig['喷水池'] = item.code
                break
            case 'zhuozi':
                this.propsConfig['桌子'] = item.code
                break
            case 'yizi':
                this.propsConfig['椅子'] = item.code
                break
            case 'title':
                this.propsConfig['标题装饰'] = item.code
                break
            default: //GIF情况
                this.propsConfig['GIF'] = item.code
                break
        }
    })
    console.log(this.propsConfig, 'this.propsConfig')
}

//清除获取新会员限定道具通知
async function updateUserFlagOnReceviedItemKind(kind_code: string) {
    return await authRequest('PATCH', '/api/sdk/treetask/update/flag_on_received_item_kind', { params: { kind_code } })
        .then((res) => {
            return res
        })
        .catch((err) => {
            return err
        })
}

//减碳商城
async function commodityList(target = 0) {
    return await authGet('/api/sdk/treetask/commodity/list' + '?target=' + target)
}
//减碳商城未登录
async function commodityListNoLogin(target = 0) {
    return await get('/api/sdk/treetask/commodity/list/shared' + '?target=' + target)
}

//购买接口
async function commodityBuy(commodity_id: number) {
    return await authRequest('PATCH', '/api/sdk/treetask/commodity/buy', {
        params: {
            commodity_id
        }
    }).catch((err) => {
        useToast().toast(err.msg)
    })
}
export default {
    wearProp,
    propsHandle,
    useCard,
    cleanProp,
    vipPull,
    updateUserFlagOnReceviedItemKind,
    commodityList,
    commodityListNoLogin,
    commodityBuy
}
