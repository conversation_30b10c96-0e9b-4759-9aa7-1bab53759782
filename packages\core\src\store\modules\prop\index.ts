import { defineStore } from 'pinia'
import actions from './actions'
import type { propType } from './type'

export const usePropStore = defineStore('propStore', {
    state: (): propType => ({
        propsConfig: {
            挂饰: [null, null, null], 
            铲子: null, 
            围栏: null,
            灯柱: null,
            稻草人: null,
            木头车: null,
            喷水池: null,
            桌子: null,
            椅子: null,
            GIF: null
        },
        toCard1:false,
        toCard2:false,
        toCard:false,
    }),
    actions
})
