<template>
    <commonSheet :showSheet="$attrs.showSheet" @close="emit('close')">
        <template #title>
            <h1 class="title">{{ state.nft.previewNFTTitle }}</h1>
        </template>
        <template #content>
            <div class="content">
                <div class="preview">
                    <img :src="treeSideImg" />
                </div>
                <div class="bottom" @click="emit('close')">
                    <div class="click_btn active">{{ state.nft.gotIt }}</div>
                </div>
            </div>
        </template>
    </commonSheet>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useLang, useRouter } from 'hook'
import commonSheet from '@/components/common/commonSheet.vue'
import { imgs } from '@/assets/imgs'
const { router } = useRouter()
const emit = defineEmits(['close'])
const treeSideImg = ref('')
const { treeCode } = defineProps({
    /** 树的类型 */
    treeCode: {
        type: String,
        require: true,
        default: ''
    }
})
const { state } = useLang()

onMounted(async () => {
    // 根据传入树的信息显示不同的图片
    treeSideImg.value = imgs[`NTFDetail/view/${treeCode}.png`]
})
/** 进入条款 */
const toClause = () => {
    toRouter('/rule')
}

/**
 * 路由跳转
 * @param url 跳转地址
 */
const toRouter = (url: string) => {
    router.push({
        path: url,
        query: {
            hideNavigationBar: 'true'
        }
    })
}
</script>

<style lang="less" scoped>
.title {
    font-weight: 600;
    font-size: 32px;
    color: #4e5b7e;
    line-height: 40px;
    text-align: center;
    padding-top: 20px;
}

.content {
    height: 600px;
    padding: 72px 50px 0px;

    .preview {
        display: flex;
        justify-content: center;
        align-items: center;
        img {
            width: 570px;
        }
    }

    .bottom {
        margin-top: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /** 弹窗按钮 */
    .click_btn {
        border-radius: 48px 12px 48px 12px;
        bottom: 50px;
        width: 570px;
        height: 84px;
        padding: 5px 20px;
        text-align: center;
        box-sizing: content-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #fff;
    }

    /** 未激活的按钮 */
    .active {
        background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
        box-shadow: 0px 2px 0px 2px rgba(252, 175, 40, 1), inset 0px 4px 0px 2px rgba(255, 242, 178, 1);
    }
}
</style>
