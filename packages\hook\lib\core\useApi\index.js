import { request, post, get, registerResponseInterceptor } from './axios';
import config from './config';
import { useStorage } from '../useStorage';
import { useJwt } from '../useJwt';
import { useLang } from '../useLang';
const storage = useStorage();
let cacheJwt = '';
function setJwt(jwt) {
    let success = false;
    const { leftTime } = useJwt(jwt);
    if (leftTime > 0) {
        cacheJwt = jwt;
        let key = 'login-jwt';
        storage.save(key, jwt, leftTime);
        success = true;
    }
    else {
        cacheJwt = '';
    }
    return success;
}
//需要jwt验证
async function authRequest(method, url, opts) {
    const options = Object.assign({ params: {}, headers: {}, data: {} }, opts);
    if (cacheJwt) {
        options.headers.Authorization = `Bearer ${cacheJwt}`;
    }
    else {
        let token = storage.customLoad('via:system:login-jwt');
        if (token)
            options.headers.Authorization = `Bearer ${token.v}`;
    }
    options.headers.lang = useLang().lang.value;
    return request(method, url, options);
}
async function authGet(url, opts) {
    return authRequest('get', url, opts);
}
async function authPost(url, opts) {
    return authRequest('post', url, opts);
}
export function useApi() {
    return {
        host: config.host,
        request,
        post,
        get,
        setJwt,
        authRequest,
        authGet,
        authPost,
        registerResponseInterceptor
    };
}
