<template>
    <div class="friend-box">
        <navComponent :returnType="2" :showTransmit="true"/>
        <div class="nofriend" v-if="friendList.length == 0">
            <img :src="$imgs['noFriend.png']" alt="">
            <div class="nofriend-text">
                <span>{{ state.flower.暫無任何好友 }}</span>
            </div>
            <div class="nofriend-btn" @click="addFirend">
                <span>{{ state.flower.去添加好友 }}</span>
            </div>
        </div>
        <!-- <div class="myfriend">
            <p>我的好友</p>
            <span>(共{{ friendList.length }}个好友)</span>
        </div> -->
        <!-- <div class="seizeASeat"></div> -->
        <div class="friend-list" v-else>
            <div class="box">
                <!-- <van-index-bar :index-list="list">
                    <template v-for="item in list">
                        <van-index-anchor :index="item">{{ item }}</van-index-anchor> -->
                        <van-cell v-for="value in friendList" @click="giveFriend(value.head_logo,value.name,value.third_id)">
                            <div class="head">
                                <img :src="value.head_logo" alt="">
                            </div>
                            <div class="text">
                                <span>{{ value.name }}</span>
                            </div>
                        </van-cell>
                    <!-- </template>
                </van-index-bar> -->
            </div>
        </div>
        <giveDig :treeCode="flowersCode" :head="friendHead" :name="friendName" :friend_third_id="friendId" v-if="giveDigShow"></giveDig>
    </div>
</template>

<script lang="ts" setup>
import { ref,onBeforeMount,onMounted } from "vue";
import navComponent from '@/components/navComponent.vue';
import { useTaskStore, useTreeStore, useUserStore, usePropStore, useAppStore, useFriendStore } from '@/store'
import { useDayjs,useRouter,useEventBus,useLang,useDialog,useEnvConfig } from "hook";
import giveDig from "./components/giveDig.vue";
import guideDialog from '@/components/guideDialog.vue';

const { router } = useRouter()
const { state } = useLang()
const eventBus = useEventBus()
const friendStore = useFriendStore()
const giveDigShow = ref(false)
let friendList = ref<any>([])
const flowersCode = ref<any>(router.currentRoute.value.query.treeCode)
const friendHead = ref('')
const friendName = ref('')
const friendId = ref('')
useUserStore().setUpData('first_list_sending_flowers','1')
const init = async ()=>{
    await friendStore.getFlowerFriendList()
    friendList.value = friendStore.flowerFriendList
}

const giveFriend = async (head,name,friend_third_id)=>{
    friendHead.value = head
    friendName.value = name
    friendId.value = friend_third_id
    giveDigShow.value = true
}

const addFirend = ()=>{
    location.href = useEnvConfig().ADD_URL
}

onBeforeMount(async ()=>{
    eventBus.on('cancel',()=>giveDigShow.value = false)
    await init()
})

onMounted(()=>{
    let dialog = document.querySelector('#app-dialog')
    dialog.style.display = 'none'
})
</script>

<style lang="less" scoped>
.friend-box{
    height: 100vh;
}
:deep(.van-index-bar__index){
    line-height: 40px;
}
:deep(.van-cell){
    height: 84px;
    display: flex;
    align-items: center;
}
:deep(.van-index-anchor){
    color: rgba(151,142,188,0.8);
    font-size: 28px;
}
:deep(.van-hairline--bottom){
    position: fixed;
}
:deep(.van-cell__value--alone){
    display: flex;
    align-items: center;
}
:deep(.van-cell__value){
    display: flex;
    align-items: center;
    height: 60px;
    padding: 12px 0;
}
.friend-list{
    height: calc(100% - 300px);
    .box{
        overflow-y: scroll;
        height: 100%;
    }
}
.van-index-bar{
}
:deep(.van-index-bar__sidebar){
    top: 300px;
    transform: translateY(0);
}
.nav{
    height:192px;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(180deg, #D9F4E7 0%, #fff 100%);
}
.myfriend{
    height: 108px;
    position: fixed;
    top: 192px;
    background-color: #fff;
    z-index: 3;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    p{
        font-size: 32px;
        font-family: PingFang HK-Semibold, PingFang HK;
        font-weight: 600;
        color: rgba(0,0,0,0.8);
        margin-left: 38px;
        margin-right: 8px;
    }
    span{
        font-size: 22px;
        font-family: PingFang HK-Regular, PingFang HK;
        font-weight: 400;
        color: rgba(102,102,102,0.8);
    }
}
.seizeASeat{
    width: 100%;
    height: 108px;
}
.nofriend{
    img{
        width: 400px;
        height: 320px;
        margin: 354px auto 0;
        display: block;
    }
    .nofriend-text{
        text-align: center;
        span{
            font-size: 32px;
            font-family: PingFang HK-Medium, PingFang HK;
            font-weight: 500;
            color: #4E5B7E;
            line-height: 40px;
        }
    }
    .nofriend-btn{
        width: 404px;
        
        height: 82px;
        background: #3CBF48;
        border-radius: 110px 110px 110px 110px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 94px auto  0;
        span{
            font-size: 36px;
            font-family: PingFang HK-Medium, PingFang HK;
            font-weight: 500;
            color: #FFFFFF;
            line-height: 38px;
        }
    }
}
:deep(.transmit){
    display:none !important;
}
.head{
    width: 60px;
    height: 60px;
    background-color: #000;
    border-radius: 50%;
    margin-right: 12px;
    img{
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }
}
.text{
    font-size: 28px;
    font-family: PingFang HK-Regular, PingFang HK;
    font-weight: 400;
    color: rgba(0,0,0,0.8);
    line-height: 24px;
}
</style>