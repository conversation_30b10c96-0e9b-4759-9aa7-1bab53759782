export default class BaseFormatter {
    _caches: {
        [key: string]: Array<Token>;
    };
    constructor();
    interpolate(message: string, values: any): string;
}
type Token = {
    type: 'text' | 'named' | 'list' | 'unknown';
    value: string;
};
export declare function parse(format: string): Array<Token>;
export declare function compile(tokens: Array<Token>, values: Object | Array<any>): Array<any>;
export {};
