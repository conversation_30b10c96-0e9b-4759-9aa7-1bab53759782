import { useEnvConfig } from '../useEnvConfig';
export var RuntimeMode;
(function (RuntimeMode) {
    RuntimeMode["DEV"] = "dev";
    RuntimeMode["PROD"] = "prod";
})(RuntimeMode || (RuntimeMode = {}));
export var RuntimeEnv;
(function (RuntimeEnv) {
    RuntimeEnv["web"] = "web";
    RuntimeEnv["wx"] = "wx";
    RuntimeEnv["wxMini"] = "wxMini";
    RuntimeEnv["other"] = "other"; // 其他
})(RuntimeEnv || (RuntimeEnv = {}));
export var MobileType;
(function (MobileType) {
    MobileType["web"] = "web";
    MobileType["android"] = "android";
    MobileType["ios"] = "ios";
})(MobileType || (MobileType = {}));
const { isProd } = useEnvConfig();
let runtimeEnv = RuntimeEnv.web;
let runtimeMode = isProd ? RuntimeMode.PROD : RuntimeMode.DEV;
let mobileType = MobileType.web;
function checkEnv() {
    const ua = navigator.userAgent;
    if (/micromessenger/i.test(ua)) {
        runtimeEnv = RuntimeEnv.wx;
    }
}
function setRuntimeEnv(env) {
    runtimeEnv = env;
}
function checkMobileType() {
    const ua = navigator.userAgent;
    if (ua.indexOf('Android') > -1 || ua.indexOf('Adr') > -1) {
        mobileType = MobileType.android;
    }
    if (!!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
        mobileType = MobileType.ios;
    }
}
checkEnv();
checkMobileType();
export function useRuntimeEnv() {
    return {
        runtimeEnv,
        runtimeMode,
        mobileType,
        setRuntimeEnv
    };
}
