import { App, Component, createApp, getCurrentInstance } from 'vue'
import DialogApp from './DialogApp.vue'
import { UseDialog } from './type'
import { state, rawState } from './state'
import registerList, { register } from './register'
import { Dialog } from './dialog'

const dialogApp = createApp(DialogApp)
dialogApp.mount('#app-dialog')
register(dialogApp)

/**
 * 注册弹窗，显示弹窗之前，先要有注册，否则会读取不到弹窗配置
 * @param dialog
 */
function registerDialog(dialog: UseDialog.DialogRegisterType) {
  rawState.dialogMapper.set(dialog.dialogName || dialog.name, dialog)
}

registerList.forEach(registerDialog)

export function useDialog(
  localDialog?: Record<string, Component | { componentName?: string; component: Component; opts?: UseDialog.DialogOpt }>
) {
  // 先从当前组件获取，看是否有注册局部弹窗，没有再获取全局的
  const parent = getCurrentInstance()

  if (parent) {
    ;(parent as any).localDialog = localDialog
  }
  // console.log(parent)

  return {
    registerDialog,
    app: dialogApp,
    state,
    rawState,
    get: (name: string, opts?: UseDialog.DialogOpt) => new Dialog(false, parent, name, opts),
    getSingle: (name: string, opts?: UseDialog.DialogOpt) => {
      const singleDialog = rawState.singleDialogMap.get(name)
      return singleDialog || new Dialog(true, parent, name, opts)
    },
    getInstance: (uuid: string) => rawState.dialogMap.get(uuid),
    closeAll: () => {
      while (state.showList.length) {
        const item = state.showList.pop() as UseDialog.DialogData
        rawState.dialogMap.get(item.uuid)?.close()
        rawState.singleDialogMap.get(item.name)?.close()
      }
    }
  }
}
