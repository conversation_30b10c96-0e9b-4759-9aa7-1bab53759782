<template>
  <div class="box">
      <p class="tip">MyGarden</p>
      <!-- <div class="actBox"> -->
          <Swipe class="actBox" :loop="false" :show-indicators="false">
              <SwipeItem v-for="(item, index) in activityArr" :key="index">
                  <activity :actDig="false" :HpData='HpData' class="itemBox" @toRule='toRule' :short='true' :item='item'/>
              </SwipeItem>
          </Swipe>
      <!-- </div> -->
      <div  class="know" @click="emit('close')">
            {{state.dialog.知道啦}}
        </div>

  </div>
</template>

<script setup lang='ts'>
import activity from './activity.vue'
import { Swipe, SwipeItem } from 'vant'
import { useDialog, useLang, useRouter } from 'hook';
import { defineComponent, onBeforeMount, onMounted, inject, ref, watch, getCurrentInstance } from 'vue'
import { useTaskStore, useUserStore } from '@store/index'
import { PropType } from 'vue';
const { state } = useLang()
let emit = defineEmits(['close'])
const { router, currentRoute } = useRouter()
let activityArr = ref<{
    id:number,
    title?:string,
    time?:string,
    pic?:string,
    tip?:string,
    text?:string,
    textAct?:string,
    title2?:string
}[]>([])
let linkage = ref(false)
const toRule = (type?: any) =>{
    emit('close')
    router.push({
        path: '/rule',
        query: {
            "hideNavigationBar": 'true',
            "type": type
        }
    })
}



const props = defineProps({
  HpData: {
      type: Object,
      required: false
  },
  list: {
      type: Array as PropType<number[]>,
      required: false
  }
})

onBeforeMount(() => {
    if(props.list && props.list.length > 0){
        if(props.list.includes(-1)){
            linkage.value = true
        }
        props.list.forEach(ele => {
            if(ele < 0) return
            let arr:Array<never> = state.activityArr.filter((item) => {
                return ele == item.id
            })
            activityArr.value.push(...arr)
        })
    }else{
        // 深拷贝
        linkage.value = true
        activityArr.value = JSON.parse(JSON.stringify(state.activityArr))
        if (useUserStore().isHK == 1) {
            activityArr.value = activityArr.value.filter((item) => {
                return item.id != 7 && item.id != 9 && item.id != 15
            })
        }
    }
    if(!useTaskStore().acIntStu){
        activityArr.value = activityArr.value.filter((item) => {
            return item.id != 3
        })
    }
    if(useUserStore().isSlash){
        activityArr.value = activityArr.value.filter((item) => {
            return (item.id != 4 && item.id != 5)
        })
    }
    // 会员下架、一期任务 卡片去掉
    activityArr.value = activityArr.value.filter((item) => {
            return (item.id != 5 && item.id != 3 && item.id != 15 && item.id != 17 && item.id != 19)
        })
})
</script>

<style lang='less' scoped>
.van-swipe{
    overflow: visible !important;
}
.van-swipe-item{
    display: flex;
    align-items: center;
    justify-content: center;
}
.box{
    display: flex;
    flex-direction: column;
    align-items: center;
    .tip{
        font-size: 48px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        margin-bottom: 190px;
    }
    >.actBox{
        width: 592px;
        height: 640px;
        .itemBox{
            flex-shrink: 0;
        }
    }
    .know{
        margin-top: 180px;
        min-width: 430px;
        height: 84px;
        background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
        box-shadow: 0px 8px 0px 2px rgba(252,175,40,1), inset 0px 4px 0px 2px rgba(255,242,178,1);
        border-radius: 48px 12px 48px 12px;
        font-size: 36px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        line-height: 32px;
        text-shadow: 0px 0px 12px #FBAC2E;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
