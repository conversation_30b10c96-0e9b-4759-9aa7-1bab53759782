import dayjs from 'dayjs';
/**
 * 判断当前日期是否小于等于传入的日期
 */
dayjs.prototype.isSameOrBefore = function (date) {
    return this.isSame(date) || this.isBefore(date);
};
/**
 * 判断当前日期是否大于等于传入的日期
 */
dayjs.prototype.isSameOrAfter = function (date) {
    return this.isSame(date) || this.isAfter(date);
};
let currentDayjs = '';
function setCurrentDay(dayjs) {
    currentDayjs = dayjs.format();
}
function customDayjs(date, option, locale) {
    if (!date && currentDayjs) {
        date = currentDayjs;
    }
    return dayjs(date, option, locale);
}
// ConfigType时间格式
// OptionType操作格式,如get、diff等(maybe)
export function useDayjs(date, option, locale) {
    return {
        setCurrentDay,
        dayjs: customDayjs,
        current: dayjs(date, option, locale)
    };
}
