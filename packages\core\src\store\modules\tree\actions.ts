import { treeType } from './type'
import { useApi, useEventBus } from 'hook'

const { authGet, authPost, get } = useApi()

async function getTreeConfig(tree_id: number) {
    return await authGet('/api/sdk/treetask/tree/config', { params: { tree_id } })
}

async function getTreeConfigShare(tree_id: number) {
    return await get('/api/sdk/treetask/tree/config/shared', { params: { tree_id } })
}

async function plantTree(tree_id: number) {
    return await authPost('/api/sdk/treetask/plant', { data: { tree_id } })
}

function changeHasPlanted(this: treeType, bol: boolean) {
    this.hasPlanted = bol
}

function changePlanted(this: treeType, bol: boolean) {
    this.planted = bol
}

function changeTreeCode(this: treeType, code: string) {
    this.treeCode = code
}

function changeEnergyTotal(this: treeType, num: number) {
    useEventBus().emit('upDataEnergyTotal',num)
    this.energyTotal = num
}

function changelv(this: treeType, num: number) {
    this.lv = num
}



export default { changelv, changeEnergyTotal, changeTreeCode, getTreeConfig, plantTree, changeHasPlanted, changePlanted, getTreeConfigShare }
