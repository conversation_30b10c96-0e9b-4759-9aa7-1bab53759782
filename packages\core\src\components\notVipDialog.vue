<template>
  <div class="box">
      <div class="d-box" v-if="step == 0">
          <p class="title">{{$lang.dialog.您还不是会员.replace('{x}', $lang.plant[`${treeCode}`].plantName)}}</p>
          <div class="btns"> 
              <div class="btn" @click="emit('close')">{{$lang.dialog.重新选择}}</div>
              <div class="btn r" @click="step++">{{$lang.dialog.成为会员}}</div>
          </div>
      </div>
      <div class="d-box n-box" v-if="step == 1">
          <p class="title">{{$lang.dialog.即将跳转到我的会员}}</p>
          <p class="content">{{$lang.dialog.确认立即跳转吗}}</p>
          <div class="btns"> 
              <div class="btn" @click="step--">{{$lang.dialog.取消}}</div>
              <div class="btn r" @click="toVip">{{$lang.dialog.确认}}</div>
          </div>
      </div>
  </div>
</template>

<script setup>
import { useEnvConfig, useLang } from 'hook'
import { ref } from 'vue'

const { lang } = useLang()
const props = defineProps({
  treeCode: {
    type: String
  },
})
let emit = defineEmits(['close'])
let step = ref(0)
let isPat = useEnvConfig().RUN_ENV == 'production' ? true : false

let member_url = {
    uat: 'openurl-modal://https://*************/activity/member/#/memberApply/introduce?isScroll=1&lang=<<cmcchkhsh_cmplang>>',
    pat: 'openurl-modal://https://mylink.komect.com/activity/member/#/memberApply/introduce?isScroll=1&lang=<<cmcchkhsh_cmplang>>'
}

const toVip = () => {
    window.location.href = isPat ? member_url.pat : member_url.uat
}
</script>

<style lang='less' scoped>
.box{
    .d-box{
        border-radius: 72px 24px 72px 24px;
        width: 544px;
        background: #FFFFFF;
        // border-radius: 24px 24px 24px 24px;
        padding: 64px 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .n-box{
        border-radius: 24px 24px 24px 24px;
        .btn{
            border-radius: 48px 48px 48px 48px !important;
        }
    }
    .title{
        font-size: 32px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #4E5B7E;
        text-align: center;
        // white-space: nowrap;
    }
    .content{
        margin-top: 16px;
        font-size: 24px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #4E5B7E;
        line-height: 40px;
        text-align: center;
        opacity: 0.7;
    }
    .btns{
        margin-top: 50px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .btn{
            font-size: 28px;
            color: #A7AEC3;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 220px;
            min-height: 56px;
            flex-shrink: 1;
            padding: 5px 20px;
            // white-space: nowrap;
            text-align: center;
            background: #FFFFFF;
            border-radius: 48px 12px 48px 12px;
            border: 2px solid #9EA5BA;
        }
        .r{
            color: #FFFFFF;
            border: none;
            background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
        }
    }
}
</style>