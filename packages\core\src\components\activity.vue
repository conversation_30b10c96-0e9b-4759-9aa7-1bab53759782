<template>
    <div>
        <!-- 活动内容页面 -->
        <div
            class="limit__content"
            :class="{ limit__contents: actDig }"
            v-if="item.id == 9 || item.id == 19 || item.id == 21"
        >
            <!-- <img :src="$imgs['redBubble.png']" alt=""> -->
            <i class="limit">
                <span>{{ state.nft.活動限定 }}</span>
            </i>
        </div>
        <div class="activity" :class="{ activity11: item.id == 11 }">
            <p class="title" v-if="item.id < 7" v-html="item.title"></p>
            <p
                class="title"
                :class="{ img: item.id == 11 && lang == 'en', img12: item.id == 12 }"
                v-else
                v-html="actDig ? item.title : item.title2"
            ></p>
            <p class="time" v-html="item.time"></p>
            <div :class="{ newSize1: item.id == 10, newSize2: item.id == 20, gao: item.id == 30 }">
                <img
                    class="dialog"
                    :class="{
                        gao: item.id == 30,
                        size: item.id == 7,
                        Esize: item.id == 8,
                        Esize2: item.id == 9,
                        newSize: item.id == 10,
                        updateSize: item.id == 11,
                        plantLife: item.id == 1
                    }"
                    :src="!actDig && item.pic2 ? $imgs[item.pic2] : $imgs[item.pic]"
                    alt=""
                    v-if="item.id !== 9"
                />
                <img
                    :class="{ Esize2: item.id == 9 }"
                    :src="actDig && item.id == 9 ? $imgs[item.pic] : $imgs[item.actPic]"
                    alt=""
                    v-else
                />
            </div>
            <p class="tip" v-if="item.id == 1">
                {{ item.tip }}
                <span>{{ HpData.n1 }}</span>
                /{{ HpData.n2 }}
            </p>
            <p class="tip" v-if="item.id == 3 && actDig">{{ item.tip }}</p>
            <p class="tip" v-if="item.id == 3 && !actDig">{{ item.tip1 }}</p>
            <p class="tip1" v-if="item.id == 13">{{ item.tip }}</p>
            <template v-if="item.id != 3">
                <p
                    class="text"
                    v-if="item.id < 7"
                    v-html="item.id == 4 ? (short ? item.text1 : item.text) : item.text"
                ></p>
                <p class="text" v-else v-html="actDig ? item.text : item.textAct"></p>
            </template>
            <template v-else>
                <p class="text" v-html="actDig ? item.text : item.text1"></p>
            </template>
            <div class="btn" @click="goRecharge" v-if="item.id == 34">
                {{ state.ai.去充值 }}
            </div>
            <p v-if="item.id != 27 && item.id != 26" class="torule" @click="toRule(index)">
                {{ state.home.详细规则 }} >
            </p>
            <template v-else>
                <div :class="actDig ? 'btn-link' : 'btn-actDig'" @click="goLink(item.id)">
                    <p>{{ item.id == 27 ? state.springFestival.前往參與活動 : state.springFestival.前往LinkNFT }}</p>
                </div>
            </template>
        </div>
    </div>
</template>

<script setup>
import { useDialog, useLang, useEnvConfig } from 'hook'
import { defineComponent, onBeforeMount, onMounted, inject, ref, watch, getCurrentInstance } from 'vue'
const env = useEnvConfig()
const props = defineProps({
    item: {
        type: Object,
        required: true
    },
    HpData: {
        type: Object,
        required: false
    },
    short: {
        type: Boolean,
        default: false
    },
    actDig: {
        type: Boolean,
        required: false
    }
})
const { state, lang } = useLang()
const uuid = inject('uuid')
let emit = defineEmits(['toRule'])

const toRule = (index) => {
    if (props.item.id == 12) {
        emit('toRule', 'integral')
    } else if (props.item.id == 15) {
        emit('toRule', 'task')
    } else if (props.item.id == 17) {
        emit('toRule', 'task')
    } else {
        emit('toRule')
    }
}

const goLink = (id) => {
    if (id == 27) {
        if (env.RUN_ENV == 'develop' || env.RUN_ENV == 'uat') {
            location.href =
                'https://uatoss.mylinkapp.hk/via/atv/2025CNY/index.html?lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&debug=true'
        } else {
            location.href =
                'https://cdn.mylink.hk.chinamobile.com/via/atv/2025CNY/index.html?lang=%3C%3Ccmcchkhsh_cmplang%3E%3E'
        }
    } else {
        if (env.RUN_ENV == 'develop' || env.RUN_ENV == 'uat') {
            location.href =
                'https://appsvc.mylink.cool/nftweb/#/third-party?origin=14&target=numberExchange&lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true&categoryId=0'
        } else {
            location.href =
                'https://cdn.mylink.hk.chinamobile.com/blockchain/link/nft/index.html#/third-party?origin=14&target=numberExchange&forceHideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>'
        }
    }
}

const goRecharge = () => {
    if (env.RUN_ENV == 'develop' || env.RUN_ENV == 'uat') {
        location.href =
            'http://*************/activity/2daigc/#/photo/profile?forceHideNavigationBar=true&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&isRecharge=1'
    } else {
        location.href =
            'https://mylink.komect.com/activity/2daigc/#/photo/profile?forceHideNavigationBar=true&lang=%3C%3Ccmcchkhsh_cmplang%3E%3E&isRecharge=1'
    }
}
</script>

<style lang="less" scoped>
.btn-link {
    width: 430px;
    height: 84px;
    background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
    box-shadow: 0px 8px 0px 2px #fcaf28, inset 0px 4px 0px 2px #fff2b2;
    border-radius: 48px 12px 48px 12px;
    margin-top: 32px;
    font-weight: bold;
    font-size: 36px;
    color: #ffffff;
    line-height: 32px;
    text-shadow: 0px 0px 12px #fbac2e;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    p {
        margin-top: 10px;
    }
}
.btn-actDig {
    width: 244px;
    height: 66px;
    background: linear-gradient(360deg, #3cbf48 0%, #85e277 100%);
    border-radius: 34px 34px 34px 34px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    p {
        font-weight: 400;
        font-size: 24px;
        color: #ffffff;
        line-height: 40px;
    }
}
.img {
    width: 480px;
}

.img12 {
    width: 356px;
}

.limit__contents {
    top: 8% !important;
    right: 60px !important;
}

.limit__content {
    position: absolute;
    right: 45px;
    top: -7%;
    width: 146px;
    height: 88px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: url('@assets/imgs/redBubble.png') no-repeat;
    background-size: 100%;
    z-index: 2;

    // img{
    //     width: 100%;
    // }
    .limit {
        width: 120px;
        height: 40px;
        text-align: center;
        font-style: normal;
        font-size: 12px;
        line-height: 12px;
        position: absolute;
        color: #fff;
        top: 24px;
        left: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.activity {
    width: 542px;
    // min-height: 640px;
    height: 640px;
    overflow-y: scroll;
    background: #eaf5ef;
    border-radius: 32px 32px 32px 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding-bottom: 34px;

    > .title {
        padding-top: 48px;
        font-size: 32px;
        font-family: PingFang HK-Semibold, PingFang HK;
        font-weight: 600;
        color: #4e5b7e;
        text-align: center;
        white-space: pre-line;
    }

    > .time {
        width: 100%;
        text-align: center;
        white-space: pre-line;
        padding-top: 10px;
        font-size: 24px;
        font-family: PingFang HK-Regular, PingFang HK;
        font-weight: 400;
        color: #a7aec3;
    }

    > img {
        height: 170px;
        padding-top: 24px;
        box-sizing: content-box;
    }

    .size {
        height: 238px;
        width: 446px;
    }

    .Esize {
        width: 446px;
        height: 498px;
    }

    .Esize2 {
        padding-top: 0;
        width: 480px;
        height: auto;
    }

    .plantLife {
        width: 150px;
    }

    .updateSize {
        width: 480px;
        height: 252px;
    }

    .newSize {
        width: 480px;
        height: 424px;
    }

    .newSize1 {
        width: 480px;
        min-height: 424px;
    }
    .newSize2 {
        margin-top: 24px;
        margin-bottom: -40px;
    }
    .gao {
        width: 446px;
        height: 238px;
    }

    .tip1 {
        font-size: 24px;
        font-family: PingFang HK, PingFang HK;
        font-weight: 600;
        color: #4e5b7e;
        margin-top: 32px;
    }

    > .tip {
        max-width: 90%;
        text-align: center;
        padding-top: 32px;
        font-size: 28px;
        font-family: PingFang HK-Medium, PingFang HK;
        font-weight: 550;
        color: #4e5b7e;
        line-height: 32px;

        span {
            color: #fb7655;
            font-size: 36px;
        }
    }

    > .text {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 18px 0;
        max-width: 90%;
        font-size: 24px;
        font-family: PingFang HK-Regular, PingFang HK;
        font-weight: 400;
        color: #6a6a6a;
        line-height: 36px;
    }
    .btn {
        width: 264px;
        min-height: 64px;
        background: linear-gradient(180deg, #50dd4c 0%, #3cbf48 100%);
        border-radius: 30px 30px 30px 30px;
        font-size: 28px;
        color: #ffffff;
        text-stroke: 2px rgba(0, 0, 0, 0);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 36px;
    }

    > .torule {
        // position: absolute;
        // left: 50%;
        // transform: translate(-50%, 0);
        // bottom: 34px;
        display: flex;
        align-items: flex-end;
        font-size: 24px;
        font-family: PingFang HK-Regular, PingFang HK;
        font-weight: 400;
        color: #4e5b7e;
        line-height: 28px;
    }
}

.dialog {
    width: 480px;
    // height: 204px;
}
.newShopping {
    width: 440px;
}

// .activity1{
//     min-height: 640px;
//     overflow-y: initial;
//     height: auto;
// }
.activity11 {
    // height: auto;
    // border-radius: 72px 24px 72px 24px;
    // background: #FFFFFF;
    .text {
        margin: 0 48px;
    }
}
</style>
