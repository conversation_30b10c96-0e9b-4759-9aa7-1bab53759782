import type { Ref } from 'vue'

export type GuideBox = {
    style: {
        width: string
        height: string
        left: string
        top: string
        transition: string
    }
    className: string
}

export type GuideProps = {
    virtualBub: Ref<boolean>
    showSheet: Ref<boolean>
    finLoad: Ref<boolean>
    stepIndex: Ref<number>
    level: Ref<number>
    guide_completed: Ref<boolean>
    buildTree: Ref<boolean>
    no_first_login: Ref<boolean>
    notified_first_logic1: Ref<boolean>
    notified_first_logic2: Ref<boolean>
    notified_first_logic3: Ref<boolean>
    notified_first_logic4: Ref<boolean>
    tree_energy: Ref<number>
    demand: Ref<number[]>
    judgePopUpNotification: (tree_energy: any, demand: any, next: () => void) => Promise<void>
    ifLotArr: () => Promise<void>
    openNFTDialog: () => void
}
