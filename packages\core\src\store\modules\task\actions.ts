import * as api from './api'
import { useStorage } from 'hook'
import { TaskStoreType, taskType } from './type'
import { inApp, getUserInfo, logEventStatistics } from '@via/mylink-sdk'

const { customLoad } = useStorage()

//获取可点击的减碳球
export async function getEnergy(stepNum?: number) {
    return await api
        .getEnergy(stepNum)
        .then((res) => {
            return res
        })
        .catch((err) => {
            console.log(err)
        })
}

// 点击减碳球获取减碳值
export async function clickEnergy(energy_keys: Array<String>) {
    return await api
        .clickEnergy(energy_keys)
        .then((res) => {
            console.log(res)
            return res
        })
        .catch((err) => {
            console.log(err)
            return clickEnergy(energy_keys)
        })
}

// 获取用户信息
export async function getInfo() {
    return await api
        .getInfo()
        .then((res) => {
            return res
        })
        .catch((err) => {
            console.log(err)
            return err
        })
}

// 获取功能卡
export async function getPull() {
    return await api
        .getPull()
        .then((res) => {
            return res
        })
        .catch((err) => {
            console.log(err)
        })
}

// 获取道具列表
export async function getPropList(target: Number = 0, bag: Number = 0) {
    return await api
        //target 0-装饰道具 1-功能道具 -1-过期
        //bag 0-普通 1-VIP
        .getPropList(target, bag)
        .then((res) => {
            return res
        })
        .catch((err) => {
            console.log(err)
        })
}

// 获取道具列表
export async function getShareList(target: Number = 0, bag: Number = -1) {
    return await api
        //target 0-普通 1-VIP
        //bag 0-装饰道具 1-功能道具
        .getShareList(target, bag)
        .then((res) => {
            return res
        })
        .catch((err) => {
            console.log(err)
        })
}

// 获取树列表
export async function getTreeList() {
    return await api
        .getTreeList()
        .then((res) => {
            return res
        })
        .catch((err) => {
            console.log(err)
        })
}

// 未登录时获取树列表
export async function getTreeListNoLogin() {
    return await api
        .getTreeListNoLogin()
        .then((res) => {
            return res
        })
        .catch((err) => {
            console.log(err)
        })
}

// 种树
export async function plantTree(this: taskType, tree_id: Number) {
    return await api
        .plantTree(tree_id)
        .then((res) => {
            logEventStatistics('Mygarden_plant_user')
            if (res.user_actions.length) {
                let upgrade = res.user_actions
                    .filter((item) => item.action_key == 'tree_upgrade')
                    .sort((a, b) => {
                        return a.tree_value.level - b.tree_value.level
                    })
                let complete = res.user_actions.filter((item) => item.action_key == 'tree_complete')
                this.action_upgrade = [...upgrade, ...complete]
            }
            return res
        })
        .catch((err) => {
            console.log(err)
        })
}

// 种树
export async function setUserActions(this: taskType, actArr: any[]) {
    if (actArr.length) {
        let upgrade = actArr
            .filter((item) => item.action_key == 'tree_upgrade')
            .sort((a, b) => {
                return a.tree_value.level - b.tree_value.level
            })
        let complete = actArr.filter((item) => item.action_key == 'tree_complete')
        this.action_upgrade = [...upgrade, ...complete]
    }
}

export function getAction(this: taskType) {
    let action = JSON.parse(JSON.stringify(this.action_upgrade))
    this.action_upgrade = []
    return action
}

// 减碳值领取记录
export async function getRecord(startTime: String, endTime: String) {
    return await api
        .getRecord(startTime, endTime)
        .then((res) => {
            return res
        })
        .catch((err) => {
            console.log(err)
        })
}

// 获取购买记录
export async function getBuyHistory(startTime: string, endTime: string) {
    return await api.getBuyHistory(startTime, endTime)
}

export function changeAcIntStu(this: taskType, boo: boolean) {
    this.acIntStu = boo
}

// 获取任务列表
export async function getTaskList() {
    return await api.getTaskList()
}

// 领取任务奖励
export async function getAwards(task_id: number) {
    return await api.getAwards(task_id)
}

// 任务状态
export async function getTaskState() {
    return await api.getTaskState()
}

// 领取上期任务
export async function getPreviousReward() {
    return await api.getPreviousReward()
}

/**
 * 获取盲盒状态，主要用于决定盲盒是否显示红点
 */
export async function getBlindBoxState() {
    return await api.getBlindBoxState()
}

/**
 * 获取nft权益状态
 */
export async function getNFTEquityState() {
    return await api.getNFTEquityState()
}

/**
 * nft权益数据清除
 */
export async function nftNewestOff() {
    return await api.nftNewestOff()
}

export async function changeNewest(target:number) {
    return await api.changeNewest(target)
}

export default {
    setUserActions,
    getEnergy,
    clickEnergy,
    getInfo,
    getPropList,
    getShareList,
    getTreeList,
    getTreeListNoLogin,
    plantTree,
    getRecord,
    getAction,
    changeAcIntStu,
    getPull,
    getBuyHistory,
    getTaskList,
    getAwards,
    getTaskState,
    getPreviousReward,
    getBlindBoxState,
    getNFTEquityState,
    changeNewest,
    nftNewestOff
}
