export declare enum EventBusKey {
    LOGIN = "v:event:login",
    WEB_HIDDEN = "__web-hidden__",
    WEB_RESUME = "__web-resume__"
}
declare function register(instance?: any): void;
declare function unregister(instance?: any): void;
declare function on(type: string, handler: any, instance?: any): void;
declare function once(type: string, handler: any, instance?: any): void;
declare function emit(type: string, ...params: any): void;
declare function emitToInstance(type: string, instance: any, ...params: any): void;
declare function stickyEmit(type: string, ...params: any): void;
declare function off(type: string, handler: any, instance?: any): void;
export declare function useEventBus(): {
    register: typeof register;
    unregister: typeof unregister;
    on: typeof on;
    once: typeof once;
    emit: typeof emit;
    emitToInstance: typeof emitToInstance;
    stickyEmit: typeof stickyEmit;
    off: typeof off;
};
export {};
