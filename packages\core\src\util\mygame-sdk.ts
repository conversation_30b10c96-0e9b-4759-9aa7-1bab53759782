function systemName() {
  var u = navigator.userAgent;
  var name;
  if (
    u.indexOf("Android") > -1 ||
    u.indexOf("Adr") > -1 ||
    u.indexOf("hshhk/android") > -1
  ) {
    name = "android";
  } else if (
    u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) ||
    u.indexOf("hshhk/ios/") > -1
  ) {
    name = "ios";
  } else {
    name = "unknown";
  }
  return name;
}
function isIOS() {
  return systemName() === "ios";
}
function isAndroid() {
  return systemName() === "android";
}

function iOSSystemCall(funcName, payload) {
  var obj = window;
  if (
    obj.webkit &&
    obj.webkit.messageHandlers &&
    obj.webkit.messageHandlers[funcName]
  ) {
    if (payload) {
      obj.webkit.messageHandlers[funcName].postMessage(payload);
    } else {
      obj.webkit.messageHandlers[funcName]();
    }
  }
}

function androidSystemCall(funcName, payload) {
  if (window.HkAndroid) {
    if (payload) {
      window.HkAndroid[funcName](payload);
    } else {
      window.HkAndroid[funcName]();
    }
  }
}

function systemCall(funcName, payload) {
  if (isAndroid()) {
    androidSystemCall(funcName, payload);
  } else if (isIOS()) {
    iOSSystemCall(funcName, payload);
  }
}

/**
 *  获取App 版本号
 */
function getAppVersion() {
  const reg = /hshhk\/(?:ios|android)\/(\d+\.\d+(\.\d+)?)/;
  const ua = navigator.userAgent;
  const match = ua.match(reg);
  if (match) {
    return match[1];
  } else {
    return "Unknown";
  }
}

/**
   *  对比两个版本大小
      0 ： 相等， 1 ： 大于， -1 ： 小于
   */
function compareAppVersion(v1, v2) {
  const reg = /\d+\.\d+(\.\d+)?/;
  if (!reg.test(v1) || !reg.test(v2)) {
    return new Error("请输入正确版本格式！");
  }
  const arr1 = v1.split(".");
  const arr2 = v2.split(".");
  const len1 = arr1.length;
  const len2 = arr2.length;
  const len = len1 > len2 ? len1 : len2;
  for (let i = 0; i < len; i++) {
    const a = i >= len1 ? "0" : arr1[i];
    const b = i >= len2 ? "0" : arr2[i];
    if (a > b) {
      return 1;
    } else if (a < b) {
      return -1;
    }
  }

  return 0;
}

/**
 *  根据App 版本来区别跳转是h5 还是小程序
 *  isUat - App 环境
 *  jumpGame - mygame 游戏code
 */
export function jumpMyGame(isUAT = false, jumpGame = "") {
  const TARGET_APP_VERSION = "11.0";
  const MYGAME_CONFIG = {
    uat: {
      url: "openhkhshlogin://https://uatoss.mylinkapp.hk/mygame/center/web-uat/index.html?thirdPartyToken=<<cmcchkhsh_thirdparty_token_channel=mUBJ5xHj7Y>>&lang=<<cmcchkhsh_cmplang>>",
      appId: "2024092410000001",
    },
    pat: {
      url: "openhkhshlogin://https://cdn.mylinkapp.hk/mygame/center/index.html?thirdPartyToken=<<cmcchkhsh_thirdparty_token_channel=bU9JU0HAZG>>&lang=<<cmcchkhsh_cmplang>>",
      appId: "2024092520000001",
    },
  };
  const appVersion = getAppVersion();
  const payload = {
    appId: isUAT ? MYGAME_CONFIG.uat.appId : MYGAME_CONFIG.pat.appId,
    params: {},
  };

  let url = isUAT ? MYGAME_CONFIG.uat.url : MYGAME_CONFIG.pat.url;
  if (jumpGame) {
    url += `&jumpGame=${jumpGame}`;

    payload.params = {
      jumpGame,
    };
  }

  const cmp = compareAppVersion(appVersion, TARGET_APP_VERSION);
  switch (cmp) {
    case 0:
    case 1:
      // mygame 小程序
      systemCall("mylinkStartMiniApp", JSON.stringify(payload));
      break;
    case -1:
      // h5
      location.href = url;
      break;
  }
}
