import { Method } from 'axios';
import { request, post, get, AxiosParams, registerResponseInterceptor } from './axios';
export type { AxiosParams };
declare function setJwt(jwt: string): boolean;
declare function authRequest<T = any>(method: Method, url: string, opts?: AxiosParams): Promise<T>;
declare function authGet<T = any>(url: string, opts?: AxiosParams): Promise<T>;
declare function authPost<T = any>(url: string, opts?: AxiosParams): Promise<T>;
export declare function useApi(): {
    host: string;
    request: typeof request;
    post: typeof post;
    get: typeof get;
    setJwt: typeof setJwt;
    authRequest: typeof authRequest;
    authGet: typeof authGet;
    authPost: typeof authPost;
    registerResponseInterceptor: typeof registerResponseInterceptor;
};
