<template>
    <div class="userHead-contain">
        <div class="left">
            <img class="head" :src="headLogo" alt="" />
        </div>
        <div class="right">
            <h1>{{ userName }}</h1>
            <p>
                {{ state.task.achievementUnlocked1 }}
                <span class="greed">{{ achieveNum }}</span>
                {{ state.task.achievementUnlocked2 }}
            </p>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted } from 'vue'
import { useLang } from 'hook'
import { useUserStore, useFriendStore } from '@/store'
import { imgs } from "@/assets/imgs/index";
const { state, lang } = useLang()
const userStore = useUserStore()
const userName = computed(() => {
    return userStore?.name
})
defineProps({
    achieveNum: {
        type: Number,
        default: 0
    }
})
const friendStore = useFriendStore()
/** 默认头像 */
const friendHeadLogo =
    'https://cdn.mylink.hk.chinamobile.com/via/resource/activity/2025-05-15/363c48415801449e8cb1fa0218f8aff0.png'
/** 配置用戶头像 */
const headLogo = computed(() => {
    return friendStore.isOwn ? useUserStore().headLogo : friendHeadLogo
})
</script>

<style lang="less" scoped>
.userHead-contain {
    display: flex;
    justify-content: flex-start;
    height: 86px;

    .left {
        width: 86px;
        height: 86px;
        background: #fff;
        border-radius: 50%;

        display: flex;
        align-items: center;
        justify-content: center;

        margin-right: 20px;
        .head {
            width: 70px;
            height: 70px;
            border-radius: 50%;
        }
    }

    .right {
        h1 {
            height: 40px;
            line-height: 40px;
            color: #4e5b7e;
            font-size: 28px;
            margin-bottom: 6px;
        }
        p {
            color: #8490b2;
            font-size: 28px;
            height: 34px;
            line-height: 34px;
            .greed {
                color: #3cbf48;
            }
        }
    }
}
</style>
