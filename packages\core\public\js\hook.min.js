(function(ue,I){typeof exports=="object"&&typeof module!="undefined"?I(exports,require("vue")):typeof define=="function"&&define.amd?define(["exports","vue"],I):(ue=typeof globalThis!="undefined"?globalThis:ue||self,I(ue.Hook={},ue.Vue))})(this,function(ue,I){"use strict";class Ot{use(t,...n){return this}directive(t,n){return this}component(t,n){return this}}const Qt={},Mr="VITE_APP_",Dr=window.__vite_env__,An=Object.keys(Dr);for(let e=0;e<An.length;++e){let t=An[e];t.startsWith(Mr)&&(t=t.substring(Mr.length)),Qt[t]=Dr[An[e]]}Qt.isProd=Qt.NODE_ENV==="production";function Ft(){return Qt}const{API_HOST:mo}=Ft();var Pt={host:mo,defaultHeaders:{"Content-Type":"application/json"},timeout:3e4},gn=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{};function po(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var mn={exports:{}},kr=function(t,n){return function(){for(var s=new Array(arguments.length),a=0;a<s.length;a++)s[a]=arguments[a];return t.apply(n,s)}},Eo=kr,Ke=Object.prototype.toString;function pn(e){return Array.isArray(e)}function En(e){return typeof e=="undefined"}function Co(e){return e!==null&&!En(e)&&e.constructor!==null&&!En(e.constructor)&&typeof e.constructor.isBuffer=="function"&&e.constructor.isBuffer(e)}function vr(e){return Ke.call(e)==="[object ArrayBuffer]"}function Io(e){return Ke.call(e)==="[object FormData]"}function yo(e){var t;return typeof ArrayBuffer!="undefined"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&vr(e.buffer),t}function _o(e){return typeof e=="string"}function wo(e){return typeof e=="number"}function Rr(e){return e!==null&&typeof e=="object"}function Ut(e){if(Ke.call(e)!=="[object Object]")return!1;var t=Object.getPrototypeOf(e);return t===null||t===Object.prototype}function bo(e){return Ke.call(e)==="[object Date]"}function So(e){return Ke.call(e)==="[object File]"}function Lo(e){return Ke.call(e)==="[object Blob]"}function Br(e){return Ke.call(e)==="[object Function]"}function Mo(e){return Rr(e)&&Br(e.pipe)}function Do(e){return Ke.call(e)==="[object URLSearchParams]"}function ko(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function vo(){return typeof navigator!="undefined"&&(navigator.product==="ReactNative"||navigator.product==="NativeScript"||navigator.product==="NS")?!1:typeof window!="undefined"&&typeof document!="undefined"}function Cn(e,t){if(!(e===null||typeof e=="undefined"))if(typeof e!="object"&&(e=[e]),pn(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.call(null,e[s],s,e)}function In(){var e={};function t(s,a){Ut(e[a])&&Ut(s)?e[a]=In(e[a],s):Ut(s)?e[a]=In({},s):pn(s)?e[a]=s.slice():e[a]=s}for(var n=0,r=arguments.length;n<r;n++)Cn(arguments[n],t);return e}function Ro(e,t,n){return Cn(t,function(s,a){n&&typeof s=="function"?e[a]=Eo(s,n):e[a]=s}),e}function Bo(e){return e.charCodeAt(0)===65279&&(e=e.slice(1)),e}var be={isArray:pn,isArrayBuffer:vr,isBuffer:Co,isFormData:Io,isArrayBufferView:yo,isString:_o,isNumber:wo,isObject:Rr,isPlainObject:Ut,isUndefined:En,isDate:bo,isFile:So,isBlob:Lo,isFunction:Br,isStream:Mo,isURLSearchParams:Do,isStandardBrowserEnv:vo,forEach:Cn,merge:In,extend:Ro,trim:ko,stripBOM:Bo},nt=be;function Tr(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var Nr=function(t,n,r){if(!n)return t;var s;if(r)s=r(n);else if(nt.isURLSearchParams(n))s=n.toString();else{var a=[];nt.forEach(n,function(c,f){c===null||typeof c=="undefined"||(nt.isArray(c)?f=f+"[]":c=[c],nt.forEach(c,function(m){nt.isDate(m)?m=m.toISOString():nt.isObject(m)&&(m=JSON.stringify(m)),a.push(Tr(f)+"="+Tr(m))}))}),s=a.join("&")}if(s){var o=t.indexOf("#");o!==-1&&(t=t.slice(0,o)),t+=(t.indexOf("?")===-1?"?":"&")+s}return t},To=be;function xt(){this.handlers=[]}xt.prototype.use=function(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1},xt.prototype.eject=function(t){this.handlers[t]&&(this.handlers[t]=null)},xt.prototype.forEach=function(t){To.forEach(this.handlers,function(r){r!==null&&t(r)})};var No=xt,Oo=be,Qo=function(t,n){Oo.forEach(t,function(s,a){a!==n&&a.toUpperCase()===n.toUpperCase()&&(t[n]=s,delete t[a])})},Or=function(t,n,r,s,a){return t.config=n,r&&(t.code=r),t.request=s,t.response=a,t.isAxiosError=!0,t.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}},t},Qr={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Fo=Or,Fr=function(t,n,r,s,a){var o=new Error(t);return Fo(o,n,r,s,a)},Po=Fr,Uo=function(t,n,r){var s=r.config.validateStatus;!r.status||!s||s(r.status)?t(r):n(Po("Request failed with status code "+r.status,r.config,null,r.request,r))},Gt=be,xo=Gt.isStandardBrowserEnv()?function(){return{write:function(n,r,s,a,o,i){var c=[];c.push(n+"="+encodeURIComponent(r)),Gt.isNumber(s)&&c.push("expires="+new Date(s).toGMTString()),Gt.isString(a)&&c.push("path="+a),Gt.isString(o)&&c.push("domain="+o),i===!0&&c.push("secure"),document.cookie=c.join("; ")},read:function(n){var r=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove:function(n){this.write(n,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}(),Go=function(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)},Ko=function(t,n){return n?t.replace(/\/+$/,"")+"/"+n.replace(/^\/+/,""):t},Jo=Go,jo=Ko,Yo=function(t,n){return t&&!Jo(n)?jo(t,n):n},yn=be,Ho=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"],Wo=function(t){var n={},r,s,a;return t&&yn.forEach(t.split(`
`),function(i){if(a=i.indexOf(":"),r=yn.trim(i.substr(0,a)).toLowerCase(),s=yn.trim(i.substr(a+1)),r){if(n[r]&&Ho.indexOf(r)>=0)return;r==="set-cookie"?n[r]=(n[r]?n[r]:[]).concat([s]):n[r]=n[r]?n[r]+", "+s:s}}),n},Pr=be,Vo=Pr.isStandardBrowserEnv()?function(){var t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a"),r;function s(a){var o=a;return t&&(n.setAttribute("href",o),o=n.href),n.setAttribute("href",o),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:n.pathname.charAt(0)==="/"?n.pathname:"/"+n.pathname}}return r=s(window.location.href),function(o){var i=Pr.isString(o)?s(o):o;return i.protocol===r.protocol&&i.host===r.host}}():function(){return function(){return!0}}();function _n(e){this.message=e}_n.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},_n.prototype.__CANCEL__=!0;var Kt=_n,Jt=be,qo=Uo,$o=xo,Zo=Nr,Xo=Yo,zo=Wo,ei=Vo,wn=Fr,ti=Qr,ni=Kt,Ur=function(t){return new Promise(function(r,s){var a=t.data,o=t.headers,i=t.responseType,c;function f(){t.cancelToken&&t.cancelToken.unsubscribe(c),t.signal&&t.signal.removeEventListener("abort",c)}Jt.isFormData(a)&&delete o["Content-Type"];var l=new XMLHttpRequest;if(t.auth){var m=t.auth.username||"",d=t.auth.password?unescape(encodeURIComponent(t.auth.password)):"";o.Authorization="Basic "+btoa(m+":"+d)}var _=Xo(t.baseURL,t.url);l.open(t.method.toUpperCase(),Zo(_,t.params,t.paramsSerializer),!0),l.timeout=t.timeout;function L(){if(!!l){var S="getAllResponseHeaders"in l?zo(l.getAllResponseHeaders()):null,g=!i||i==="text"||i==="json"?l.responseText:l.response,u={data:g,status:l.status,statusText:l.statusText,headers:S,config:t,request:l};qo(function(A){r(A),f()},function(A){s(A),f()},u),l=null}}if("onloadend"in l?l.onloadend=L:l.onreadystatechange=function(){!l||l.readyState!==4||l.status===0&&!(l.responseURL&&l.responseURL.indexOf("file:")===0)||setTimeout(L)},l.onabort=function(){!l||(s(wn("Request aborted",t,"ECONNABORTED",l)),l=null)},l.onerror=function(){s(wn("Network Error",t,null,l)),l=null},l.ontimeout=function(){var g=t.timeout?"timeout of "+t.timeout+"ms exceeded":"timeout exceeded",u=t.transitional||ti;t.timeoutErrorMessage&&(g=t.timeoutErrorMessage),s(wn(g,t,u.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",l)),l=null},Jt.isStandardBrowserEnv()){var D=(t.withCredentials||ei(_))&&t.xsrfCookieName?$o.read(t.xsrfCookieName):void 0;D&&(o[t.xsrfHeaderName]=D)}"setRequestHeader"in l&&Jt.forEach(o,function(g,u){typeof a=="undefined"&&u.toLowerCase()==="content-type"?delete o[u]:l.setRequestHeader(u,g)}),Jt.isUndefined(t.withCredentials)||(l.withCredentials=!!t.withCredentials),i&&i!=="json"&&(l.responseType=t.responseType),typeof t.onDownloadProgress=="function"&&l.addEventListener("progress",t.onDownloadProgress),typeof t.onUploadProgress=="function"&&l.upload&&l.upload.addEventListener("progress",t.onUploadProgress),(t.cancelToken||t.signal)&&(c=function(S){!l||(s(!S||S&&S.type?new ni("canceled"):S),l.abort(),l=null)},t.cancelToken&&t.cancelToken.subscribe(c),t.signal&&(t.signal.aborted?c():t.signal.addEventListener("abort",c))),a||(a=null),l.send(a)})},Ce=be,xr=Qo,ri=Or,ai=Qr,si={"Content-Type":"application/x-www-form-urlencoded"};function Gr(e,t){!Ce.isUndefined(e)&&Ce.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}function oi(){var e;return(typeof XMLHttpRequest!="undefined"||typeof process!="undefined"&&Object.prototype.toString.call(process)==="[object process]")&&(e=Ur),e}function ii(e,t,n){if(Ce.isString(e))try{return(t||JSON.parse)(e),Ce.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}var jt={transitional:ai,adapter:oi(),transformRequest:[function(t,n){return xr(n,"Accept"),xr(n,"Content-Type"),Ce.isFormData(t)||Ce.isArrayBuffer(t)||Ce.isBuffer(t)||Ce.isStream(t)||Ce.isFile(t)||Ce.isBlob(t)?t:Ce.isArrayBufferView(t)?t.buffer:Ce.isURLSearchParams(t)?(Gr(n,"application/x-www-form-urlencoded;charset=utf-8"),t.toString()):Ce.isObject(t)||n&&n["Content-Type"]==="application/json"?(Gr(n,"application/json"),ii(t)):t}],transformResponse:[function(t){var n=this.transitional||jt.transitional,r=n&&n.silentJSONParsing,s=n&&n.forcedJSONParsing,a=!r&&this.responseType==="json";if(a||s&&Ce.isString(t)&&t.length)try{return JSON.parse(t)}catch(o){if(a)throw o.name==="SyntaxError"?ri(o,this,"E_JSON_PARSE"):o}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};Ce.forEach(["delete","get","head"],function(t){jt.headers[t]={}}),Ce.forEach(["post","put","patch"],function(t){jt.headers[t]=Ce.merge(si)});var bn=jt,li=be,ci=bn,ui=function(t,n,r){var s=this||ci;return li.forEach(r,function(o){t=o.call(s,t,n)}),t},Kr=function(t){return!!(t&&t.__CANCEL__)},Jr=be,Sn=ui,fi=Kr,di=bn,hi=Kt;function Ln(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new hi("canceled")}var Ai=function(t){Ln(t),t.headers=t.headers||{},t.data=Sn.call(t,t.data,t.headers,t.transformRequest),t.headers=Jr.merge(t.headers.common||{},t.headers[t.method]||{},t.headers),Jr.forEach(["delete","get","head","post","put","patch","common"],function(s){delete t.headers[s]});var n=t.adapter||di.adapter;return n(t).then(function(s){return Ln(t),s.data=Sn.call(t,s.data,s.headers,t.transformResponse),s},function(s){return fi(s)||(Ln(t),s&&s.response&&(s.response.data=Sn.call(t,s.response.data,s.response.headers,t.transformResponse))),Promise.reject(s)})},Le=be,jr=function(t,n){n=n||{};var r={};function s(l,m){return Le.isPlainObject(l)&&Le.isPlainObject(m)?Le.merge(l,m):Le.isPlainObject(m)?Le.merge({},m):Le.isArray(m)?m.slice():m}function a(l){if(Le.isUndefined(n[l])){if(!Le.isUndefined(t[l]))return s(void 0,t[l])}else return s(t[l],n[l])}function o(l){if(!Le.isUndefined(n[l]))return s(void 0,n[l])}function i(l){if(Le.isUndefined(n[l])){if(!Le.isUndefined(t[l]))return s(void 0,t[l])}else return s(void 0,n[l])}function c(l){if(l in n)return s(t[l],n[l]);if(l in t)return s(void 0,t[l])}var f={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c};return Le.forEach(Object.keys(t).concat(Object.keys(n)),function(m){var d=f[m]||a,_=d(m);Le.isUndefined(_)&&d!==c||(r[m]=_)}),r},Yr={version:"0.26.1"},gi=Yr.version,Mn={};["object","boolean","number","function","string","symbol"].forEach(function(e,t){Mn[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});var Hr={};Mn.transitional=function(t,n,r){function s(a,o){return"[Axios v"+gi+"] Transitional option '"+a+"'"+o+(r?". "+r:"")}return function(a,o,i){if(t===!1)throw new Error(s(o," has been removed"+(n?" in "+n:"")));return n&&!Hr[o]&&(Hr[o]=!0,console.warn(s(o," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(a,o,i):!0}};function mi(e,t,n){if(typeof e!="object")throw new TypeError("options must be an object");for(var r=Object.keys(e),s=r.length;s-- >0;){var a=r[s],o=t[a];if(o){var i=e[a],c=i===void 0||o(i,a,e);if(c!==!0)throw new TypeError("option "+a+" must be "+c);continue}if(n!==!0)throw Error("Unknown option "+a)}}var pi={assertOptions:mi,validators:Mn},Wr=be,Ei=Nr,Vr=No,qr=Ai,Yt=jr,$r=pi,rt=$r.validators;function mt(e){this.defaults=e,this.interceptors={request:new Vr,response:new Vr}}mt.prototype.request=function(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Yt(this.defaults,n),n.method?n.method=n.method.toLowerCase():this.defaults.method?n.method=this.defaults.method.toLowerCase():n.method="get";var r=n.transitional;r!==void 0&&$r.assertOptions(r,{silentJSONParsing:rt.transitional(rt.boolean),forcedJSONParsing:rt.transitional(rt.boolean),clarifyTimeoutError:rt.transitional(rt.boolean)},!1);var s=[],a=!0;this.interceptors.request.forEach(function(_){typeof _.runWhen=="function"&&_.runWhen(n)===!1||(a=a&&_.synchronous,s.unshift(_.fulfilled,_.rejected))});var o=[];this.interceptors.response.forEach(function(_){o.push(_.fulfilled,_.rejected)});var i;if(!a){var c=[qr,void 0];for(Array.prototype.unshift.apply(c,s),c=c.concat(o),i=Promise.resolve(n);c.length;)i=i.then(c.shift(),c.shift());return i}for(var f=n;s.length;){var l=s.shift(),m=s.shift();try{f=l(f)}catch(d){m(d);break}}try{i=qr(f)}catch(d){return Promise.reject(d)}for(;o.length;)i=i.then(o.shift(),o.shift());return i},mt.prototype.getUri=function(t){return t=Yt(this.defaults,t),Ei(t.url,t.params,t.paramsSerializer).replace(/^\?/,"")},Wr.forEach(["delete","get","head","options"],function(t){mt.prototype[t]=function(n,r){return this.request(Yt(r||{},{method:t,url:n,data:(r||{}).data}))}}),Wr.forEach(["post","put","patch"],function(t){mt.prototype[t]=function(n,r,s){return this.request(Yt(s||{},{method:t,url:n,data:r}))}});var Ci=mt,Ii=Kt;function at(e){if(typeof e!="function")throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(s){t=s});var n=this;this.promise.then(function(r){if(!!n._listeners){var s,a=n._listeners.length;for(s=0;s<a;s++)n._listeners[s](r);n._listeners=null}}),this.promise.then=function(r){var s,a=new Promise(function(o){n.subscribe(o),s=o}).then(r);return a.cancel=function(){n.unsubscribe(s)},a},e(function(s){n.reason||(n.reason=new Ii(s),t(n.reason))})}at.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},at.prototype.subscribe=function(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]},at.prototype.unsubscribe=function(t){if(!!this._listeners){var n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}},at.source=function(){var t,n=new at(function(s){t=s});return{token:n,cancel:t}};var yi=at,_i=function(t){return function(r){return t.apply(null,r)}},wi=be,bi=function(t){return wi.isObject(t)&&t.isAxiosError===!0},Zr=be,Si=kr,Ht=Ci,Li=jr,Mi=bn;function Xr(e){var t=new Ht(e),n=Si(Ht.prototype.request,t);return Zr.extend(n,Ht.prototype,t),Zr.extend(n,t),n.create=function(s){return Xr(Li(e,s))},n}var Be=Xr(Mi);Be.Axios=Ht,Be.Cancel=Kt,Be.CancelToken=yi,Be.isCancel=Kr,Be.VERSION=Yr.version,Be.all=function(t){return Promise.all(t)},Be.spread=_i,Be.isAxiosError=bi,mn.exports=Be,mn.exports.default=Be;var Di=mn.exports,zr={exports:{}};/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */(function(e,t){(function(r,s){e.exports=s()})(gn,function(){return function(){var n={686:function(a,o,i){i.d(o,{default:function(){return se}});var c=i(279),f=i.n(c),l=i(370),m=i.n(l),d=i(817),_=i.n(d);function L(J){try{return document.execCommand(J)}catch{return!1}}var D=function(Q){var F=_()(Q);return L("cut"),F},S=D;function g(J){var Q=document.documentElement.getAttribute("dir")==="rtl",F=document.createElement("textarea");F.style.fontSize="12pt",F.style.border="0",F.style.padding="0",F.style.margin="0",F.style.position="absolute",F.style[Q?"right":"left"]="-9999px";var H=window.pageYOffset||document.documentElement.scrollTop;return F.style.top="".concat(H,"px"),F.setAttribute("readonly",""),F.value=J,F}var u=function(Q,F){var H=g(Q);F.container.appendChild(H);var $=_()(H);return L("copy"),H.remove(),$},p=function(Q){var F=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body},H="";return typeof Q=="string"?H=u(Q,F):Q instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(Q==null?void 0:Q.type)?H=u(Q.value,F):(H=_()(Q),L("copy")),H},A=p;function y(J){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?y=function(F){return typeof F}:y=function(F){return F&&typeof Symbol=="function"&&F.constructor===Symbol&&F!==Symbol.prototype?"symbol":typeof F},y(J)}var E=function(){var Q=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},F=Q.action,H=F===void 0?"copy":F,$=Q.container,w=Q.target,T=Q.text;if(H!=="copy"&&H!=="cut")throw new Error('Invalid "action" value, use either "copy" or "cut"');if(w!==void 0)if(w&&y(w)==="object"&&w.nodeType===1){if(H==="copy"&&w.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if(H==="cut"&&(w.hasAttribute("readonly")||w.hasAttribute("disabled")))throw new Error(`Invalid "target" attribute. You can't cut text from elements with "readonly" or "disabled" attributes`)}else throw new Error('Invalid "target" value, use a valid Element');if(T)return A(T,{container:$});if(w)return H==="cut"?S(w):A(w,{container:$})},M=E;function k(J){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?k=function(F){return typeof F}:k=function(F){return F&&typeof Symbol=="function"&&F.constructor===Symbol&&F!==Symbol.prototype?"symbol":typeof F},k(J)}function j(J,Q){if(!(J instanceof Q))throw new TypeError("Cannot call a class as a function")}function q(J,Q){for(var F=0;F<Q.length;F++){var H=Q[F];H.enumerable=H.enumerable||!1,H.configurable=!0,"value"in H&&(H.writable=!0),Object.defineProperty(J,H.key,H)}}function W(J,Q,F){return Q&&q(J.prototype,Q),F&&q(J,F),J}function ee(J,Q){if(typeof Q!="function"&&Q!==null)throw new TypeError("Super expression must either be null or a function");J.prototype=Object.create(Q&&Q.prototype,{constructor:{value:J,writable:!0,configurable:!0}}),Q&&ne(J,Q)}function ne(J,Q){return ne=Object.setPrototypeOf||function(H,$){return H.__proto__=$,H},ne(J,Q)}function x(J){var Q=Y();return function(){var H=N(J),$;if(Q){var w=N(this).constructor;$=Reflect.construct(H,arguments,w)}else $=H.apply(this,arguments);return O(this,$)}}function O(J,Q){return Q&&(k(Q)==="object"||typeof Q=="function")?Q:R(J)}function R(J){if(J===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return J}function Y(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function N(J){return N=Object.setPrototypeOf?Object.getPrototypeOf:function(F){return F.__proto__||Object.getPrototypeOf(F)},N(J)}function Z(J,Q){var F="data-clipboard-".concat(J);if(!!Q.hasAttribute(F))return Q.getAttribute(F)}var X=function(J){ee(F,J);var Q=x(F);function F(H,$){var w;return j(this,F),w=Q.call(this),w.resolveOptions($),w.listenClick(H),w}return W(F,[{key:"resolveOptions",value:function(){var $=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.action=typeof $.action=="function"?$.action:this.defaultAction,this.target=typeof $.target=="function"?$.target:this.defaultTarget,this.text=typeof $.text=="function"?$.text:this.defaultText,this.container=k($.container)==="object"?$.container:document.body}},{key:"listenClick",value:function($){var w=this;this.listener=m()($,"click",function(T){return w.onClick(T)})}},{key:"onClick",value:function($){var w=$.delegateTarget||$.currentTarget,T=this.action(w)||"copy",B=M({action:T,container:this.container,target:this.target(w),text:this.text(w)});this.emit(B?"success":"error",{action:T,text:B,trigger:w,clearSelection:function(){w&&w.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function($){return Z("action",$)}},{key:"defaultTarget",value:function($){var w=Z("target",$);if(w)return document.querySelector(w)}},{key:"defaultText",value:function($){return Z("text",$)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function($){var w=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body};return A($,w)}},{key:"cut",value:function($){return S($)}},{key:"isSupported",value:function(){var $=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["copy","cut"],w=typeof $=="string"?[$]:$,T=!!document.queryCommandSupported;return w.forEach(function(B){T=T&&!!document.queryCommandSupported(B)}),T}}]),F}(f()),se=X},828:function(a){var o=9;if(typeof Element!="undefined"&&!Element.prototype.matches){var i=Element.prototype;i.matches=i.matchesSelector||i.mozMatchesSelector||i.msMatchesSelector||i.oMatchesSelector||i.webkitMatchesSelector}function c(f,l){for(;f&&f.nodeType!==o;){if(typeof f.matches=="function"&&f.matches(l))return f;f=f.parentNode}}a.exports=c},438:function(a,o,i){var c=i(828);function f(d,_,L,D,S){var g=m.apply(this,arguments);return d.addEventListener(L,g,S),{destroy:function(){d.removeEventListener(L,g,S)}}}function l(d,_,L,D,S){return typeof d.addEventListener=="function"?f.apply(null,arguments):typeof L=="function"?f.bind(null,document).apply(null,arguments):(typeof d=="string"&&(d=document.querySelectorAll(d)),Array.prototype.map.call(d,function(g){return f(g,_,L,D,S)}))}function m(d,_,L,D){return function(S){S.delegateTarget=c(S.target,_),S.delegateTarget&&D.call(d,S)}}a.exports=l},879:function(a,o){o.node=function(i){return i!==void 0&&i instanceof HTMLElement&&i.nodeType===1},o.nodeList=function(i){var c=Object.prototype.toString.call(i);return i!==void 0&&(c==="[object NodeList]"||c==="[object HTMLCollection]")&&"length"in i&&(i.length===0||o.node(i[0]))},o.string=function(i){return typeof i=="string"||i instanceof String},o.fn=function(i){var c=Object.prototype.toString.call(i);return c==="[object Function]"}},370:function(a,o,i){var c=i(879),f=i(438);function l(L,D,S){if(!L&&!D&&!S)throw new Error("Missing required arguments");if(!c.string(D))throw new TypeError("Second argument must be a String");if(!c.fn(S))throw new TypeError("Third argument must be a Function");if(c.node(L))return m(L,D,S);if(c.nodeList(L))return d(L,D,S);if(c.string(L))return _(L,D,S);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function m(L,D,S){return L.addEventListener(D,S),{destroy:function(){L.removeEventListener(D,S)}}}function d(L,D,S){return Array.prototype.forEach.call(L,function(g){g.addEventListener(D,S)}),{destroy:function(){Array.prototype.forEach.call(L,function(g){g.removeEventListener(D,S)})}}}function _(L,D,S){return f(document.body,L,D,S)}a.exports=l},817:function(a){function o(i){var c;if(i.nodeName==="SELECT")i.focus(),c=i.value;else if(i.nodeName==="INPUT"||i.nodeName==="TEXTAREA"){var f=i.hasAttribute("readonly");f||i.setAttribute("readonly",""),i.select(),i.setSelectionRange(0,i.value.length),f||i.removeAttribute("readonly"),c=i.value}else{i.hasAttribute("contenteditable")&&i.focus();var l=window.getSelection(),m=document.createRange();m.selectNodeContents(i),l.removeAllRanges(),l.addRange(m),c=l.toString()}return c}a.exports=o},279:function(a){function o(){}o.prototype={on:function(i,c,f){var l=this.e||(this.e={});return(l[i]||(l[i]=[])).push({fn:c,ctx:f}),this},once:function(i,c,f){var l=this;function m(){l.off(i,m),c.apply(f,arguments)}return m._=c,this.on(i,m,f)},emit:function(i){var c=[].slice.call(arguments,1),f=((this.e||(this.e={}))[i]||[]).slice(),l=0,m=f.length;for(l;l<m;l++)f[l].fn.apply(f[l].ctx,c);return this},off:function(i,c){var f=this.e||(this.e={}),l=f[i],m=[];if(l&&c)for(var d=0,_=l.length;d<_;d++)l[d].fn!==c&&l[d].fn._!==c&&m.push(l[d]);return m.length?f[i]=m:delete f[i],this}},a.exports=o,a.exports.TinyEmitter=o}},r={};function s(a){if(r[a])return r[a].exports;var o=r[a]={exports:{}};return n[a](o,o.exports,s),o.exports}return function(){s.n=function(a){var o=a&&a.__esModule?function(){return a.default}:function(){return a};return s.d(o,{a:o}),o}}(),function(){s.d=function(a,o){for(var i in o)s.o(o,i)&&!s.o(a,i)&&Object.defineProperty(a,i,{enumerable:!0,get:o[i]})}}(),function(){s.o=function(a,o){return Object.prototype.hasOwnProperty.call(a,o)}}(),s(686)}().default})})(zr);var ki=po(zr.exports);class vi{constructor(t){this.mode=4,this.data="",this.parsedData=[],this.data=t;for(let n=0,r=this.data.length;n<r;n++){let s=[],a=this.data.charCodeAt(n);a>65536?(s[0]=240|(a&1835008)>>>18,s[1]=128|(a&258048)>>>12,s[2]=128|(a&4032)>>>6,s[3]=128|a&63):a>2048?(s[0]=224|(a&61440)>>>12,s[1]=128|(a&4032)>>>6,s[2]=128|a&63):a>128?(s[0]=192|(a&1984)>>>6,s[1]=128|a&63):s[0]=a,this.parsedData.push(s)}this.parsedData=Array.prototype.concat.apply([],this.parsedData),this.parsedData.length!==this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}getLength(){return this.parsedData.length}write(t){for(let n=0,r=this.parsedData.length;n<r;n++)t.put(this.parsedData[n],8)}}const pt=class{constructor(e,t){this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[],this.typeNumber=e,this.errorCorrectLevel=t}static createData(e,t,n){let r=ea.getRSBlocks(e,t),s=new Ri;for(let o=0;o<n.length;o++){let i=n[o];s.put(i.mode,4),s.put(i.getLength(),de.getLengthInBits(i.mode,e)),i.write(s)}let a=0;for(let o=0;o<r.length;o++)a+=r[o].dataCount;if(s.getLengthInBits()>a*8)throw new Error("code length overflow. ("+s.getLengthInBits()+">"+a*8+")");for(s.getLengthInBits()+4<=a*8&&s.put(0,4);s.getLengthInBits()%8!==0;)s.putBit(!1);for(;!(s.getLengthInBits()>=a*8||(s.put(pt.PAD0,8),s.getLengthInBits()>=a*8));)s.put(pt.PAD1,8);return pt.createBytes(s,r)}static createBytes(e,t){let n=0,r=0,s=0,a=new Array(t.length),o=new Array(t.length);for(let l=0;l<t.length;l++){let m=t[l].dataCount,d=t[l].totalCount-m;r=Math.max(r,m),s=Math.max(s,d),a[l]=new Array(m);for(let S=0;S<a[l].length;S++)a[l][S]=255&e.buffer[S+n];n+=m;let _=de.getErrorCorrectPolynomial(d),D=new st(a[l],_.getLength()-1).mod(_);o[l]=new Array(_.getLength()-1);for(let S=0;S<o[l].length;S++){let g=S+D.getLength()-o[l].length;o[l][S]=g>=0?D.get(g):0}}let i=0;for(let l=0;l<t.length;l++)i+=t[l].totalCount;let c=new Array(i),f=0;for(let l=0;l<r;l++)for(let m=0;m<t.length;m++)l<a[m].length&&(c[f++]=a[m][l]);for(let l=0;l<s;l++)for(let m=0;m<t.length;m++)l<o[m].length&&(c[f++]=o[m][l]);return c}addData(e){let t=new vi(e);this.dataList.push(t),this.dataCache=null}isDark(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(e+","+t);return this.modules[e][t]}getModuleCount(){return this.moduleCount}make(){this.makeImpl(!1,this.getBestMaskPattern())}makeImpl(e,t){this.moduleCount=this.typeNumber*4+17,this.modules=new Array(this.moduleCount);for(let n=0;n<this.moduleCount;n++){this.modules[n]=new Array(this.moduleCount);for(let r=0;r<this.moduleCount;r++)this.modules[n][r]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),this.dataCache==null&&(this.dataCache=pt.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)}setupPositionProbePattern(e,t){for(let n=-1;n<=7;n++)if(!(e+n<=-1||this.moduleCount<=e+n))for(let r=-1;r<=7;r++)t+r<=-1||this.moduleCount<=t+r||(0<=n&&n<=6&&(r===0||r===6)||0<=r&&r<=6&&(n===0||n===6)||2<=n&&n<=4&&2<=r&&r<=4?this.modules[e+n][t+r]=!0:this.modules[e+n][t+r]=!1)}getBestMaskPattern(){let e=0,t=0;for(let n=0;n<8;n++){this.makeImpl(!0,n);let r=de.getLostPoint(this);(n===0||e>r)&&(e=r,t=n)}return t}createMovieClip(e,t,n){let r=e.createEmptyMovieClip(t,n),s=1;this.make();for(let a=0;a<this.modules.length;a++){let o=a*s;for(let i=0;i<this.modules[a].length;i++){let c=i*s;this.modules[a][i]&&(r.beginFill(0,100),r.moveTo(c,o),r.lineTo(c+s,o),r.lineTo(c+s,o+s),r.lineTo(c,o+s),r.endFill())}}return r}setupTimingPattern(){for(let e=8;e<this.moduleCount-8;e++)this.modules[e][6]==null&&(this.modules[e][6]=e%2===0);for(let e=8;e<this.moduleCount-8;e++)this.modules[6][e]==null&&(this.modules[6][e]=e%2===0)}setupPositionAdjustPattern(){let e=de.getPatternPosition(this.typeNumber);for(let t=0;t<e.length;t++)for(let n=0;n<e.length;n++){let r=e[t],s=e[n];if(this.modules[r][s]==null)for(let a=-2;a<=2;a++)for(let o=-2;o<=2;o++)a===-2||a===2||o===-2||o===2||a===0&&o===0?this.modules[r+a][s+o]=!0:this.modules[r+a][s+o]=!1}}setupTypeNumber(e){let t=de.getBCHTypeNumber(this.typeNumber);for(let n=0;n<18;n++){let r=!e&&(t>>n&1)===1;this.modules[Math.floor(n/3)][n%3+this.moduleCount-8-3]=r}for(let n=0;n<18;n++){let r=!e&&(t>>n&1)===1;this.modules[n%3+this.moduleCount-8-3][Math.floor(n/3)]=r}}setupTypeInfo(e,t){let n=this.errorCorrectLevel<<3|t,r=de.getBCHTypeInfo(n);for(let s=0;s<15;s++){let a=!e&&(r>>s&1)===1;s<6?this.modules[s][8]=a:s<8?this.modules[s+1][8]=a:this.modules[this.moduleCount-15+s][8]=a}for(let s=0;s<15;s++){let a=!e&&(r>>s&1)===1;s<8?this.modules[8][this.moduleCount-s-1]=a:s<9?this.modules[8][15-s-1+1]=a:this.modules[8][15-s-1]=a}this.modules[this.moduleCount-8][8]=!e}mapData(e,t){let n=-1,r=this.moduleCount-1,s=7,a=0;for(let o=this.moduleCount-1;o>0;o-=2)for(o===6&&o--;;){for(let i=0;i<2;i++)if(this.modules[r][o-i]==null){let c=!1;a<e.length&&(c=(e[a]>>>s&1)===1),de.getMask(t,r,o-i)&&(c=!c),this.modules[r][o-i]=c,s--,s===-1&&(a++,s=7)}if(r+=n,r<0||this.moduleCount<=r){r-=n,n=-n;break}}}};let Dn=pt;Dn.PAD0=236,Dn.PAD1=17;let de={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1<<10|1<<8|1<<5|1<<4|1<<2|1<<1|1<<0,G18:1<<12|1<<11|1<<10|1<<9|1<<8|1<<5|1<<2|1<<0,G15_MASK:1<<14|1<<12|1<<10|1<<4|1<<1,getBCHTypeInfo(e){let t=e<<10;for(;de.getBCHDigit(t)-de.getBCHDigit(de.G15)>=0;)t^=de.G15<<de.getBCHDigit(t)-de.getBCHDigit(de.G15);return(e<<10|t)^de.G15_MASK},getBCHTypeNumber(e){let t=e<<12;for(;de.getBCHDigit(t)-de.getBCHDigit(de.G18)>=0;)t^=de.G18<<de.getBCHDigit(t)-de.getBCHDigit(de.G18);return e<<12|t},getBCHDigit(e){let t=0;for(;e!==0;)t++,e>>>=1;return t},getPatternPosition(e){return de.PATTERN_POSITION_TABLE[e-1]},getMask(e,t,n){switch(e){case 0:return(t+n)%2===0;case 1:return t%2===0;case 2:return n%3===0;case 3:return(t+n)%3===0;case 4:return(Math.floor(t/2)+Math.floor(n/3))%2===0;case 5:return t*n%2+t*n%3===0;case 6:return(t*n%2+t*n%3)%2===0;case 7:return(t*n%3+(t+n)%2)%2===0;default:throw new Error("bad maskPattern:"+e)}},getErrorCorrectPolynomial(e){let t=new st([1],0);for(let n=0;n<e;n++)t=t.multiply(new st([1,pe.gexp(n)],0));return t},getLengthInBits(e,t){if(1<=t&&t<10)switch(e){case 1:return 10;case 2:return 9;case 4:return 8;case 8:return 8;default:throw new Error("mode:"+e)}else if(t<27)switch(e){case 1:return 12;case 2:return 11;case 4:return 16;case 8:return 10;default:throw new Error("mode:"+e)}else if(t<41)switch(e){case 1:return 14;case 2:return 13;case 4:return 16;case 8:return 12;default:throw new Error("mode:"+e)}else throw new Error("type:"+t)},getLostPoint(e){let t=e.getModuleCount(),n=0;for(let a=0;a<t;a++)for(let o=0;o<t;o++){let i=0,c=e.isDark(a,o);for(let f=-1;f<=1;f++)if(!(a+f<0||t<=a+f))for(let l=-1;l<=1;l++)o+l<0||t<=o+l||f===0&&l===0||c===e.isDark(a+f,o+l)&&i++;i>5&&(n+=3+i-5)}for(let a=0;a<t-1;a++)for(let o=0;o<t-1;o++){let i=0;e.isDark(a,o)&&i++,e.isDark(a+1,o)&&i++,e.isDark(a,o+1)&&i++,e.isDark(a+1,o+1)&&i++,(i===0||i===4)&&(n+=3)}for(let a=0;a<t;a++)for(let o=0;o<t-6;o++)e.isDark(a,o)&&!e.isDark(a,o+1)&&e.isDark(a,o+2)&&e.isDark(a,o+3)&&e.isDark(a,o+4)&&!e.isDark(a,o+5)&&e.isDark(a,o+6)&&(n+=40);for(let a=0;a<t;a++)for(let o=0;o<t-6;o++)e.isDark(o,a)&&!e.isDark(o+1,a)&&e.isDark(o+2,a)&&e.isDark(o+3,a)&&e.isDark(o+4,a)&&!e.isDark(o+5,a)&&e.isDark(o+6,a)&&(n+=40);let r=0;for(let a=0;a<t;a++)for(let o=0;o<t;o++)e.isDark(o,a)&&r++;return n+=Math.abs(100*r/t/t-50)/5*10,n}},pe={glog(e){if(e<1)throw new Error("glog("+e+")");return pe.LOG_TABLE[e]},gexp(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return pe.EXP_TABLE[e]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)};for(let e=0;e<8;e++)pe.EXP_TABLE[e]=1<<e;for(let e=8;e<256;e++)pe.EXP_TABLE[e]=pe.EXP_TABLE[e-4]^pe.EXP_TABLE[e-5]^pe.EXP_TABLE[e-6]^pe.EXP_TABLE[e-8];for(let e=0;e<255;e++)pe.LOG_TABLE[pe.EXP_TABLE[e]]=e;class st{constructor(t,n){if(t.length===void 0)throw new Error(t.length+"/home"+n);let r=0;for(;r<t.length&&t[r]===0;)r++;this.num=new Array(t.length-r+n);for(let s=0;s<t.length-r;s++)this.num[s]=t[s+r]}get(t){return this.num[t]}getLength(){return this.num.length}multiply(t){let n=new Array(this.getLength()+t.getLength()-1);for(let r=0;r<this.getLength();r++)for(let s=0;s<t.getLength();s++)n[r+s]^=pe.gexp(pe.glog(this.get(r))+pe.glog(t.get(s)));return new st(n,0)}mod(t){if(this.getLength()-t.getLength()<0)return this;let n=pe.glog(this.get(0))-pe.glog(t.get(0)),r=new Array(this.getLength());for(let s=0;s<this.getLength();s++)r[s]=this.get(s);for(let s=0;s<t.getLength();s++)r[s]^=pe.gexp(pe.glog(t.get(s))+n);return new st(r,0).mod(t)}}const $e=class{static getRSBlocks(e,t){let n=$e.getRsBlockTable(e,t);if(n===void 0)throw new Error("bad rs block @ typeNumber:"+e+"/errorCorrectLevel:"+t);let r=n.length/3,s=[];for(let a=0;a<r;a++){let o=n[a*3+0],i=n[a*3+1],c=n[a*3+2];for(let f=0;f<o;f++)s.push(new $e(i,c))}return s}static getRsBlockTable(e,t){switch(t){case 1:return $e.RS_BLOCK_TABLE[(e-1)*4+0];case 0:return $e.RS_BLOCK_TABLE[(e-1)*4+1];case 3:return $e.RS_BLOCK_TABLE[(e-1)*4+2];case 2:return $e.RS_BLOCK_TABLE[(e-1)*4+3];default:return}}constructor(e,t){this.totalCount=e,this.dataCount=t}};let ea=$e;ea.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];class Ri{constructor(){this.buffer=[],this.length=0}get(t){let n=Math.floor(t/8);return(this.buffer[n]>>>7-t%8&1)===1}put(t,n){for(let r=0;r<n;r++)this.putBit((t>>>n-r-1&1)===1)}getLengthInBits(){return this.length}putBit(t){let n=Math.floor(this.length/8);this.buffer.length<=n&&this.buffer.push(0),t&&(this.buffer[n]|=128>>>this.length%8),this.length++}}let ot=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];function Bi(e){let t=encodeURI(e).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return t.length+(t.length!==e?3:0)}function Ti(e,t){let n=1,r=Bi(e);for(let s=0,a=ot.length;s<=a;s++){let o=0;switch(t){case 1:o=ot[s][0];break;case 0:o=ot[s][1];break;case 3:o=ot[s][2];break;case 2:o=ot[s][3];break}if(r<=o)break;n++}if(n>ot.length)throw new Error("Too long data");return n}class Ni{constructor(){this.opt={width:256,height:256,colorDark:"#000000",colorLight:"#ffffff",correctLevel:2},this.opt={width:256,height:256,colorDark:"#000000",colorLight:"#ffffff",correctLevel:2}}init(t){return Object.assign(this.opt,t),this}create(t,n){typeof t!="string"&&(t=JSON.stringify(t)),t=t||"",this.opt=Object.assign(this.opt,n);let r=new Dn(Ti(t,this.opt.correctLevel),this.opt.correctLevel);r.addData(t),r.make();let s=document.createElement("canvas"),a=s.getContext("2d");s.width=this.opt.width,s.height=this.opt.height;let o=r.getModuleCount(),i=this.opt.width/o,c=this.opt.height/o,f=Math.round(i),l=Math.round(c);for(let m=0;m<o;m++)for(let d=0;d<o;d++){let _=r.isDark(m,d),L=d*i,D=m*c;a.strokeStyle=_?this.opt.colorDark:this.opt.colorLight,a.lineWidth=1,a.fillStyle=_?this.opt.colorDark:this.opt.colorLight,a.fillRect(L,D,i,c),a.strokeRect(Math.floor(L)+.5,Math.floor(D)+.5,f,l),a.strokeRect(Math.ceil(L)-.5,Math.ceil(D)-.5,f,l)}return s.toDataURL("image/png")}}var kn={exports:{}};/*!
 * md5js v1.0.7
 * (c) 2017-2018 penyuying
 * Released under the MIT License.
 */(function(e,t){(function(n,r){r(t)})(gn,function(n){n.md5=function(r,s){function a(M,k){return M<<k|M>>>32-k}function o(M,k){var j,q,W,ee,ne;return W=**********&M,ee=**********&k,j=**********&M,q=**********&k,ne=(**********&M)+(**********&k),j&q?**********^ne^W^ee:j|q?**********&ne?**********^ne^W^ee:**********^ne^W^ee:ne^W^ee}function i(M,k,j,q,W,ee,ne){return M=o(M,o(o(function(x,O,R){return x&O|~x&R}(k,j,q),W),ne)),o(a(M,ee),k)}function c(M,k,j,q,W,ee,ne){return M=o(M,o(o(function(x,O,R){return x&R|O&~R}(k,j,q),W),ne)),o(a(M,ee),k)}function f(M,k,j,q,W,ee,ne){return M=o(M,o(o(function(x,O,R){return x^O^R}(k,j,q),W),ne)),o(a(M,ee),k)}function l(M,k,j,q,W,ee,ne){return M=o(M,o(o(function(x,O,R){return O^(x|~R)}(k,j,q),W),ne)),o(a(M,ee),k)}function m(M){var k,j="",q="";for(k=0;k<=3;k++)j+=(q="0"+(M>>>8*k&255).toString(16)).substr(q.length-2,2);return j}var d,_,L,D,S,g,u,p,A,y=r,E=Array();for(E=function(M){for(var k,j=M.length,q=j+8,W=16*((q-q%64)/64+1),ee=Array(W-1),ne=0,x=0;x<j;)ne=x%4*8,ee[k=(x-x%4)/4]=ee[k]|M.charCodeAt(x)<<ne,x++;return k=(x-x%4)/4,ne=x%4*8,ee[k]=ee[k]|128<<ne,ee[W-2]=j<<3,ee[W-1]=j>>>29,ee}(y),g=1732584193,u=4023233417,p=2562383102,A=271733878,d=0;d<E.length;d+=16)_=g,L=u,D=p,S=A,u=l(u=l(u=l(u=l(u=f(u=f(u=f(u=f(u=c(u=c(u=c(u=c(u=i(u=i(u=i(u=i(u,p=i(p,A=i(A,g=i(g,u,p,A,E[d+0],7,3614090360),u,p,E[d+1],12,3905402710),g,u,E[d+2],17,606105819),A,g,E[d+3],22,3250441966),p=i(p,A=i(A,g=i(g,u,p,A,E[d+4],7,4118548399),u,p,E[d+5],12,1200080426),g,u,E[d+6],17,2821735955),A,g,E[d+7],22,4249261313),p=i(p,A=i(A,g=i(g,u,p,A,E[d+8],7,1770035416),u,p,E[d+9],12,2336552879),g,u,E[d+10],17,4294925233),A,g,E[d+11],22,2304563134),p=i(p,A=i(A,g=i(g,u,p,A,E[d+12],7,1804603682),u,p,E[d+13],12,4254626195),g,u,E[d+14],17,2792965006),A,g,E[d+15],22,1236535329),p=c(p,A=c(A,g=c(g,u,p,A,E[d+1],5,4129170786),u,p,E[d+6],9,3225465664),g,u,E[d+11],14,643717713),A,g,E[d+0],20,3921069994),p=c(p,A=c(A,g=c(g,u,p,A,E[d+5],5,3593408605),u,p,E[d+10],9,38016083),g,u,E[d+15],14,3634488961),A,g,E[d+4],20,3889429448),p=c(p,A=c(A,g=c(g,u,p,A,E[d+9],5,568446438),u,p,E[d+14],9,3275163606),g,u,E[d+3],14,4107603335),A,g,E[d+8],20,1163531501),p=c(p,A=c(A,g=c(g,u,p,A,E[d+13],5,2850285829),u,p,E[d+2],9,4243563512),g,u,E[d+7],14,1735328473),A,g,E[d+12],20,2368359562),p=f(p,A=f(A,g=f(g,u,p,A,E[d+5],4,4294588738),u,p,E[d+8],11,2272392833),g,u,E[d+11],16,1839030562),A,g,E[d+14],23,4259657740),p=f(p,A=f(A,g=f(g,u,p,A,E[d+1],4,2763975236),u,p,E[d+4],11,1272893353),g,u,E[d+7],16,4139469664),A,g,E[d+10],23,3200236656),p=f(p,A=f(A,g=f(g,u,p,A,E[d+13],4,681279174),u,p,E[d+0],11,3936430074),g,u,E[d+3],16,3572445317),A,g,E[d+6],23,76029189),p=f(p,A=f(A,g=f(g,u,p,A,E[d+9],4,3654602809),u,p,E[d+12],11,3873151461),g,u,E[d+15],16,530742520),A,g,E[d+2],23,3299628645),p=l(p,A=l(A,g=l(g,u,p,A,E[d+0],6,4096336452),u,p,E[d+7],10,1126891415),g,u,E[d+14],15,2878612391),A,g,E[d+5],21,4237533241),p=l(p,A=l(A,g=l(g,u,p,A,E[d+12],6,1700485571),u,p,E[d+3],10,2399980690),g,u,E[d+10],15,4293915773),A,g,E[d+1],21,2240044497),p=l(p,A=l(A,g=l(g,u,p,A,E[d+8],6,1873313359),u,p,E[d+15],10,4264355552),g,u,E[d+6],15,2734768916),A,g,E[d+13],21,1309151649),p=l(p,A=l(A,g=l(g,u,p,A,E[d+4],6,4149444226),u,p,E[d+11],10,3174756917),g,u,E[d+2],15,718787259),A,g,E[d+9],21,3951481745),g=o(g,_),u=o(u,L),p=o(p,D),A=o(A,S);return s==32?m(g)+m(u)+m(p)+m(A):m(u)+m(p)},Object.defineProperty(n,"__esModule",{value:!0})})})(kn,kn.exports);function Oi(e){const t=document.createElement("div");new ki(t,{text:()=>e,action:()=>"copy"}),t.click()}function Qi(e){function t(s){return s.split("&").map(a=>a.split("=")).reduce((a,o)=>(a[o[0]]=unescape(decodeURIComponent(o[1])),a),{})}e=e||"";const n={query:{},hashQuery:{}};let r=e.split("#")[0].split("?")[1]||"";return r&&(n.query=t(r)),r=(e.split("#")[1]||"").split("?")[1]||"",r&&(n.hashQuery=t(r)),n}function Fi(e){return typeof Array.isArray=="function"?Array.isArray(e):Object.prototype.toString.call(e)==="[object Array]"}function ta(e,t){return((r,s)=>{for(let a in s)r[a]&&r[a].toString()==="[object Object]"?r[a]=ta(r[a],s[a]):r[a]=s[a];return r})(JSON.parse(JSON.stringify(e)),t)}function Pi(e,t){let n=e.split("."),r=t.split(".");const s=Math.max(n.length,r.length);for(;n.length<s;)n.push("0");for(;r.length<s;)r.push("0");for(let a=0;a<s;a++){const o=Number(n[a]),i=Number(r[a]);if(o>i)return 1;if(o<i)return-1}return 0}function Ui(e){return e!==null&&typeof e=="object"}function Wt(){return{copyText:Oi,loadUrlQuery:Qi,isArray:Fi,deepObjectMerge:ta,compareVersion:Pi,QRCode:Ni,md5:kn.exports.md5,isObject:Ui}}const{md5:xi}=Wt(),Gi=Di.create({baseURL:Pt.host,headers:Pt.defaultHeaders,timeout:Pt.timeout}),Vt={},vn=[],Ze={};function Rn(e){return Object.entries(e).map(([t,n])=>`${t}=${JSON.stringify(n)}`).sort().join("&")}function Ki(e,t,n,r,s){const a=[e,t,Rn(r),Rn(n),Rn(s)].join("_");return xi(encodeURI(a),16)}async function Ji(e,t){return t.then(n=>{if(Vt[e]=n,vn.push(e),vn.length>20){const r=vn.shift();r&&delete Vt[r]}return Ze[e]&&(Ze[e].fnList.forEach(r=>{r(n)}),delete Ze[e]),n.data}).catch(n=>{throw delete Ze[e],n})}let Bn;async function ji(e,t,n){const r=Object.assign({params:{},headers:{},data:{},refresh:!0},n);console.groupCollapsed("%c api\u8BF7\u6C42\u53D1\u8D77","color: #ffffff;background-color: #8f4b2e;padding: 5px",t),console.log("\u8BF7\u6C42\u65B9\u5F0F\uFF1A",e),console.log("\u8BF7\u6C42\u94FE\u63A5\uFF1A",t),console.log("\u8BF7\u6C42\u53C2\u6570\uFF1A",r),console.groupEnd();const s=Ki(e,t,r.params,r.data,r.headers);let a;return Ze[s]?(console.log("\u6709\u76F8\u540C\u63A5\u53E3\u53D1\u8D77\uFF0C\u6574\u5408\u6210\u4E00\u4E2A\u63A5\u53E3 ",`[${t}]`),a=new Promise(o=>{Ze[s].fnList.push(o)})):(Ze[s]={fnList:[]},!r.refresh&&Vt[s]?(console.log("\u4ECE\u7F13\u5B58\u83B7\u53D6\u6570\u636E"),a=new Promise(o=>{o(Vt[s])})):a=Gi({...n,url:t,method:e,params:r.params,data:r.data,headers:r.headers}).then(async o=>o)),Ji(s,a)}async function qt(e,t,n){return ji(e,t,n).then(async r=>{if(console.groupCollapsed("%c api\u8BF7\u6C42\u7ED3\u675F","color: #ffffff;background-color: #5c7a29;padding: 5px",t),console.log("\u8BF7\u6C42\u65B9\u5F0F\uFF1A",e),console.log("\u8BF7\u6C42\u94FE\u63A5\uFF1A",t),console.log("\u8BF7\u6C42\u7ED3\u679C\uFF1A",r),console.groupEnd(),Bn&&(r=await Bn(r)),+r.code!=0)throw r;return r.data})}async function Yi(e,t){return qt("get",e,Object.assign({params:{},headers:{},refresh:!0},t))}async function Hi(e,t){return qt("post",e,Object.assign({data:{},headers:{},refresh:!0},t))}function Wi(e){Bn=e}const Vi=["via","system"];function Tn(e){return[...Vi,e].join(":")}function qi(e,t){try{let n;try{n=JSON.stringify(t)}catch{n=t}window.localStorage.setItem(e,n)}catch(n){console.log(n),console.log("\u5B58\u50A8\u6570\u636E\u5931\u8D25",e,t)}}function $i(e,t){let n=window.localStorage.getItem(e);if(n===null)return t;try{n=JSON.parse(n)}catch(r){console.log(r),n=n||t}return n}function Zi(e){window.localStorage.removeItem(e)}function Xi(e,t,n){e=Tn(e);const r={v:t,e:n||-1,t:new Date().getTime()/1e3};localStorage.setItem(e,JSON.stringify(r))}function zi(e,t){e=Tn(e);const n=localStorage.getItem(e);if(n===null)return t;try{let r=JSON.parse(n||"{}");return!r.e||!r.t?(localStorage.removeItem(e),t):r.e===-1?r.v:r.t+r.e<=new Date().getTime()/1e3?(localStorage.removeItem(e),t):r.v}catch{return localStorage.removeItem(e),t}}function el(e){e=Tn(e),localStorage.removeItem(e)}function na(){return{customSave:qi,customLoad:$i,customRemove:Zi,save:Xi,load:zi,remove:el}}function Nn(e){this.message=e}Nn.prototype=new Error,Nn.prototype.name="InvalidCharacterError";var ra=typeof window!="undefined"&&window.atob&&window.atob.bind(window)||function(e){var t=String(e).replace(/=+$/,"");if(t.length%4==1)throw new Nn("'atob' failed: The string to be decoded is not correctly encoded.");for(var n,r,s=0,a=0,o="";r=t.charAt(a++);~r&&(n=s%4?64*n+r:r,s++%4)?o+=String.fromCharCode(255&n>>(-2*s&6)):0)r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(r);return o};function tl(e){var t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw"Illegal base64url string!"}try{return function(n){return decodeURIComponent(ra(n).replace(/(.)/g,function(r,s){var a=s.charCodeAt(0).toString(16).toUpperCase();return a.length<2&&(a="0"+a),"%"+a}))}(t)}catch{return ra(t)}}function $t(e){this.message=e}function nl(e,t){if(typeof e!="string")throw new $t("Invalid token specified");var n=(t=t||{}).header===!0?0:1;try{return JSON.parse(tl(e.split(".")[n]))}catch(r){throw new $t("Invalid token specified: "+r.message)}}$t.prototype=new Error,$t.prototype.name="InvalidTokenError";var aa={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(gn,function(){var n=1e3,r=6e4,s=36e5,a="millisecond",o="second",i="minute",c="hour",f="day",l="week",m="month",d="quarter",_="year",L="date",D="Invalid Date",S=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,g=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,u={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(x){var O=["th","st","nd","rd"],R=x%100;return"["+x+(O[(R-20)%10]||O[R]||O[0])+"]"}},p=function(x,O,R){var Y=String(x);return!Y||Y.length>=O?x:""+Array(O+1-Y.length).join(R)+x},A={s:p,z:function(x){var O=-x.utcOffset(),R=Math.abs(O),Y=Math.floor(R/60),N=R%60;return(O<=0?"+":"-")+p(Y,2,"0")+":"+p(N,2,"0")},m:function x(O,R){if(O.date()<R.date())return-x(R,O);var Y=12*(R.year()-O.year())+(R.month()-O.month()),N=O.clone().add(Y,m),Z=R-N<0,X=O.clone().add(Y+(Z?-1:1),m);return+(-(Y+(R-N)/(Z?N-X:X-N))||0)},a:function(x){return x<0?Math.ceil(x)||0:Math.floor(x)},p:function(x){return{M:m,y:_,w:l,d:f,D:L,h:c,m:i,s:o,ms:a,Q:d}[x]||String(x||"").toLowerCase().replace(/s$/,"")},u:function(x){return x===void 0}},y="en",E={};E[y]=u;var M="$isDayjsObject",k=function(x){return x instanceof ee||!(!x||!x[M])},j=function x(O,R,Y){var N;if(!O)return y;if(typeof O=="string"){var Z=O.toLowerCase();E[Z]&&(N=Z),R&&(E[Z]=R,N=Z);var X=O.split("-");if(!N&&X.length>1)return x(X[0])}else{var se=O.name;E[se]=O,N=se}return!Y&&N&&(y=N),N||!Y&&y},q=function(x,O){if(k(x))return x.clone();var R=typeof O=="object"?O:{};return R.date=x,R.args=arguments,new ee(R)},W=A;W.l=j,W.i=k,W.w=function(x,O){return q(x,{locale:O.$L,utc:O.$u,x:O.$x,$offset:O.$offset})};var ee=function(){function x(R){this.$L=j(R.locale,null,!0),this.parse(R),this.$x=this.$x||R.x||{},this[M]=!0}var O=x.prototype;return O.parse=function(R){this.$d=function(Y){var N=Y.date,Z=Y.utc;if(N===null)return new Date(NaN);if(W.u(N))return new Date;if(N instanceof Date)return new Date(N);if(typeof N=="string"&&!/Z$/i.test(N)){var X=N.match(S);if(X){var se=X[2]-1||0,J=(X[7]||"0").substring(0,3);return Z?new Date(Date.UTC(X[1],se,X[3]||1,X[4]||0,X[5]||0,X[6]||0,J)):new Date(X[1],se,X[3]||1,X[4]||0,X[5]||0,X[6]||0,J)}}return new Date(N)}(R),this.init()},O.init=function(){var R=this.$d;this.$y=R.getFullYear(),this.$M=R.getMonth(),this.$D=R.getDate(),this.$W=R.getDay(),this.$H=R.getHours(),this.$m=R.getMinutes(),this.$s=R.getSeconds(),this.$ms=R.getMilliseconds()},O.$utils=function(){return W},O.isValid=function(){return this.$d.toString()!==D},O.isSame=function(R,Y){var N=q(R);return this.startOf(Y)<=N&&N<=this.endOf(Y)},O.isAfter=function(R,Y){return q(R)<this.startOf(Y)},O.isBefore=function(R,Y){return this.endOf(Y)<q(R)},O.$g=function(R,Y,N){return W.u(R)?this[Y]:this.set(N,R)},O.unix=function(){return Math.floor(this.valueOf()/1e3)},O.valueOf=function(){return this.$d.getTime()},O.startOf=function(R,Y){var N=this,Z=!!W.u(Y)||Y,X=W.p(R),se=function(B,U){var re=W.w(N.$u?Date.UTC(N.$y,U,B):new Date(N.$y,U,B),N);return Z?re:re.endOf(f)},J=function(B,U){return W.w(N.toDate()[B].apply(N.toDate("s"),(Z?[0,0,0,0]:[23,59,59,999]).slice(U)),N)},Q=this.$W,F=this.$M,H=this.$D,$="set"+(this.$u?"UTC":"");switch(X){case _:return Z?se(1,0):se(31,11);case m:return Z?se(1,F):se(0,F+1);case l:var w=this.$locale().weekStart||0,T=(Q<w?Q+7:Q)-w;return se(Z?H-T:H+(6-T),F);case f:case L:return J($+"Hours",0);case c:return J($+"Minutes",1);case i:return J($+"Seconds",2);case o:return J($+"Milliseconds",3);default:return this.clone()}},O.endOf=function(R){return this.startOf(R,!1)},O.$set=function(R,Y){var N,Z=W.p(R),X="set"+(this.$u?"UTC":""),se=(N={},N[f]=X+"Date",N[L]=X+"Date",N[m]=X+"Month",N[_]=X+"FullYear",N[c]=X+"Hours",N[i]=X+"Minutes",N[o]=X+"Seconds",N[a]=X+"Milliseconds",N)[Z],J=Z===f?this.$D+(Y-this.$W):Y;if(Z===m||Z===_){var Q=this.clone().set(L,1);Q.$d[se](J),Q.init(),this.$d=Q.set(L,Math.min(this.$D,Q.daysInMonth())).$d}else se&&this.$d[se](J);return this.init(),this},O.set=function(R,Y){return this.clone().$set(R,Y)},O.get=function(R){return this[W.p(R)]()},O.add=function(R,Y){var N,Z=this;R=Number(R);var X=W.p(Y),se=function(F){var H=q(Z);return W.w(H.date(H.date()+Math.round(F*R)),Z)};if(X===m)return this.set(m,this.$M+R);if(X===_)return this.set(_,this.$y+R);if(X===f)return se(1);if(X===l)return se(7);var J=(N={},N[i]=r,N[c]=s,N[o]=n,N)[X]||1,Q=this.$d.getTime()+R*J;return W.w(Q,this)},O.subtract=function(R,Y){return this.add(-1*R,Y)},O.format=function(R){var Y=this,N=this.$locale();if(!this.isValid())return N.invalidDate||D;var Z=R||"YYYY-MM-DDTHH:mm:ssZ",X=W.z(this),se=this.$H,J=this.$m,Q=this.$M,F=N.weekdays,H=N.months,$=N.meridiem,w=function(U,re,oe,C){return U&&(U[re]||U(Y,Z))||oe[re].slice(0,C)},T=function(U){return W.s(se%12||12,U,"0")},B=$||function(U,re,oe){var C=U<12?"AM":"PM";return oe?C.toLowerCase():C};return Z.replace(g,function(U,re){return re||function(oe){switch(oe){case"YY":return String(Y.$y).slice(-2);case"YYYY":return W.s(Y.$y,4,"0");case"M":return Q+1;case"MM":return W.s(Q+1,2,"0");case"MMM":return w(N.monthsShort,Q,H,3);case"MMMM":return w(H,Q);case"D":return Y.$D;case"DD":return W.s(Y.$D,2,"0");case"d":return String(Y.$W);case"dd":return w(N.weekdaysMin,Y.$W,F,2);case"ddd":return w(N.weekdaysShort,Y.$W,F,3);case"dddd":return F[Y.$W];case"H":return String(se);case"HH":return W.s(se,2,"0");case"h":return T(1);case"hh":return T(2);case"a":return B(se,J,!0);case"A":return B(se,J,!1);case"m":return String(J);case"mm":return W.s(J,2,"0");case"s":return String(Y.$s);case"ss":return W.s(Y.$s,2,"0");case"SSS":return W.s(Y.$ms,3,"0");case"Z":return X}return null}(U)||X.replace(":","")})},O.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},O.diff=function(R,Y,N){var Z,X=this,se=W.p(Y),J=q(R),Q=(J.utcOffset()-this.utcOffset())*r,F=this-J,H=function(){return W.m(X,J)};switch(se){case _:Z=H()/12;break;case m:Z=H();break;case d:Z=H()/3;break;case l:Z=(F-Q)/6048e5;break;case f:Z=(F-Q)/864e5;break;case c:Z=F/s;break;case i:Z=F/r;break;case o:Z=F/n;break;default:Z=F}return N?Z:W.a(Z)},O.daysInMonth=function(){return this.endOf(m).$D},O.$locale=function(){return E[this.$L]},O.locale=function(R,Y){if(!R)return this.$L;var N=this.clone(),Z=j(R,Y,!0);return Z&&(N.$L=Z),N},O.clone=function(){return W.w(this.$d,this)},O.toDate=function(){return new Date(this.valueOf())},O.toJSON=function(){return this.isValid()?this.toISOString():null},O.toISOString=function(){return this.$d.toISOString()},O.toString=function(){return this.$d.toUTCString()},x}(),ne=ee.prototype;return q.prototype=ne,[["$ms",a],["$s",o],["$m",i],["$H",c],["$W",f],["$M",m],["$y",_],["$D",L]].forEach(function(x){ne[x[1]]=function(O){return this.$g(O,x[0],x[1])}}),q.extend=function(x,O){return x.$i||(x(O,ee,q),x.$i=!0),q},q.locale=j,q.isDayjs=k,q.unix=function(x){return q(1e3*x)},q.en=E[y],q.Ls=E,q.p={},q})})(aa);var Zt=aa.exports;Zt.prototype.isSameOrBefore=function(e){return this.isSame(e)||this.isBefore(e)},Zt.prototype.isSameOrAfter=function(e){return this.isSame(e)||this.isAfter(e)};let On="";function rl(e){On=e.format()}function al(e,t,n){return!e&&On&&(e=On),Zt(e,t,n)}function sa(e,t,n){return{setCurrentDay:rl,dayjs:al,current:Zt(e,t,n)}}function oa(e){try{const t=nl(e),{dayjs:n,current:r}=sa(),s=n(t.exp*1e3).diff(r);return{data:t.data,leftTime:s}}catch{}return{leftTime:0}}/*!
  * shared v9.5.0
  * (c) 2023 kazuya kawaguchi
  * Released under the MIT License.
  */const Qn=typeof window!="undefined",Qe=(e,t=!1)=>t?Symbol.for(e):Symbol(e),sl=(e,t,n)=>ol({l:e,k:t,s:n}),ol=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),ge=e=>typeof e=="number"&&isFinite(e),il=e=>ua(e)==="[object Date]",Je=e=>ua(e)==="[object RegExp]",Xt=e=>te(e)&&Object.keys(e).length===0,Ie=Object.assign;let ia;const Fe=()=>ia||(ia=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:typeof global!="undefined"?global:{});function la(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const ll=Object.prototype.hasOwnProperty;function Fn(e,t){return ll.call(e,t)}const fe=Array.isArray,he=e=>typeof e=="function",K=e=>typeof e=="string",ae=e=>typeof e=="boolean",le=e=>e!==null&&typeof e=="object",ca=Object.prototype.toString,ua=e=>ca.call(e),te=e=>{if(!le(e))return!1;const t=Object.getPrototypeOf(e);return t===null||t.constructor===Object},cl=e=>e==null?"":fe(e)||te(e)&&e.toString===ca?JSON.stringify(e,null,2):String(e);function ul(e,t=""){return e.reduce((n,r,s)=>s===0?n+r:n+t+r,"")}function Pn(e){let t=e;return()=>++t}function fl(e,t){typeof console!="undefined"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}/*!
  * message-compiler v9.5.0
  * (c) 2023 kazuya kawaguchi
  * Released under the MIT License.
  */function dl(e,t,n){return{line:e,column:t,offset:n}}function Un(e,t,n){const r={start:e,end:t};return n!=null&&(r.source=n),r}const hl=/\{([0-9a-zA-Z]+)\}/g;function Al(e,...t){return t.length===1&&gl(t[0])&&(t=t[0]),(!t||!t.hasOwnProperty)&&(t={}),e.replace(hl,(n,r)=>t.hasOwnProperty(r)?t[r]:"")}const fa=Object.assign,da=e=>typeof e=="string",gl=e=>e!==null&&typeof e=="object";function ha(e,t=""){return e.reduce((n,r,s)=>s===0?n+r:n+t+r,"")}const z={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},ml={[z.EXPECTED_TOKEN]:"Expected token: '{0}'",[z.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[z.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[z.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[z.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[z.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[z.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[z.EMPTY_PLACEHOLDER]:"Empty placeholder",[z.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[z.INVALID_LINKED_FORMAT]:"Invalid linked format",[z.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[z.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[z.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[z.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[z.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[z.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function it(e,t,n={}){const{domain:r,messages:s,args:a}=n,o=Al((s||ml)[e]||"",...a||[]),i=new SyntaxError(String(o));return i.code=e,t&&(i.location=t),i.domain=r,i}function pl(e){throw e}const Pe=" ",El="\r",_e=`
`,Cl=String.fromCharCode(8232),Il=String.fromCharCode(8233);function yl(e){const t=e;let n=0,r=1,s=1,a=0;const o=M=>t[M]===El&&t[M+1]===_e,i=M=>t[M]===_e,c=M=>t[M]===Il,f=M=>t[M]===Cl,l=M=>o(M)||i(M)||c(M)||f(M),m=()=>n,d=()=>r,_=()=>s,L=()=>a,D=M=>o(M)||c(M)||f(M)?_e:t[M],S=()=>D(n),g=()=>D(n+a);function u(){return a=0,l(n)&&(r++,s=0),o(n)&&n++,n++,s++,t[n]}function p(){return o(n+a)&&a++,a++,t[n+a]}function A(){n=0,r=1,s=1,a=0}function y(M=0){a=M}function E(){const M=n+a;for(;M!==n;)u();a=0}return{index:m,line:d,column:_,peekOffset:L,charAt:D,currentChar:S,currentPeek:g,next:u,peek:p,reset:A,resetPeek:y,skipToPeek:E}}const je=void 0,_l=".",Aa="'",wl="tokenizer";function bl(e,t={}){const n=t.location!==!1,r=yl(e),s=()=>r.index(),a=()=>dl(r.line(),r.column(),r.index()),o=a(),i=s(),c={currentType:14,offset:i,startLoc:o,endLoc:o,lastType:14,lastOffset:i,lastStartLoc:o,lastEndLoc:o,braceNest:0,inLinked:!1,text:""},f=()=>c,{onError:l}=t;function m(C,h,b,...G){const V=f();if(h.column+=b,h.offset+=b,l){const ie=n?Un(V.startLoc,h):null,Ne=it(C,ie,{domain:wl,args:G});l(Ne)}}function d(C,h,b){C.endLoc=a(),C.currentType=h;const G={type:h};return n&&(G.loc=Un(C.startLoc,C.endLoc)),b!=null&&(G.value=b),G}const _=C=>d(C,14);function L(C,h){return C.currentChar()===h?(C.next(),h):(m(z.EXPECTED_TOKEN,a(),0,h),"")}function D(C){let h="";for(;C.currentPeek()===Pe||C.currentPeek()===_e;)h+=C.currentPeek(),C.peek();return h}function S(C){const h=D(C);return C.skipToPeek(),h}function g(C){if(C===je)return!1;const h=C.charCodeAt(0);return h>=97&&h<=122||h>=65&&h<=90||h===95}function u(C){if(C===je)return!1;const h=C.charCodeAt(0);return h>=48&&h<=57}function p(C,h){const{currentType:b}=h;if(b!==2)return!1;D(C);const G=g(C.currentPeek());return C.resetPeek(),G}function A(C,h){const{currentType:b}=h;if(b!==2)return!1;D(C);const G=C.currentPeek()==="-"?C.peek():C.currentPeek(),V=u(G);return C.resetPeek(),V}function y(C,h){const{currentType:b}=h;if(b!==2)return!1;D(C);const G=C.currentPeek()===Aa;return C.resetPeek(),G}function E(C,h){const{currentType:b}=h;if(b!==8)return!1;D(C);const G=C.currentPeek()===".";return C.resetPeek(),G}function M(C,h){const{currentType:b}=h;if(b!==9)return!1;D(C);const G=g(C.currentPeek());return C.resetPeek(),G}function k(C,h){const{currentType:b}=h;if(!(b===8||b===12))return!1;D(C);const G=C.currentPeek()===":";return C.resetPeek(),G}function j(C,h){const{currentType:b}=h;if(b!==10)return!1;const G=()=>{const ie=C.currentPeek();return ie==="{"?g(C.peek()):ie==="@"||ie==="%"||ie==="|"||ie===":"||ie==="."||ie===Pe||!ie?!1:ie===_e?(C.peek(),G()):g(ie)},V=G();return C.resetPeek(),V}function q(C){D(C);const h=C.currentPeek()==="|";return C.resetPeek(),h}function W(C){const h=D(C),b=C.currentPeek()==="%"&&C.peek()==="{";return C.resetPeek(),{isModulo:b,hasSpace:h.length>0}}function ee(C,h=!0){const b=(V=!1,ie="",Ne=!1)=>{const Ge=C.currentPeek();return Ge==="{"?ie==="%"?!1:V:Ge==="@"||!Ge?ie==="%"?!0:V:Ge==="%"?(C.peek(),b(V,"%",!0)):Ge==="|"?ie==="%"||Ne?!0:!(ie===Pe||ie===_e):Ge===Pe?(C.peek(),b(!0,Pe,Ne)):Ge===_e?(C.peek(),b(!0,_e,Ne)):!0},G=b();return h&&C.resetPeek(),G}function ne(C,h){const b=C.currentChar();return b===je?je:h(b)?(C.next(),b):null}function x(C){return ne(C,b=>{const G=b.charCodeAt(0);return G>=97&&G<=122||G>=65&&G<=90||G>=48&&G<=57||G===95||G===36})}function O(C){return ne(C,b=>{const G=b.charCodeAt(0);return G>=48&&G<=57})}function R(C){return ne(C,b=>{const G=b.charCodeAt(0);return G>=48&&G<=57||G>=65&&G<=70||G>=97&&G<=102})}function Y(C){let h="",b="";for(;h=O(C);)b+=h;return b}function N(C){S(C);const h=C.currentChar();return h!=="%"&&m(z.EXPECTED_TOKEN,a(),0,h),C.next(),"%"}function Z(C){let h="";for(;;){const b=C.currentChar();if(b==="{"||b==="}"||b==="@"||b==="|"||!b)break;if(b==="%")if(ee(C))h+=b,C.next();else break;else if(b===Pe||b===_e)if(ee(C))h+=b,C.next();else{if(q(C))break;h+=b,C.next()}else h+=b,C.next()}return h}function X(C){S(C);let h="",b="";for(;h=x(C);)b+=h;return C.currentChar()===je&&m(z.UNTERMINATED_CLOSING_BRACE,a(),0),b}function se(C){S(C);let h="";return C.currentChar()==="-"?(C.next(),h+=`-${Y(C)}`):h+=Y(C),C.currentChar()===je&&m(z.UNTERMINATED_CLOSING_BRACE,a(),0),h}function J(C){S(C),L(C,"'");let h="",b="";const G=ie=>ie!==Aa&&ie!==_e;for(;h=ne(C,G);)h==="\\"?b+=Q(C):b+=h;const V=C.currentChar();return V===_e||V===je?(m(z.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,a(),0),V===_e&&(C.next(),L(C,"'")),b):(L(C,"'"),b)}function Q(C){const h=C.currentChar();switch(h){case"\\":case"'":return C.next(),`\\${h}`;case"u":return F(C,h,4);case"U":return F(C,h,6);default:return m(z.UNKNOWN_ESCAPE_SEQUENCE,a(),0,h),""}}function F(C,h,b){L(C,h);let G="";for(let V=0;V<b;V++){const ie=R(C);if(!ie){m(z.INVALID_UNICODE_ESCAPE_SEQUENCE,a(),0,`\\${h}${G}${C.currentChar()}`);break}G+=ie}return`\\${h}${G}`}function H(C){S(C);let h="",b="";const G=V=>V!=="{"&&V!=="}"&&V!==Pe&&V!==_e;for(;h=ne(C,G);)b+=h;return b}function $(C){let h="",b="";for(;h=x(C);)b+=h;return b}function w(C){const h=(b=!1,G)=>{const V=C.currentChar();return V==="{"||V==="%"||V==="@"||V==="|"||V==="("||V===")"||!V||V===Pe?G:V===_e||V===_l?(G+=V,C.next(),h(b,G)):(G+=V,C.next(),h(!0,G))};return h(!1,"")}function T(C){S(C);const h=L(C,"|");return S(C),h}function B(C,h){let b=null;switch(C.currentChar()){case"{":return h.braceNest>=1&&m(z.NOT_ALLOW_NEST_PLACEHOLDER,a(),0),C.next(),b=d(h,2,"{"),S(C),h.braceNest++,b;case"}":return h.braceNest>0&&h.currentType===2&&m(z.EMPTY_PLACEHOLDER,a(),0),C.next(),b=d(h,3,"}"),h.braceNest--,h.braceNest>0&&S(C),h.inLinked&&h.braceNest===0&&(h.inLinked=!1),b;case"@":return h.braceNest>0&&m(z.UNTERMINATED_CLOSING_BRACE,a(),0),b=U(C,h)||_(h),h.braceNest=0,b;default:let V=!0,ie=!0,Ne=!0;if(q(C))return h.braceNest>0&&m(z.UNTERMINATED_CLOSING_BRACE,a(),0),b=d(h,1,T(C)),h.braceNest=0,h.inLinked=!1,b;if(h.braceNest>0&&(h.currentType===5||h.currentType===6||h.currentType===7))return m(z.UNTERMINATED_CLOSING_BRACE,a(),0),h.braceNest=0,re(C,h);if(V=p(C,h))return b=d(h,5,X(C)),S(C),b;if(ie=A(C,h))return b=d(h,6,se(C)),S(C),b;if(Ne=y(C,h))return b=d(h,7,J(C)),S(C),b;if(!V&&!ie&&!Ne)return b=d(h,13,H(C)),m(z.INVALID_TOKEN_IN_PLACEHOLDER,a(),0,b.value),S(C),b;break}return b}function U(C,h){const{currentType:b}=h;let G=null;const V=C.currentChar();switch((b===8||b===9||b===12||b===10)&&(V===_e||V===Pe)&&m(z.INVALID_LINKED_FORMAT,a(),0),V){case"@":return C.next(),G=d(h,8,"@"),h.inLinked=!0,G;case".":return S(C),C.next(),d(h,9,".");case":":return S(C),C.next(),d(h,10,":");default:return q(C)?(G=d(h,1,T(C)),h.braceNest=0,h.inLinked=!1,G):E(C,h)||k(C,h)?(S(C),U(C,h)):M(C,h)?(S(C),d(h,12,$(C))):j(C,h)?(S(C),V==="{"?B(C,h)||G:d(h,11,w(C))):(b===8&&m(z.INVALID_LINKED_FORMAT,a(),0),h.braceNest=0,h.inLinked=!1,re(C,h))}}function re(C,h){let b={type:14};if(h.braceNest>0)return B(C,h)||_(h);if(h.inLinked)return U(C,h)||_(h);switch(C.currentChar()){case"{":return B(C,h)||_(h);case"}":return m(z.UNBALANCED_CLOSING_BRACE,a(),0),C.next(),d(h,3,"}");case"@":return U(C,h)||_(h);default:if(q(C))return b=d(h,1,T(C)),h.braceNest=0,h.inLinked=!1,b;const{isModulo:V,hasSpace:ie}=W(C);if(V)return ie?d(h,0,Z(C)):d(h,4,N(C));if(ee(C))return d(h,0,Z(C));break}return b}function oe(){const{currentType:C,offset:h,startLoc:b,endLoc:G}=c;return c.lastType=C,c.lastOffset=h,c.lastStartLoc=b,c.lastEndLoc=G,c.offset=s(),c.startLoc=a(),r.currentChar()===je?d(c,14):re(r,c)}return{nextToken:oe,currentOffset:s,currentPosition:a,context:f}}const Sl="parser",Ll=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Ml(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const r=parseInt(t||n,16);return r<=55295||r>=57344?String.fromCodePoint(r):"\uFFFD"}}}function Dl(e={}){const t=e.location!==!1,{onError:n}=e;function r(g,u,p,A,...y){const E=g.currentPosition();if(E.offset+=A,E.column+=A,n){const M=t?Un(p,E):null,k=it(u,M,{domain:Sl,args:y});n(k)}}function s(g,u,p){const A={type:g};return t&&(A.start=u,A.end=u,A.loc={start:p,end:p}),A}function a(g,u,p,A){A&&(g.type=A),t&&(g.end=u,g.loc&&(g.loc.end=p))}function o(g,u){const p=g.context(),A=s(3,p.offset,p.startLoc);return A.value=u,a(A,g.currentOffset(),g.currentPosition()),A}function i(g,u){const p=g.context(),{lastOffset:A,lastStartLoc:y}=p,E=s(5,A,y);return E.index=parseInt(u,10),g.nextToken(),a(E,g.currentOffset(),g.currentPosition()),E}function c(g,u){const p=g.context(),{lastOffset:A,lastStartLoc:y}=p,E=s(4,A,y);return E.key=u,g.nextToken(),a(E,g.currentOffset(),g.currentPosition()),E}function f(g,u){const p=g.context(),{lastOffset:A,lastStartLoc:y}=p,E=s(9,A,y);return E.value=u.replace(Ll,Ml),g.nextToken(),a(E,g.currentOffset(),g.currentPosition()),E}function l(g){const u=g.nextToken(),p=g.context(),{lastOffset:A,lastStartLoc:y}=p,E=s(8,A,y);return u.type!==12?(r(g,z.UNEXPECTED_EMPTY_LINKED_MODIFIER,p.lastStartLoc,0),E.value="",a(E,A,y),{nextConsumeToken:u,node:E}):(u.value==null&&r(g,z.UNEXPECTED_LEXICAL_ANALYSIS,p.lastStartLoc,0,Te(u)),E.value=u.value||"",a(E,g.currentOffset(),g.currentPosition()),{node:E})}function m(g,u){const p=g.context(),A=s(7,p.offset,p.startLoc);return A.value=u,a(A,g.currentOffset(),g.currentPosition()),A}function d(g){const u=g.context(),p=s(6,u.offset,u.startLoc);let A=g.nextToken();if(A.type===9){const y=l(g);p.modifier=y.node,A=y.nextConsumeToken||g.nextToken()}switch(A.type!==10&&r(g,z.UNEXPECTED_LEXICAL_ANALYSIS,u.lastStartLoc,0,Te(A)),A=g.nextToken(),A.type===2&&(A=g.nextToken()),A.type){case 11:A.value==null&&r(g,z.UNEXPECTED_LEXICAL_ANALYSIS,u.lastStartLoc,0,Te(A)),p.key=m(g,A.value||"");break;case 5:A.value==null&&r(g,z.UNEXPECTED_LEXICAL_ANALYSIS,u.lastStartLoc,0,Te(A)),p.key=c(g,A.value||"");break;case 6:A.value==null&&r(g,z.UNEXPECTED_LEXICAL_ANALYSIS,u.lastStartLoc,0,Te(A)),p.key=i(g,A.value||"");break;case 7:A.value==null&&r(g,z.UNEXPECTED_LEXICAL_ANALYSIS,u.lastStartLoc,0,Te(A)),p.key=f(g,A.value||"");break;default:r(g,z.UNEXPECTED_EMPTY_LINKED_KEY,u.lastStartLoc,0);const y=g.context(),E=s(7,y.offset,y.startLoc);return E.value="",a(E,y.offset,y.startLoc),p.key=E,a(p,y.offset,y.startLoc),{nextConsumeToken:A,node:p}}return a(p,g.currentOffset(),g.currentPosition()),{node:p}}function _(g){const u=g.context(),p=u.currentType===1?g.currentOffset():u.offset,A=u.currentType===1?u.endLoc:u.startLoc,y=s(2,p,A);y.items=[];let E=null;do{const j=E||g.nextToken();switch(E=null,j.type){case 0:j.value==null&&r(g,z.UNEXPECTED_LEXICAL_ANALYSIS,u.lastStartLoc,0,Te(j)),y.items.push(o(g,j.value||""));break;case 6:j.value==null&&r(g,z.UNEXPECTED_LEXICAL_ANALYSIS,u.lastStartLoc,0,Te(j)),y.items.push(i(g,j.value||""));break;case 5:j.value==null&&r(g,z.UNEXPECTED_LEXICAL_ANALYSIS,u.lastStartLoc,0,Te(j)),y.items.push(c(g,j.value||""));break;case 7:j.value==null&&r(g,z.UNEXPECTED_LEXICAL_ANALYSIS,u.lastStartLoc,0,Te(j)),y.items.push(f(g,j.value||""));break;case 8:const q=d(g);y.items.push(q.node),E=q.nextConsumeToken||null;break}}while(u.currentType!==14&&u.currentType!==1);const M=u.currentType===1?u.lastOffset:g.currentOffset(),k=u.currentType===1?u.lastEndLoc:g.currentPosition();return a(y,M,k),y}function L(g,u,p,A){const y=g.context();let E=A.items.length===0;const M=s(1,u,p);M.cases=[],M.cases.push(A);do{const k=_(g);E||(E=k.items.length===0),M.cases.push(k)}while(y.currentType!==14);return E&&r(g,z.MUST_HAVE_MESSAGES_IN_PLURAL,p,0),a(M,g.currentOffset(),g.currentPosition()),M}function D(g){const u=g.context(),{offset:p,startLoc:A}=u,y=_(g);return u.currentType===14?y:L(g,p,A,y)}function S(g){const u=bl(g,fa({},e)),p=u.context(),A=s(0,p.offset,p.startLoc);return t&&A.loc&&(A.loc.source=g),A.body=D(u),e.onCacheKey&&(A.cacheKey=e.onCacheKey(g)),p.currentType!==14&&r(u,z.UNEXPECTED_LEXICAL_ANALYSIS,p.lastStartLoc,0,g[p.offset]||""),a(A,u.currentOffset(),u.currentPosition()),A}return{parse:S}}function Te(e){if(e.type===14)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"\u2026":t}function kl(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:a=>(n.helpers.add(a),a)}}function ga(e,t){for(let n=0;n<e.length;n++)xn(e[n],t)}function xn(e,t){switch(e.type){case 1:ga(e.cases,t),t.helper("plural");break;case 2:ga(e.items,t);break;case 6:xn(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function vl(e,t={}){const n=kl(e);n.helper("normalize"),e.body&&xn(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function Rl(e){const t=e.body;return t.type===2?ma(t):t.cases.forEach(n=>ma(n)),e}function ma(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(!(r.type===3||r.type===9)||r.value==null)break;t.push(r.value)}if(t.length===e.items.length){e.static=ha(t);for(let n=0;n<e.items.length;n++){const r=e.items[n];(r.type===3||r.type===9)&&delete r.value}}}}const Bl="minifier";function lt(e){switch(e.t=e.type,e.type){case 0:const t=e;lt(t.body),t.b=t.body,delete t.body;break;case 1:const n=e,r=n.cases;for(let l=0;l<r.length;l++)lt(r[l]);n.c=r,delete n.cases;break;case 2:const s=e,a=s.items;for(let l=0;l<a.length;l++)lt(a[l]);s.i=a,delete s.items,s.static&&(s.s=s.static,delete s.static);break;case 3:case 9:case 8:case 7:const o=e;o.value&&(o.v=o.value,delete o.value);break;case 6:const i=e;lt(i.key),i.k=i.key,delete i.key,i.modifier&&(lt(i.modifier),i.m=i.modifier,delete i.modifier);break;case 5:const c=e;c.i=c.index,delete c.index;break;case 4:const f=e;f.k=f.key,delete f.key;break;default:throw it(z.UNHANDLED_MINIFIER_NODE_TYPE,null,{domain:Bl,args:[e.type]})}delete e.type}const Tl="parser";function Nl(e,t){const{sourceMap:n,filename:r,breakLineCode:s,needIndent:a}=t,o=t.location!==!1,i={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:s,needIndent:a,indentLevel:0};o&&e.loc&&(i.source=e.loc.source);const c=()=>i;function f(S,g){i.code+=S}function l(S,g=!0){const u=g?s:"";f(a?u+"  ".repeat(S):u)}function m(S=!0){const g=++i.indentLevel;S&&l(g)}function d(S=!0){const g=--i.indentLevel;S&&l(g)}function _(){l(i.indentLevel)}return{context:c,push:f,indent:m,deindent:d,newline:_,helper:S=>`_${S}`,needIndent:()=>i.needIndent}}function Ol(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),ct(e,t.key),t.modifier?(e.push(", "),ct(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function Ql(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const s=t.items.length;for(let a=0;a<s&&(ct(e,t.items[a]),a!==s-1);a++)e.push(", ");e.deindent(r()),e.push("])")}function Fl(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const s=t.cases.length;for(let a=0;a<s&&(ct(e,t.cases[a]),a!==s-1);a++)e.push(", ");e.deindent(r()),e.push("])")}}function Pl(e,t){t.body?ct(e,t.body):e.push("null")}function ct(e,t){const{helper:n}=e;switch(t.type){case 0:Pl(e,t);break;case 1:Fl(e,t);break;case 2:Ql(e,t);break;case 6:Ol(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break;default:throw it(z.UNHANDLED_CODEGEN_NODE_TYPE,null,{domain:Tl,args:[t.type]})}}const Ul=(e,t={})=>{const n=da(t.mode)?t.mode:"normal",r=da(t.filename)?t.filename:"message.intl",s=!!t.sourceMap,a=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,o=t.needIndent?t.needIndent:n!=="arrow",i=e.helpers||[],c=Nl(e,{mode:n,filename:r,sourceMap:s,breakLineCode:a,needIndent:o});c.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(o),i.length>0&&(c.push(`const { ${ha(i.map(m=>`${m}: _${m}`),", ")} } = ctx`),c.newline()),c.push("return "),ct(c,e),c.deindent(o),c.push("}"),delete e.helpers;const{code:f,map:l}=c.context();return{ast:e,code:f,map:l?l.toJSON():void 0}};function xl(e,t={}){const n=fa({},t),r=!!n.jit,s=!!n.minify,a=n.optimize==null?!0:n.optimize,i=Dl(n).parse(e);return r?(a&&Rl(i),s&&lt(i),{ast:i,code:""}):(vl(i,n),Ul(i,n))}/*!
  * core-base v9.5.0
  * (c) 2023 kazuya kawaguchi
  * Released under the MIT License.
  */function Gl(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(Fe().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(Fe().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Fe().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}const Ye=[];Ye[0]={w:[0],i:[3,0],["["]:[4],o:[7]},Ye[1]={w:[1],["."]:[2],["["]:[4],o:[7]},Ye[2]={w:[2],i:[3,0],[0]:[3,0]},Ye[3]={i:[3,0],[0]:[3,0],w:[1,1],["."]:[2,1],["["]:[4,1],o:[7,1]},Ye[4]={["'"]:[5,0],['"']:[6,0],["["]:[4,2],["]"]:[1,3],o:8,l:[4,0]},Ye[5]={["'"]:[4,0],o:8,l:[5,0]},Ye[6]={['"']:[4,0],o:8,l:[6,0]};const Kl=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Jl(e){return Kl.test(e)}function jl(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function Yl(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function Hl(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:Jl(t)?jl(t):"*"+t}function Wl(e){const t=[];let n=-1,r=0,s=0,a,o,i,c,f,l,m;const d=[];d[0]=()=>{o===void 0?o=i:o+=i},d[1]=()=>{o!==void 0&&(t.push(o),o=void 0)},d[2]=()=>{d[0](),s++},d[3]=()=>{if(s>0)s--,r=4,d[0]();else{if(s=0,o===void 0||(o=Hl(o),o===!1))return!1;d[1]()}};function _(){const L=e[n+1];if(r===5&&L==="'"||r===6&&L==='"')return n++,i="\\"+L,d[0](),!0}for(;r!==null;)if(n++,a=e[n],!(a==="\\"&&_())){if(c=Yl(a),m=Ye[r],f=m[c]||m.l||8,f===8||(r=f[0],f[1]!==void 0&&(l=d[f[1]],l&&(i=a,l()===!1))))return;if(r===7)return t}}const pa=new Map;function Vl(e,t){return le(e)?e[t]:null}function ql(e,t){if(!le(e))return null;let n=pa.get(t);if(n||(n=Wl(t),n&&pa.set(t,n)),!n)return null;const r=n.length;let s=e,a=0;for(;a<r;){const o=s[n[a]];if(o===void 0)return null;s=o,a++}return s}const $l=e=>e,Zl=e=>"",Xl="text",zl=e=>e.length===0?"":ul(e),ec=cl;function Ea(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function tc(e){const t=ge(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(ge(e.named.count)||ge(e.named.n))?ge(e.named.count)?e.named.count:ge(e.named.n)?e.named.n:t:t}function nc(e,t){t.count||(t.count=e),t.n||(t.n=e)}function rc(e={}){const t=e.locale,n=tc(e),r=le(e.pluralRules)&&K(t)&&he(e.pluralRules[t])?e.pluralRules[t]:Ea,s=le(e.pluralRules)&&K(t)&&he(e.pluralRules[t])?Ea:void 0,a=g=>g[r(n,g.length,s)],o=e.list||[],i=g=>o[g],c=e.named||{};ge(e.pluralIndex)&&nc(n,c);const f=g=>c[g];function l(g){const u=he(e.messages)?e.messages(g):le(e.messages)?e.messages[g]:!1;return u||(e.parent?e.parent.message(g):Zl)}const m=g=>e.modifiers?e.modifiers[g]:$l,d=te(e.processor)&&he(e.processor.normalize)?e.processor.normalize:zl,_=te(e.processor)&&he(e.processor.interpolate)?e.processor.interpolate:ec,L=te(e.processor)&&K(e.processor.type)?e.processor.type:Xl,S={list:i,named:f,plural:a,linked:(g,...u)=>{const[p,A]=u;let y="text",E="";u.length===1?le(p)?(E=p.modifier||E,y=p.type||y):K(p)&&(E=p||E):u.length===2&&(K(p)&&(E=p||E),K(A)&&(y=A||y));const M=l(g)(S),k=y==="vnode"&&fe(M)&&E?M[0]:M;return E?m(E)(k,y):k},message:l,type:L,interpolate:_,normalize:d,values:Ie({},o,c)};return S}let Et=null;function ac(e){Et=e}function sc(e,t,n){Et&&Et.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const oc=ic("function:translate");function ic(e){return t=>Et&&Et.emit(e,t)}const lc={NOT_FOUND_KEY:1,FALLBACK_TO_TRANSLATE:2,CANNOT_FORMAT_NUMBER:3,FALLBACK_TO_NUMBER_FORMAT:4,CANNOT_FORMAT_DATE:5,FALLBACK_TO_DATE_FORMAT:6,EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:7,__EXTEND_POINT__:8};function Gn(e,t){return t.locale!=null?Ca(t.locale):Ca(e.locale)}let Kn;function Ca(e){return K(e)?e:Kn!=null&&e.resolvedOnce?Kn:Kn=e()}function cc(e,t,n){return[...new Set([n,...fe(t)?t:le(t)?Object.keys(t):K(t)?[t]:[n]])]}function Ia(e,t,n){const r=K(n)?n:ut,s=e;s.__localeChainCache||(s.__localeChainCache=new Map);let a=s.__localeChainCache.get(r);if(!a){a=[];let o=[n];for(;fe(o);)o=ya(a,o,t);const i=fe(t)||!te(t)?t:t.default?t.default:null;o=K(i)?[i]:i,fe(o)&&ya(a,o,!1),s.__localeChainCache.set(r,a)}return a}function ya(e,t,n){let r=!0;for(let s=0;s<t.length&&ae(r);s++){const a=t[s];K(a)&&(r=uc(e,t[s],n))}return r}function uc(e,t,n){let r;const s=t.split("-");do{const a=s.join("-");r=fc(e,a,n),s.splice(-1,1)}while(s.length&&r===!0);return r}function fc(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r=t[t.length-1]!=="!";const s=t.replace(/!/g,"");e.push(s),(fe(n)||te(n))&&n[s]&&(r=n[s])}return r}const dc="9.5.0",zt=-1,ut="en-US",_a="",wa=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function hc(){return{upper:(e,t)=>t==="text"&&K(e)?e.toUpperCase():t==="vnode"&&le(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&K(e)?e.toLowerCase():t==="vnode"&&le(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&K(e)?wa(e):t==="vnode"&&le(e)&&"__v_isVNode"in e?wa(e.children):e}}let ba;function Sa(e){ba=e}let La;function Ac(e){La=e}let Ma;function gc(e){Ma=e}let Da=null;const ka=e=>{Da=e},mc=()=>Da;let va=null;const Ra=e=>{va=e},pc=()=>va;let Ba=0;function Ec(e={}){const t=he(e.onWarn)?e.onWarn:fl,n=K(e.version)?e.version:dc,r=K(e.locale)||he(e.locale)?e.locale:ut,s=he(r)?ut:r,a=fe(e.fallbackLocale)||te(e.fallbackLocale)||K(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:s,o=te(e.messages)?e.messages:{[s]:{}},i=te(e.datetimeFormats)?e.datetimeFormats:{[s]:{}},c=te(e.numberFormats)?e.numberFormats:{[s]:{}},f=Ie({},e.modifiers||{},hc()),l=e.pluralRules||{},m=he(e.missing)?e.missing:null,d=ae(e.missingWarn)||Je(e.missingWarn)?e.missingWarn:!0,_=ae(e.fallbackWarn)||Je(e.fallbackWarn)?e.fallbackWarn:!0,L=!!e.fallbackFormat,D=!!e.unresolving,S=he(e.postTranslation)?e.postTranslation:null,g=te(e.processor)?e.processor:null,u=ae(e.warnHtmlMessage)?e.warnHtmlMessage:!0,p=!!e.escapeParameter,A=he(e.messageCompiler)?e.messageCompiler:ba,y=he(e.messageResolver)?e.messageResolver:La||Vl,E=he(e.localeFallbacker)?e.localeFallbacker:Ma||cc,M=le(e.fallbackContext)?e.fallbackContext:void 0,k=e,j=le(k.__datetimeFormatters)?k.__datetimeFormatters:new Map,q=le(k.__numberFormatters)?k.__numberFormatters:new Map,W=le(k.__meta)?k.__meta:{};Ba++;const ee={version:n,cid:Ba,locale:r,fallbackLocale:a,messages:o,modifiers:f,pluralRules:l,missing:m,missingWarn:d,fallbackWarn:_,fallbackFormat:L,unresolving:D,postTranslation:S,processor:g,warnHtmlMessage:u,escapeParameter:p,messageCompiler:A,messageResolver:y,localeFallbacker:E,fallbackContext:M,onWarn:t,__meta:W};return ee.datetimeFormats=i,ee.numberFormats=c,ee.__datetimeFormatters=j,ee.__numberFormatters=q,__INTLIFY_PROD_DEVTOOLS__&&sc(ee,n,W),ee}function Jn(e,t,n,r,s){const{missing:a,onWarn:o}=e;if(a!==null){const i=a(e,n,t,s);return K(i)?i:t}else return t}function Ct(e,t,n){const r=e;r.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function jn(e){return n=>Cc(n,e)}function Cc(e,t){const n=t.b||t.body;if((n.t||n.type)===1){const r=n,s=r.c||r.cases;return e.plural(s.reduce((a,o)=>[...a,Ta(e,o)],[]))}else return Ta(e,n)}function Ta(e,t){const n=t.s||t.static;if(n)return e.type==="text"?n:e.normalize([n]);{const r=(t.i||t.items).reduce((s,a)=>[...s,Yn(e,a)],[]);return e.normalize(r)}}function Yn(e,t){const n=t.t||t.type;switch(n){case 3:const r=t;return r.v||r.value;case 9:const s=t;return s.v||s.value;case 4:const a=t;return e.interpolate(e.named(a.k||a.key));case 5:const o=t;return e.interpolate(e.list(o.i!=null?o.i:o.index));case 6:const i=t,c=i.m||i.modifier;return e.linked(Yn(e,i.k||i.key),c?Yn(e,c):void 0,e.type);case 7:const f=t;return f.v||f.value;case 8:const l=t;return l.v||l.value;default:throw new Error(`unhandled node type on format message part: ${n}`)}}const Na=z.__EXTEND_POINT__,en=Pn(Na),He={INVALID_ARGUMENT:Na,INVALID_DATE_ARGUMENT:en(),INVALID_ISO_DATE_ARGUMENT:en(),NOT_SUPPORT_NON_STRING_MESSAGE:en(),__EXTEND_POINT__:en()};function Xe(e){return it(e,null,void 0)}const Oa=e=>e;let ft=Object.create(null);const It=e=>le(e)&&(e.t===0||e.type===0)&&("b"in e||"body"in e);function Qa(e,t={}){let n=!1;const r=t.onError||pl;return t.onError=s=>{n=!0,r(s)},{...xl(e,t),detectError:n}}const Ic=(e,t)=>{if(!K(e))throw Xe(He.NOT_SUPPORT_NON_STRING_MESSAGE);{ae(t.warnHtmlMessage)&&t.warnHtmlMessage;const r=(t.onCacheKey||Oa)(e),s=ft[r];if(s)return s;const{code:a,detectError:o}=Qa(e,t),i=new Function(`return ${a}`)();return o?i:ft[r]=i}};function yc(e,t){if(__INTLIFY_JIT_COMPILATION__&&!__INTLIFY_DROP_MESSAGE_COMPILER__&&K(e)){ae(t.warnHtmlMessage)&&t.warnHtmlMessage;const r=(t.onCacheKey||Oa)(e),s=ft[r];if(s)return s;const{ast:a,detectError:o}=Qa(e,{...t,location:!1,jit:!0}),i=jn(a);return o?i:ft[r]=i}else{const n=e.cacheKey;if(n){const r=ft[n];return r||(ft[n]=jn(e))}else return jn(e)}}const Fa=()=>"",ve=e=>he(e);function Pa(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:s,messageCompiler:a,fallbackLocale:o,messages:i}=e,[c,f]=Hn(...t),l=ae(f.missingWarn)?f.missingWarn:e.missingWarn,m=ae(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn,d=ae(f.escapeParameter)?f.escapeParameter:e.escapeParameter,_=!!f.resolvedMessage,L=K(f.default)||ae(f.default)?ae(f.default)?a?c:()=>c:f.default:n?a?c:()=>c:"",D=n||L!=="",S=Gn(e,f);d&&_c(f);let[g,u,p]=_?[c,S,i[S]||{}]:Ua(e,c,S,o,m,l),A=g,y=c;if(!_&&!(K(A)||It(A)||ve(A))&&D&&(A=L,y=A),!_&&(!(K(A)||It(A)||ve(A))||!K(u)))return s?zt:c;let E=!1;const M=()=>{E=!0},k=ve(A)?A:xa(e,c,u,A,y,M);if(E)return A;const j=Sc(e,u,p,f),q=rc(j),W=wc(e,k,q),ee=r?r(W,c):W;if(__INTLIFY_PROD_DEVTOOLS__){const ne={timestamp:Date.now(),key:K(c)?c:ve(A)?A.key:"",locale:u||(ve(A)?A.locale:""),format:K(A)?A:ve(A)?A.source:"",message:ee};ne.meta=Ie({},e.__meta,mc()||{}),oc(ne)}return ee}function _c(e){fe(e.list)?e.list=e.list.map(t=>K(t)?la(t):t):le(e.named)&&Object.keys(e.named).forEach(t=>{K(e.named[t])&&(e.named[t]=la(e.named[t]))})}function Ua(e,t,n,r,s,a){const{messages:o,onWarn:i,messageResolver:c,localeFallbacker:f}=e,l=f(e,r,n);let m={},d,_=null;const L="translate";for(let D=0;D<l.length&&(d=l[D],m=o[d]||{},(_=c(m,t))===null&&(_=m[t]),!(K(_)||It(_)||ve(_)));D++){const S=Jn(e,t,d,a,L);S!==t&&(_=S)}return[_,d,m]}function xa(e,t,n,r,s,a){const{messageCompiler:o,warnHtmlMessage:i}=e;if(ve(r)){const f=r;return f.locale=f.locale||n,f.key=f.key||t,f}if(o==null){const f=()=>r;return f.locale=n,f.key=t,f}const c=o(r,bc(e,n,s,r,i,a));return c.locale=n,c.key=t,c.source=r,c}function wc(e,t,n){return t(n)}function Hn(...e){const[t,n,r]=e,s={};if(!K(t)&&!ge(t)&&!ve(t)&&!It(t))throw Xe(He.INVALID_ARGUMENT);const a=ge(t)?String(t):(ve(t),t);return ge(n)?s.plural=n:K(n)?s.default=n:te(n)&&!Xt(n)?s.named=n:fe(n)&&(s.list=n),ge(r)?s.plural=r:K(r)?s.default=r:te(r)&&Ie(s,r),[a,s]}function bc(e,t,n,r,s,a){return{locale:t,key:n,warnHtmlMessage:s,onError:o=>{throw a&&a(o),o},onCacheKey:o=>sl(t,n,o)}}function Sc(e,t,n,r){const{modifiers:s,pluralRules:a,messageResolver:o,fallbackLocale:i,fallbackWarn:c,missingWarn:f,fallbackContext:l}=e,d={locale:t,modifiers:s,pluralRules:a,messages:_=>{let L=o(n,_);if(L==null&&l){const[,,D]=Ua(l,_,t,i,c,f);L=o(D,_)}if(K(L)||It(L)){let D=!1;const g=xa(e,_,t,L,_,()=>{D=!0});return D?Fa:g}else return ve(L)?L:Fa}};return e.processor&&(d.processor=e.processor),r.list&&(d.list=r.list),r.named&&(d.named=r.named),ge(r.plural)&&(d.pluralIndex=r.plural),d}function Ga(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:s,onWarn:a,localeFallbacker:o}=e,{__datetimeFormatters:i}=e,[c,f,l,m]=Wn(...t),d=ae(l.missingWarn)?l.missingWarn:e.missingWarn;ae(l.fallbackWarn)?l.fallbackWarn:e.fallbackWarn;const _=!!l.part,L=Gn(e,l),D=o(e,s,L);if(!K(c)||c==="")return new Intl.DateTimeFormat(L,m).format(f);let S={},g,u=null;const p="datetime format";for(let E=0;E<D.length&&(g=D[E],S=n[g]||{},u=S[c],!te(u));E++)Jn(e,c,g,d,p);if(!te(u)||!K(g))return r?zt:c;let A=`${g}__${c}`;Xt(m)||(A=`${A}__${JSON.stringify(m)}`);let y=i.get(A);return y||(y=new Intl.DateTimeFormat(g,Ie({},u,m)),i.set(A,y)),_?y.formatToParts(f):y.format(f)}const Ka=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Wn(...e){const[t,n,r,s]=e,a={};let o={},i;if(K(t)){const c=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!c)throw Xe(He.INVALID_ISO_DATE_ARGUMENT);const f=c[3]?c[3].trim().startsWith("T")?`${c[1].trim()}${c[3].trim()}`:`${c[1].trim()}T${c[3].trim()}`:c[1].trim();i=new Date(f);try{i.toISOString()}catch{throw Xe(He.INVALID_ISO_DATE_ARGUMENT)}}else if(il(t)){if(isNaN(t.getTime()))throw Xe(He.INVALID_DATE_ARGUMENT);i=t}else if(ge(t))i=t;else throw Xe(He.INVALID_ARGUMENT);return K(n)?a.key=n:te(n)&&Object.keys(n).forEach(c=>{Ka.includes(c)?o[c]=n[c]:a[c]=n[c]}),K(r)?a.locale=r:te(r)&&(o=r),te(s)&&(o=s),[a.key||"",i,a,o]}function Ja(e,t,n){const r=e;for(const s in n){const a=`${t}__${s}`;!r.__datetimeFormatters.has(a)||r.__datetimeFormatters.delete(a)}}function ja(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:s,onWarn:a,localeFallbacker:o}=e,{__numberFormatters:i}=e,[c,f,l,m]=Vn(...t),d=ae(l.missingWarn)?l.missingWarn:e.missingWarn;ae(l.fallbackWarn)?l.fallbackWarn:e.fallbackWarn;const _=!!l.part,L=Gn(e,l),D=o(e,s,L);if(!K(c)||c==="")return new Intl.NumberFormat(L,m).format(f);let S={},g,u=null;const p="number format";for(let E=0;E<D.length&&(g=D[E],S=n[g]||{},u=S[c],!te(u));E++)Jn(e,c,g,d,p);if(!te(u)||!K(g))return r?zt:c;let A=`${g}__${c}`;Xt(m)||(A=`${A}__${JSON.stringify(m)}`);let y=i.get(A);return y||(y=new Intl.NumberFormat(g,Ie({},u,m)),i.set(A,y)),_?y.formatToParts(f):y.format(f)}const Ya=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Vn(...e){const[t,n,r,s]=e,a={};let o={};if(!ge(t))throw Xe(He.INVALID_ARGUMENT);const i=t;return K(n)?a.key=n:te(n)&&Object.keys(n).forEach(c=>{Ya.includes(c)?o[c]=n[c]:a[c]=n[c]}),K(r)?a.locale=r:te(r)&&(o=r),te(s)&&(o=s),[a.key||"",i,a,o]}function Ha(e,t,n){const r=e;for(const s in n){const a=`${t}__${s}`;!r.__numberFormatters.has(a)||r.__numberFormatters.delete(a)}}Gl();/*!
  * vue-i18n v9.5.0
  * (c) 2023 kazuya kawaguchi
  * Released under the MIT License.
  */const Lc="9.5.0";function Mc(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(Fe().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(Fe().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(Fe().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Fe().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(Fe().__INTLIFY_PROD_DEVTOOLS__=!1)}const Wa=lc.__EXTEND_POINT__,We=Pn(Wa);We(),We(),We(),We(),We(),We(),We(),We();const Va=He.__EXTEND_POINT__,Se=Pn(Va),Ae={UNEXPECTED_RETURN_TYPE:Va,INVALID_ARGUMENT:Se(),MUST_BE_CALL_SETUP_TOP:Se(),NOT_INSTALLED:Se(),NOT_AVAILABLE_IN_LEGACY_MODE:Se(),REQUIRED_VALUE:Se(),INVALID_VALUE:Se(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:Se(),NOT_INSTALLED_WITH_PROVIDE:Se(),UNEXPECTED_ERROR:Se(),NOT_COMPATIBLE_LEGACY_VUE_I18N:Se(),BRIDGE_SUPPORT_VUE_2_ONLY:Se(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:Se(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:Se(),__EXTEND_POINT__:Se()};function me(e,...t){return it(e,null,void 0)}const qn=Qe("__translateVNode"),$n=Qe("__datetimeParts"),Zn=Qe("__numberParts"),qa=Qe("__setPluralRules");Qe("__intlifyMeta");const $a=Qe("__injectWithOption"),Xn=Qe("__dispose");function zn(e){if(!le(e))return e;for(const t in e)if(!!Fn(e,t))if(!t.includes("."))le(e[t])&&zn(e[t]);else{const n=t.split("."),r=n.length-1;let s=e,a=!1;for(let o=0;o<r;o++){if(n[o]in s||(s[n[o]]={}),!le(s[n[o]])){a=!0;break}s=s[n[o]]}a||(s[n[r]]=e[t],delete e[t]),le(s[n[r]])&&zn(s[n[r]])}return e}function tn(e,t){const{messages:n,__i18n:r,messageResolver:s,flatJson:a}=t,o=te(n)?n:fe(r)?{}:{[e]:{}};if(fe(r)&&r.forEach(i=>{if("locale"in i&&"resource"in i){const{locale:c,resource:f}=i;c?(o[c]=o[c]||{},yt(f,o[c])):yt(f,o)}else K(i)&&yt(JSON.parse(i),o)}),s==null&&a)for(const i in o)Fn(o,i)&&zn(o[i]);return o}const nn=e=>!le(e)||fe(e);function yt(e,t){if(nn(e)||nn(t))throw me(Ae.INVALID_VALUE);for(const n in e)Fn(e,n)&&(nn(e[n])||nn(t[n])?t[n]=e[n]:yt(e[n],t[n]))}function Za(e){return e.type}function Xa(e,t,n){let r=le(t.messages)?t.messages:{};"__i18nGlobal"in n&&(r=tn(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const s=Object.keys(r);s.length&&s.forEach(a=>{e.mergeLocaleMessage(a,r[a])});{if(le(t.datetimeFormats)){const a=Object.keys(t.datetimeFormats);a.length&&a.forEach(o=>{e.mergeDateTimeFormat(o,t.datetimeFormats[o])})}if(le(t.numberFormats)){const a=Object.keys(t.numberFormats);a.length&&a.forEach(o=>{e.mergeNumberFormat(o,t.numberFormats[o])})}}}function za(e){return I.createVNode(I.Text,null,e,0)}const es="__INTLIFY_META__";let ts=0;function ns(e){return(t,n,r,s)=>e(n,r,I.getCurrentInstance()||void 0,s)}const Dc=()=>{const e=I.getCurrentInstance();let t=null;return e&&(t=Za(e)[es])?{[es]:t}:null};function er(e={},t){const{__root:n,__injectWithOption:r}=e,s=n===void 0;let a=ae(e.inheritLocale)?e.inheritLocale:!0;const o=I.ref(n&&a?n.locale.value:K(e.locale)?e.locale:ut),i=I.ref(n&&a?n.fallbackLocale.value:K(e.fallbackLocale)||fe(e.fallbackLocale)||te(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:o.value),c=I.ref(tn(o.value,e)),f=I.ref(te(e.datetimeFormats)?e.datetimeFormats:{[o.value]:{}}),l=I.ref(te(e.numberFormats)?e.numberFormats:{[o.value]:{}});let m=n?n.missingWarn:ae(e.missingWarn)||Je(e.missingWarn)?e.missingWarn:!0,d=n?n.fallbackWarn:ae(e.fallbackWarn)||Je(e.fallbackWarn)?e.fallbackWarn:!0,_=n?n.fallbackRoot:ae(e.fallbackRoot)?e.fallbackRoot:!0,L=!!e.fallbackFormat,D=he(e.missing)?e.missing:null,S=he(e.missing)?ns(e.missing):null,g=he(e.postTranslation)?e.postTranslation:null,u=n?n.warnHtmlMessage:ae(e.warnHtmlMessage)?e.warnHtmlMessage:!0,p=!!e.escapeParameter;const A=n?n.modifiers:te(e.modifiers)?e.modifiers:{};let y=e.pluralRules||n&&n.pluralRules,E;E=(()=>{s&&Ra(null);const v={version:Lc,locale:o.value,fallbackLocale:i.value,messages:c.value,modifiers:A,pluralRules:y,missing:S===null?void 0:S,missingWarn:m,fallbackWarn:d,fallbackFormat:L,unresolving:!0,postTranslation:g===null?void 0:g,warnHtmlMessage:u,escapeParameter:p,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};v.datetimeFormats=f.value,v.numberFormats=l.value,v.__datetimeFormatters=te(E)?E.__datetimeFormatters:void 0,v.__numberFormatters=te(E)?E.__numberFormatters:void 0;const P=Ec(v);return s&&Ra(P),P})(),Ct(E,o.value,i.value);function k(){return[o.value,i.value,c.value,f.value,l.value]}const j=I.computed({get:()=>o.value,set:v=>{o.value=v,E.locale=o.value}}),q=I.computed({get:()=>i.value,set:v=>{i.value=v,E.fallbackLocale=i.value,Ct(E,o.value,v)}}),W=I.computed(()=>c.value),ee=I.computed(()=>f.value),ne=I.computed(()=>l.value);function x(){return he(g)?g:null}function O(v){g=v,E.postTranslation=v}function R(){return D}function Y(v){v!==null&&(S=ns(v)),D=v,E.missing=S}const N=(v,P,Oe,we,Lr,hn)=>{k();let Nt;try{__INTLIFY_PROD_DEVTOOLS__&&ka(Dc()),s||(E.fallbackContext=n?pc():void 0),Nt=v(E)}finally{__INTLIFY_PROD_DEVTOOLS__&&ka(null),s||(E.fallbackContext=void 0)}if(ge(Nt)&&Nt===zt){const[Nd,Vd]=P();return n&&_?we(n):Lr(Nd)}else{if(hn(Nt))return Nt;throw me(Ae.UNEXPECTED_RETURN_TYPE)}};function Z(...v){return N(P=>Reflect.apply(Pa,null,[P,...v]),()=>Hn(...v),"translate",P=>Reflect.apply(P.t,P,[...v]),P=>P,P=>K(P))}function X(...v){const[P,Oe,we]=v;if(we&&!le(we))throw me(Ae.INVALID_ARGUMENT);return Z(P,Oe,Ie({resolvedMessage:!0},we||{}))}function se(...v){return N(P=>Reflect.apply(Ga,null,[P,...v]),()=>Wn(...v),"datetime format",P=>Reflect.apply(P.d,P,[...v]),()=>_a,P=>K(P))}function J(...v){return N(P=>Reflect.apply(ja,null,[P,...v]),()=>Vn(...v),"number format",P=>Reflect.apply(P.n,P,[...v]),()=>_a,P=>K(P))}function Q(v){return v.map(P=>K(P)||ge(P)||ae(P)?za(String(P)):P)}const H={normalize:Q,interpolate:v=>v,type:"vnode"};function $(...v){return N(P=>{let Oe;const we=P;try{we.processor=H,Oe=Reflect.apply(Pa,null,[we,...v])}finally{we.processor=null}return Oe},()=>Hn(...v),"translate",P=>P[qn](...v),P=>[za(P)],P=>fe(P))}function w(...v){return N(P=>Reflect.apply(ja,null,[P,...v]),()=>Vn(...v),"number format",P=>P[Zn](...v),()=>[],P=>K(P)||fe(P))}function T(...v){return N(P=>Reflect.apply(Ga,null,[P,...v]),()=>Wn(...v),"datetime format",P=>P[$n](...v),()=>[],P=>K(P)||fe(P))}function B(v){y=v,E.pluralRules=y}function U(v,P){if(!v)return!1;const Oe=K(P)?P:o.value,we=C(Oe);return E.messageResolver(we,v)!==null}function re(v){let P=null;const Oe=Ia(E,i.value,o.value);for(let we=0;we<Oe.length;we++){const Lr=c.value[Oe[we]]||{},hn=E.messageResolver(Lr,v);if(hn!=null){P=hn;break}}return P}function oe(v){const P=re(v);return P!=null?P:n?n.tm(v)||{}:{}}function C(v){return c.value[v]||{}}function h(v,P){c.value[v]=P,E.messages=c.value}function b(v,P){c.value[v]=c.value[v]||{},yt(P,c.value[v]),E.messages=c.value}function G(v){return f.value[v]||{}}function V(v,P){f.value[v]=P,E.datetimeFormats=f.value,Ja(E,v,P)}function ie(v,P){f.value[v]=Ie(f.value[v]||{},P),E.datetimeFormats=f.value,Ja(E,v,P)}function Ne(v){return l.value[v]||{}}function Ge(v,P){l.value[v]=P,E.numberFormats=l.value,Ha(E,v,P)}function Td(v,P){l.value[v]=Ie(l.value[v]||{},P),E.numberFormats=l.value,Ha(E,v,P)}ts++,n&&Qn&&(I.watch(n.locale,v=>{a&&(o.value=v,E.locale=v,Ct(E,o.value,i.value))}),I.watch(n.fallbackLocale,v=>{a&&(i.value=v,E.fallbackLocale=v,Ct(E,o.value,i.value))}));const Ee={id:ts,locale:j,fallbackLocale:q,get inheritLocale(){return a},set inheritLocale(v){a=v,v&&n&&(o.value=n.locale.value,i.value=n.fallbackLocale.value,Ct(E,o.value,i.value))},get availableLocales(){return Object.keys(c.value).sort()},messages:W,get modifiers(){return A},get pluralRules(){return y||{}},get isGlobal(){return s},get missingWarn(){return m},set missingWarn(v){m=v,E.missingWarn=m},get fallbackWarn(){return d},set fallbackWarn(v){d=v,E.fallbackWarn=d},get fallbackRoot(){return _},set fallbackRoot(v){_=v},get fallbackFormat(){return L},set fallbackFormat(v){L=v,E.fallbackFormat=L},get warnHtmlMessage(){return u},set warnHtmlMessage(v){u=v,E.warnHtmlMessage=v},get escapeParameter(){return p},set escapeParameter(v){p=v,E.escapeParameter=v},t:Z,getLocaleMessage:C,setLocaleMessage:h,mergeLocaleMessage:b,getPostTranslationHandler:x,setPostTranslationHandler:O,getMissingHandler:R,setMissingHandler:Y,[qa]:B};return Ee.datetimeFormats=ee,Ee.numberFormats=ne,Ee.rt=X,Ee.te=U,Ee.tm=oe,Ee.d=se,Ee.n=J,Ee.getDateTimeFormat=G,Ee.setDateTimeFormat=V,Ee.mergeDateTimeFormat=ie,Ee.getNumberFormat=Ne,Ee.setNumberFormat=Ge,Ee.mergeNumberFormat=Td,Ee[$a]=r,Ee[qn]=$,Ee[$n]=T,Ee[Zn]=w,Ee}function kc(e){const t=K(e.locale)?e.locale:ut,n=K(e.fallbackLocale)||fe(e.fallbackLocale)||te(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,r=he(e.missing)?e.missing:void 0,s=ae(e.silentTranslationWarn)||Je(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,a=ae(e.silentFallbackWarn)||Je(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,o=ae(e.fallbackRoot)?e.fallbackRoot:!0,i=!!e.formatFallbackMessages,c=te(e.modifiers)?e.modifiers:{},f=e.pluralizationRules,l=he(e.postTranslation)?e.postTranslation:void 0,m=K(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,d=!!e.escapeParameterHtml,_=ae(e.sync)?e.sync:!0;let L=e.messages;if(te(e.sharedMessages)){const y=e.sharedMessages;L=Object.keys(y).reduce((M,k)=>{const j=M[k]||(M[k]={});return Ie(j,y[k]),M},L||{})}const{__i18n:D,__root:S,__injectWithOption:g}=e,u=e.datetimeFormats,p=e.numberFormats,A=e.flatJson;return{locale:t,fallbackLocale:n,messages:L,flatJson:A,datetimeFormats:u,numberFormats:p,missing:r,missingWarn:s,fallbackWarn:a,fallbackRoot:o,fallbackFormat:i,modifiers:c,pluralRules:f,postTranslation:l,warnHtmlMessage:m,escapeParameter:d,messageResolver:e.messageResolver,inheritLocale:_,__i18n:D,__root:S,__injectWithOption:g}}function tr(e={},t){{const n=er(kc(e)),{__extender:r}=e,s={id:n.id,get locale(){return n.locale.value},set locale(a){n.locale.value=a},get fallbackLocale(){return n.fallbackLocale.value},set fallbackLocale(a){n.fallbackLocale.value=a},get messages(){return n.messages.value},get datetimeFormats(){return n.datetimeFormats.value},get numberFormats(){return n.numberFormats.value},get availableLocales(){return n.availableLocales},get formatter(){return{interpolate(){return[]}}},set formatter(a){},get missing(){return n.getMissingHandler()},set missing(a){n.setMissingHandler(a)},get silentTranslationWarn(){return ae(n.missingWarn)?!n.missingWarn:n.missingWarn},set silentTranslationWarn(a){n.missingWarn=ae(a)?!a:a},get silentFallbackWarn(){return ae(n.fallbackWarn)?!n.fallbackWarn:n.fallbackWarn},set silentFallbackWarn(a){n.fallbackWarn=ae(a)?!a:a},get modifiers(){return n.modifiers},get formatFallbackMessages(){return n.fallbackFormat},set formatFallbackMessages(a){n.fallbackFormat=a},get postTranslation(){return n.getPostTranslationHandler()},set postTranslation(a){n.setPostTranslationHandler(a)},get sync(){return n.inheritLocale},set sync(a){n.inheritLocale=a},get warnHtmlInMessage(){return n.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(a){n.warnHtmlMessage=a!=="off"},get escapeParameterHtml(){return n.escapeParameter},set escapeParameterHtml(a){n.escapeParameter=a},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(a){},get pluralizationRules(){return n.pluralRules||{}},__composer:n,t(...a){const[o,i,c]=a,f={};let l=null,m=null;if(!K(o))throw me(Ae.INVALID_ARGUMENT);const d=o;return K(i)?f.locale=i:fe(i)?l=i:te(i)&&(m=i),fe(c)?l=c:te(c)&&(m=c),Reflect.apply(n.t,n,[d,l||m||{},f])},rt(...a){return Reflect.apply(n.rt,n,[...a])},tc(...a){const[o,i,c]=a,f={plural:1};let l=null,m=null;if(!K(o))throw me(Ae.INVALID_ARGUMENT);const d=o;return K(i)?f.locale=i:ge(i)?f.plural=i:fe(i)?l=i:te(i)&&(m=i),K(c)?f.locale=c:fe(c)?l=c:te(c)&&(m=c),Reflect.apply(n.t,n,[d,l||m||{},f])},te(a,o){return n.te(a,o)},tm(a){return n.tm(a)},getLocaleMessage(a){return n.getLocaleMessage(a)},setLocaleMessage(a,o){n.setLocaleMessage(a,o)},mergeLocaleMessage(a,o){n.mergeLocaleMessage(a,o)},d(...a){return Reflect.apply(n.d,n,[...a])},getDateTimeFormat(a){return n.getDateTimeFormat(a)},setDateTimeFormat(a,o){n.setDateTimeFormat(a,o)},mergeDateTimeFormat(a,o){n.mergeDateTimeFormat(a,o)},n(...a){return Reflect.apply(n.n,n,[...a])},getNumberFormat(a){return n.getNumberFormat(a)},setNumberFormat(a,o){n.setNumberFormat(a,o)},mergeNumberFormat(a,o){n.mergeNumberFormat(a,o)},getChoiceIndex(a,o){return-1}};return s.__extender=r,s}}const nr={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function vc({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((r,s)=>[...r,...s.type===I.Fragment?s.children:[s]],[]):t.reduce((n,r)=>{const s=e[r];return s&&(n[r]=s()),n},{})}function rs(e){return I.Fragment}const as=I.defineComponent({name:"i18n-t",props:Ie({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>ge(e)||!isNaN(e)}},nr),setup(e,t){const{slots:n,attrs:r}=t,s=e.i18n||rr({useScope:e.scope,__useComponent:!0});return()=>{const a=Object.keys(n).filter(m=>m!=="_"),o={};e.locale&&(o.locale=e.locale),e.plural!==void 0&&(o.plural=K(e.plural)?+e.plural:e.plural);const i=vc(t,a),c=s[qn](e.keypath,i,o),f=Ie({},r),l=K(e.tag)||le(e.tag)?e.tag:rs();return I.h(l,f,c)}}});function Rc(e){return fe(e)&&!K(e[0])}function ss(e,t,n,r){const{slots:s,attrs:a}=t;return()=>{const o={part:!0};let i={};e.locale&&(o.locale=e.locale),K(e.format)?o.key=e.format:le(e.format)&&(K(e.format.key)&&(o.key=e.format.key),i=Object.keys(e.format).reduce((d,_)=>n.includes(_)?Ie({},d,{[_]:e.format[_]}):d,{}));const c=r(e.value,o,i);let f=[o.key];fe(c)?f=c.map((d,_)=>{const L=s[d.type],D=L?L({[d.type]:d.value,index:_,parts:c}):[d.value];return Rc(D)&&(D[0].key=`${d.type}-${_}`),D}):K(c)&&(f=[c]);const l=Ie({},a),m=K(e.tag)||le(e.tag)?e.tag:rs();return I.h(m,l,f)}}const os=I.defineComponent({name:"i18n-n",props:Ie({value:{type:Number,required:!0},format:{type:[String,Object]}},nr),setup(e,t){const n=e.i18n||rr({useScope:"parent",__useComponent:!0});return ss(e,t,Ya,(...r)=>n[Zn](...r))}}),is=I.defineComponent({name:"i18n-d",props:Ie({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},nr),setup(e,t){const n=e.i18n||rr({useScope:"parent",__useComponent:!0});return ss(e,t,Ka,(...r)=>n[$n](...r))}});function Bc(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return r!=null?r.__composer:e.global.__composer}}function Tc(e){const t=o=>{const{instance:i,modifiers:c,value:f}=o;if(!i||!i.$)throw me(Ae.UNEXPECTED_ERROR);const l=Bc(e,i.$),m=ls(f);return[Reflect.apply(l.t,l,[...cs(m)]),l]};return{created:(o,i)=>{const[c,f]=t(i);Qn&&e.global===f&&(o.__i18nWatcher=I.watch(f.locale,()=>{i.instance&&i.instance.$forceUpdate()})),o.__composer=f,o.textContent=c},unmounted:o=>{Qn&&o.__i18nWatcher&&(o.__i18nWatcher(),o.__i18nWatcher=void 0,delete o.__i18nWatcher),o.__composer&&(o.__composer=void 0,delete o.__composer)},beforeUpdate:(o,{value:i})=>{if(o.__composer){const c=o.__composer,f=ls(i);o.textContent=Reflect.apply(c.t,c,[...cs(f)])}},getSSRProps:o=>{const[i]=t(o);return{textContent:i}}}}function ls(e){if(K(e))return{path:e};if(te(e)){if(!("path"in e))throw me(Ae.REQUIRED_VALUE,"path");return e}else throw me(Ae.INVALID_VALUE)}function cs(e){const{path:t,locale:n,args:r,choice:s,plural:a}=e,o={},i=r||{};return K(n)&&(o.locale=n),ge(s)&&(o.plural=s),ge(a)&&(o.plural=a),[t,i,o]}function Nc(e,t,...n){const r=te(n[0])?n[0]:{},s=!!r.useI18nComponentName;(ae(r.globalInstall)?r.globalInstall:!0)&&([s?"i18n":as.name,"I18nT"].forEach(o=>e.component(o,as)),[os.name,"I18nN"].forEach(o=>e.component(o,os)),[is.name,"I18nD"].forEach(o=>e.component(o,is))),e.directive("t",Tc(t))}function Oc(e,t,n){return{beforeCreate(){const r=I.getCurrentInstance();if(!r)throw me(Ae.UNEXPECTED_ERROR);const s=this.$options;if(s.i18n){const a=s.i18n;if(s.__i18n&&(a.__i18n=s.__i18n),a.__root=t,this===this.$root)this.$i18n=us(e,a);else{a.__injectWithOption=!0,a.__extender=n.__vueI18nExtend,this.$i18n=tr(a);const o=this.$i18n;o.__extender&&(o.__disposer=o.__extender(this.$i18n))}}else if(s.__i18n)if(this===this.$root)this.$i18n=us(e,s);else{this.$i18n=tr({__i18n:s.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const a=this.$i18n;a.__extender&&(a.__disposer=a.__extender(this.$i18n))}else this.$i18n=e;s.__i18nGlobal&&Xa(t,s,s),this.$t=(...a)=>this.$i18n.t(...a),this.$rt=(...a)=>this.$i18n.rt(...a),this.$tc=(...a)=>this.$i18n.tc(...a),this.$te=(a,o)=>this.$i18n.te(a,o),this.$d=(...a)=>this.$i18n.d(...a),this.$n=(...a)=>this.$i18n.n(...a),this.$tm=a=>this.$i18n.tm(a),n.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const r=I.getCurrentInstance();if(!r)throw me(Ae.UNEXPECTED_ERROR);const s=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,s.__disposer&&(s.__disposer(),delete s.__disposer,delete s.__extender),n.__deleteInstance(r),delete this.$i18n}}}function us(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[qa](t.pluralizationRules||e.pluralizationRules);const n=tn(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(r=>e.mergeLocaleMessage(r,n[r])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(r=>e.mergeDateTimeFormat(r,t.datetimeFormats[r])),t.numberFormats&&Object.keys(t.numberFormats).forEach(r=>e.mergeNumberFormat(r,t.numberFormats[r])),e}const Qc=Qe("global-vue-i18n");function Fc(e={},t){const n=__VUE_I18N_LEGACY_API__&&ae(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,r=ae(e.globalInjection)?e.globalInjection:!0,s=__VUE_I18N_LEGACY_API__&&n?!!e.allowComposition:!0,a=new Map,[o,i]=Pc(e,n),c=Qe("");function f(d){return a.get(d)||null}function l(d,_){a.set(d,_)}function m(d){a.delete(d)}{const d={get mode(){return __VUE_I18N_LEGACY_API__&&n?"legacy":"composition"},get allowComposition(){return s},async install(_,...L){if(_.__VUE_I18N_SYMBOL__=c,_.provide(_.__VUE_I18N_SYMBOL__,d),te(L[0])){const g=L[0];d.__composerExtend=g.__composerExtend,d.__vueI18nExtend=g.__vueI18nExtend}let D=null;!n&&r&&(D=Wc(_,d.global)),__VUE_I18N_FULL_INSTALL__&&Nc(_,d,...L),__VUE_I18N_LEGACY_API__&&n&&_.mixin(Oc(i,i.__composer,d));const S=_.unmount;_.unmount=()=>{D&&D(),d.dispose(),S()}},get global(){return i},dispose(){o.stop()},__instances:a,__getInstance:f,__setInstance:l,__deleteInstance:m};return d}}function rr(e={}){const t=I.getCurrentInstance();if(t==null)throw me(Ae.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw me(Ae.NOT_INSTALLED);const n=Uc(t),r=Gc(n),s=Za(t),a=xc(e,s);if(__VUE_I18N_LEGACY_API__&&n.mode==="legacy"&&!e.__useComponent){if(!n.allowComposition)throw me(Ae.NOT_AVAILABLE_IN_LEGACY_MODE);return Yc(t,a,r,e)}if(a==="global")return Xa(r,e,s),r;if(a==="parent"){let c=Kc(n,t,e.__useComponent);return c==null&&(c=r),c}const o=n;let i=o.__getInstance(t);if(i==null){const c=Ie({},e);"__i18n"in s&&(c.__i18n=s.__i18n),r&&(c.__root=r),i=er(c),o.__composerExtend&&(i[Xn]=o.__composerExtend(i)),jc(o,t,i),o.__setInstance(t,i)}return i}function Pc(e,t,n){const r=I.effectScope();{const s=__VUE_I18N_LEGACY_API__&&t?r.run(()=>tr(e)):r.run(()=>er(e));if(s==null)throw me(Ae.UNEXPECTED_ERROR);return[r,s]}}function Uc(e){{const t=I.inject(e.isCE?Qc:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw me(e.isCE?Ae.NOT_INSTALLED_WITH_PROVIDE:Ae.UNEXPECTED_ERROR);return t}}function xc(e,t){return Xt(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function Gc(e){return e.mode==="composition"?e.global:e.global.__composer}function Kc(e,t,n=!1){let r=null;const s=t.root;let a=Jc(t,n);for(;a!=null;){const o=e;if(e.mode==="composition")r=o.__getInstance(a);else if(__VUE_I18N_LEGACY_API__){const i=o.__getInstance(a);i!=null&&(r=i.__composer,n&&r&&!r[$a]&&(r=null))}if(r!=null||s===a)break;a=a.parent}return r}function Jc(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function jc(e,t,n){I.onMounted(()=>{},t),I.onUnmounted(()=>{const r=n;e.__deleteInstance(t);const s=r[Xn];s&&(s(),delete r[Xn])},t)}function Yc(e,t,n,r={}){const s=t==="local",a=I.shallowRef(null);if(s&&e.proxy&&!(e.proxy.$options.i18n||e.proxy.$options.__i18n))throw me(Ae.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const o=ae(r.inheritLocale)?r.inheritLocale:!K(r.locale),i=I.ref(!s||o?n.locale.value:K(r.locale)?r.locale:ut),c=I.ref(!s||o?n.fallbackLocale.value:K(r.fallbackLocale)||fe(r.fallbackLocale)||te(r.fallbackLocale)||r.fallbackLocale===!1?r.fallbackLocale:i.value),f=I.ref(tn(i.value,r)),l=I.ref(te(r.datetimeFormats)?r.datetimeFormats:{[i.value]:{}}),m=I.ref(te(r.numberFormats)?r.numberFormats:{[i.value]:{}}),d=s?n.missingWarn:ae(r.missingWarn)||Je(r.missingWarn)?r.missingWarn:!0,_=s?n.fallbackWarn:ae(r.fallbackWarn)||Je(r.fallbackWarn)?r.fallbackWarn:!0,L=s?n.fallbackRoot:ae(r.fallbackRoot)?r.fallbackRoot:!0,D=!!r.fallbackFormat,S=he(r.missing)?r.missing:null,g=he(r.postTranslation)?r.postTranslation:null,u=s?n.warnHtmlMessage:ae(r.warnHtmlMessage)?r.warnHtmlMessage:!0,p=!!r.escapeParameter,A=s?n.modifiers:te(r.modifiers)?r.modifiers:{},y=r.pluralRules||s&&n.pluralRules;function E(){return[i.value,c.value,f.value,l.value,m.value]}const M=I.computed({get:()=>a.value?a.value.locale.value:i.value,set:h=>{a.value&&(a.value.locale.value=h),i.value=h}}),k=I.computed({get:()=>a.value?a.value.fallbackLocale.value:c.value,set:h=>{a.value&&(a.value.fallbackLocale.value=h),c.value=h}}),j=I.computed(()=>a.value?a.value.messages.value:f.value),q=I.computed(()=>l.value),W=I.computed(()=>m.value);function ee(){return a.value?a.value.getPostTranslationHandler():g}function ne(h){a.value&&a.value.setPostTranslationHandler(h)}function x(){return a.value?a.value.getMissingHandler():S}function O(h){a.value&&a.value.setMissingHandler(h)}function R(h){return E(),h()}function Y(...h){return a.value?R(()=>Reflect.apply(a.value.t,null,[...h])):R(()=>"")}function N(...h){return a.value?Reflect.apply(a.value.rt,null,[...h]):""}function Z(...h){return a.value?R(()=>Reflect.apply(a.value.d,null,[...h])):R(()=>"")}function X(...h){return a.value?R(()=>Reflect.apply(a.value.n,null,[...h])):R(()=>"")}function se(h){return a.value?a.value.tm(h):{}}function J(h,b){return a.value?a.value.te(h,b):!1}function Q(h){return a.value?a.value.getLocaleMessage(h):{}}function F(h,b){a.value&&(a.value.setLocaleMessage(h,b),f.value[h]=b)}function H(h,b){a.value&&a.value.mergeLocaleMessage(h,b)}function $(h){return a.value?a.value.getDateTimeFormat(h):{}}function w(h,b){a.value&&(a.value.setDateTimeFormat(h,b),l.value[h]=b)}function T(h,b){a.value&&a.value.mergeDateTimeFormat(h,b)}function B(h){return a.value?a.value.getNumberFormat(h):{}}function U(h,b){a.value&&(a.value.setNumberFormat(h,b),m.value[h]=b)}function re(h,b){a.value&&a.value.mergeNumberFormat(h,b)}const oe={get id(){return a.value?a.value.id:-1},locale:M,fallbackLocale:k,messages:j,datetimeFormats:q,numberFormats:W,get inheritLocale(){return a.value?a.value.inheritLocale:o},set inheritLocale(h){a.value&&(a.value.inheritLocale=h)},get availableLocales(){return a.value?a.value.availableLocales:Object.keys(f.value)},get modifiers(){return a.value?a.value.modifiers:A},get pluralRules(){return a.value?a.value.pluralRules:y},get isGlobal(){return a.value?a.value.isGlobal:!1},get missingWarn(){return a.value?a.value.missingWarn:d},set missingWarn(h){a.value&&(a.value.missingWarn=h)},get fallbackWarn(){return a.value?a.value.fallbackWarn:_},set fallbackWarn(h){a.value&&(a.value.missingWarn=h)},get fallbackRoot(){return a.value?a.value.fallbackRoot:L},set fallbackRoot(h){a.value&&(a.value.fallbackRoot=h)},get fallbackFormat(){return a.value?a.value.fallbackFormat:D},set fallbackFormat(h){a.value&&(a.value.fallbackFormat=h)},get warnHtmlMessage(){return a.value?a.value.warnHtmlMessage:u},set warnHtmlMessage(h){a.value&&(a.value.warnHtmlMessage=h)},get escapeParameter(){return a.value?a.value.escapeParameter:p},set escapeParameter(h){a.value&&(a.value.escapeParameter=h)},t:Y,getPostTranslationHandler:ee,setPostTranslationHandler:ne,getMissingHandler:x,setMissingHandler:O,rt:N,d:Z,n:X,tm:se,te:J,getLocaleMessage:Q,setLocaleMessage:F,mergeLocaleMessage:H,getDateTimeFormat:$,setDateTimeFormat:w,mergeDateTimeFormat:T,getNumberFormat:B,setNumberFormat:U,mergeNumberFormat:re};function C(h){h.locale.value=i.value,h.fallbackLocale.value=c.value,Object.keys(f.value).forEach(b=>{h.mergeLocaleMessage(b,f.value[b])}),Object.keys(l.value).forEach(b=>{h.mergeDateTimeFormat(b,l.value[b])}),Object.keys(m.value).forEach(b=>{h.mergeNumberFormat(b,m.value[b])}),h.escapeParameter=p,h.fallbackFormat=D,h.fallbackRoot=L,h.fallbackWarn=_,h.missingWarn=d,h.warnHtmlMessage=u}return I.onBeforeMount(()=>{if(e.proxy==null||e.proxy.$i18n==null)throw me(Ae.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const h=a.value=e.proxy.$i18n.__composer;t==="global"?(i.value=h.locale.value,c.value=h.fallbackLocale.value,f.value=h.messages.value,l.value=h.datetimeFormats.value,m.value=h.numberFormats.value):s&&C(h)}),oe}const Hc=["locale","fallbackLocale","availableLocales"],fs=["t","rt","d","n","tm","te"];function Wc(e,t){const n=Object.create(null);return Hc.forEach(s=>{const a=Object.getOwnPropertyDescriptor(t,s);if(!a)throw me(Ae.UNEXPECTED_ERROR);const o=I.isRef(a.value)?{get(){return a.value.value},set(i){a.value.value=i}}:{get(){return a.get&&a.get()}};Object.defineProperty(n,s,o)}),e.config.globalProperties.$i18n=n,fs.forEach(s=>{const a=Object.getOwnPropertyDescriptor(t,s);if(!a||!a.value)throw me(Ae.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${s}`,a)}),()=>{delete e.config.globalProperties.$i18n,fs.forEach(s=>{delete e.config.globalProperties[`$${s}`]})}}if(Mc(),__INTLIFY_JIT_COMPILATION__?Sa(yc):Sa(Ic),Ac(ql),gc(Ia),__INTLIFY_PROD_DEVTOOLS__){const e=Fe();e.__INTLIFY__=!0,ac(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}var Vc={},qc={},$c={},ar={sc:Vc,en:qc,tc:$c};const{isObject:Zc}=Wt();class Xc{constructor(){this._caches=Object.create(null)}interpolate(t,n){if(!n)return t;let r=this._caches[t];return r||(r=tu(t),this._caches[t]=r),nu(r,n).join("")}}const zc=/^(?:\d)+/,eu=/^(?:\w)+/;function tu(e){const t=[];let n=0,r="";for(;n<e.length;){let s=e[n++];if(s==="{"){r&&t.push({type:"text",value:r}),r="";let a="";for(s=e[n++];s!==void 0&&s!=="}";)a+=s,s=e[n++];const o=s==="}";let i="unknown";zc.test(a)?i="list":o&&eu.test(a)&&(i="named"),t.push({value:a,type:i})}else s==="%"?e[n]!=="{"&&(r+=s):r+=s}return r&&t.push({type:"text",value:r}),t}function nu(e,t){const n=[];let r=0;const s=Array.isArray(t)?"list":Zc(t)?"named":"unknown";if(s==="unknown")return n;for(;r<e.length;){const a=e[r];switch(a.type){case"text":n.push(a.value);break;case"list":n.push(t[parseInt(a.value,10)]);break;case"named":s==="named"&&n.push(t[a.value]);break}r++}return n}var sr={},ru=e=>encodeURIComponent(e).replace(/[!'()*]/g,t=>`%${t.charCodeAt(0).toString(16).toUpperCase()}`),ds="%[a-f0-9]{2}",hs=new RegExp("("+ds+")|([^%]+?)","gi"),As=new RegExp("("+ds+")+","gi");function or(e,t){try{return[decodeURIComponent(e.join(""))]}catch{}if(e.length===1)return e;t=t||1;var n=e.slice(0,t),r=e.slice(t);return Array.prototype.concat.call([],or(n),or(r))}function au(e){try{return decodeURIComponent(e)}catch{for(var t=e.match(hs)||[],n=1;n<t.length;n++)e=or(t,n).join(""),t=e.match(hs)||[];return e}}function su(e){for(var t={"%FE%FF":"\uFFFD\uFFFD","%FF%FE":"\uFFFD\uFFFD"},n=As.exec(e);n;){try{t[n[0]]=decodeURIComponent(n[0])}catch{var r=au(n[0]);r!==n[0]&&(t[n[0]]=r)}n=As.exec(e)}t["%C2"]="\uFFFD";for(var s=Object.keys(t),a=0;a<s.length;a++){var o=s[a];e=e.replace(new RegExp(o,"g"),t[o])}return e}var ou=function(e){if(typeof e!="string")throw new TypeError("Expected `encodedURI` to be of type `string`, got `"+typeof e+"`");try{return e=e.replace(/\+/g," "),decodeURIComponent(e)}catch{return su(e)}},iu=(e,t)=>{if(!(typeof e=="string"&&typeof t=="string"))throw new TypeError("Expected the arguments to be of type `string`");if(t==="")return[e];const n=e.indexOf(t);return n===-1?[e]:[e.slice(0,n),e.slice(n+t.length)]},lu=function(e,t){for(var n={},r=Object.keys(e),s=Array.isArray(t),a=0;a<r.length;a++){var o=r[a],i=e[o];(s?t.indexOf(o)!==-1:t(o,i,e))&&(n[o]=i)}return n};(function(e){const t=ru,n=ou,r=iu,s=lu,a=u=>u==null,o=Symbol("encodeFragmentIdentifier");function i(u){switch(u.arrayFormat){case"index":return p=>(A,y)=>{const E=A.length;return y===void 0||u.skipNull&&y===null||u.skipEmptyString&&y===""?A:y===null?[...A,[l(p,u),"[",E,"]"].join("")]:[...A,[l(p,u),"[",l(E,u),"]=",l(y,u)].join("")]};case"bracket":return p=>(A,y)=>y===void 0||u.skipNull&&y===null||u.skipEmptyString&&y===""?A:y===null?[...A,[l(p,u),"[]"].join("")]:[...A,[l(p,u),"[]=",l(y,u)].join("")];case"colon-list-separator":return p=>(A,y)=>y===void 0||u.skipNull&&y===null||u.skipEmptyString&&y===""?A:y===null?[...A,[l(p,u),":list="].join("")]:[...A,[l(p,u),":list=",l(y,u)].join("")];case"comma":case"separator":case"bracket-separator":{const p=u.arrayFormat==="bracket-separator"?"[]=":"=";return A=>(y,E)=>E===void 0||u.skipNull&&E===null||u.skipEmptyString&&E===""?y:(E=E===null?"":E,y.length===0?[[l(A,u),p,l(E,u)].join("")]:[[y,l(E,u)].join(u.arrayFormatSeparator)])}default:return p=>(A,y)=>y===void 0||u.skipNull&&y===null||u.skipEmptyString&&y===""?A:y===null?[...A,l(p,u)]:[...A,[l(p,u),"=",l(y,u)].join("")]}}function c(u){let p;switch(u.arrayFormat){case"index":return(A,y,E)=>{if(p=/\[(\d*)\]$/.exec(A),A=A.replace(/\[\d*\]$/,""),!p){E[A]=y;return}E[A]===void 0&&(E[A]={}),E[A][p[1]]=y};case"bracket":return(A,y,E)=>{if(p=/(\[\])$/.exec(A),A=A.replace(/\[\]$/,""),!p){E[A]=y;return}if(E[A]===void 0){E[A]=[y];return}E[A]=[].concat(E[A],y)};case"colon-list-separator":return(A,y,E)=>{if(p=/(:list)$/.exec(A),A=A.replace(/:list$/,""),!p){E[A]=y;return}if(E[A]===void 0){E[A]=[y];return}E[A]=[].concat(E[A],y)};case"comma":case"separator":return(A,y,E)=>{const M=typeof y=="string"&&y.includes(u.arrayFormatSeparator),k=typeof y=="string"&&!M&&m(y,u).includes(u.arrayFormatSeparator);y=k?m(y,u):y;const j=M||k?y.split(u.arrayFormatSeparator).map(q=>m(q,u)):y===null?y:m(y,u);E[A]=j};case"bracket-separator":return(A,y,E)=>{const M=/(\[\])$/.test(A);if(A=A.replace(/\[\]$/,""),!M){E[A]=y&&m(y,u);return}const k=y===null?[]:y.split(u.arrayFormatSeparator).map(j=>m(j,u));if(E[A]===void 0){E[A]=k;return}E[A]=[].concat(E[A],k)};default:return(A,y,E)=>{if(E[A]===void 0){E[A]=y;return}E[A]=[].concat(E[A],y)}}}function f(u){if(typeof u!="string"||u.length!==1)throw new TypeError("arrayFormatSeparator must be single character string")}function l(u,p){return p.encode?p.strict?t(u):encodeURIComponent(u):u}function m(u,p){return p.decode?n(u):u}function d(u){return Array.isArray(u)?u.sort():typeof u=="object"?d(Object.keys(u)).sort((p,A)=>Number(p)-Number(A)).map(p=>u[p]):u}function _(u){const p=u.indexOf("#");return p!==-1&&(u=u.slice(0,p)),u}function L(u){let p="";const A=u.indexOf("#");return A!==-1&&(p=u.slice(A)),p}function D(u){u=_(u);const p=u.indexOf("?");return p===-1?"":u.slice(p+1)}function S(u,p){return p.parseNumbers&&!Number.isNaN(Number(u))&&typeof u=="string"&&u.trim()!==""?u=Number(u):p.parseBooleans&&u!==null&&(u.toLowerCase()==="true"||u.toLowerCase()==="false")&&(u=u.toLowerCase()==="true"),u}function g(u,p){p=Object.assign({decode:!0,sort:!0,arrayFormat:"none",arrayFormatSeparator:",",parseNumbers:!1,parseBooleans:!1},p),f(p.arrayFormatSeparator);const A=c(p),y=Object.create(null);if(typeof u!="string"||(u=u.trim().replace(/^[?#&]/,""),!u))return y;for(const E of u.split("&")){if(E==="")continue;let[M,k]=r(p.decode?E.replace(/\+/g," "):E,"=");k=k===void 0?null:["comma","separator","bracket-separator"].includes(p.arrayFormat)?k:m(k,p),A(m(M,p),k,y)}for(const E of Object.keys(y)){const M=y[E];if(typeof M=="object"&&M!==null)for(const k of Object.keys(M))M[k]=S(M[k],p);else y[E]=S(M,p)}return p.sort===!1?y:(p.sort===!0?Object.keys(y).sort():Object.keys(y).sort(p.sort)).reduce((E,M)=>{const k=y[M];return Boolean(k)&&typeof k=="object"&&!Array.isArray(k)?E[M]=d(k):E[M]=k,E},Object.create(null))}e.extract=D,e.parse=g,e.stringify=(u,p)=>{if(!u)return"";p=Object.assign({encode:!0,strict:!0,arrayFormat:"none",arrayFormatSeparator:","},p),f(p.arrayFormatSeparator);const A=k=>p.skipNull&&a(u[k])||p.skipEmptyString&&u[k]==="",y=i(p),E={};for(const k of Object.keys(u))A(k)||(E[k]=u[k]);const M=Object.keys(E);return p.sort!==!1&&M.sort(p.sort),M.map(k=>{const j=u[k];return j===void 0?"":j===null?l(k,p):Array.isArray(j)?j.length===0&&p.arrayFormat==="bracket-separator"?l(k,p)+"[]":j.reduce(y(k),[]).join("&"):l(k,p)+"="+l(j,p)}).filter(k=>k.length>0).join("&")},e.parseUrl=(u,p)=>{p=Object.assign({decode:!0},p);const[A,y]=r(u,"#");return Object.assign({url:A.split("?")[0]||"",query:g(D(u),p)},p&&p.parseFragmentIdentifier&&y?{fragmentIdentifier:m(y,p)}:{})},e.stringifyUrl=(u,p)=>{p=Object.assign({encode:!0,strict:!0,[o]:!0},p);const A=_(u.url).split("?")[0]||"",y=e.extract(u.url),E=e.parse(y,{sort:!1}),M=Object.assign(E,u.query);let k=e.stringify(M,p);k&&(k=`?${k}`);let j=L(u.url);return u.fragmentIdentifier&&(j=`#${p[o]?l(u.fragmentIdentifier,p):u.fragmentIdentifier}`),`${A}${k}${j}`},e.pick=(u,p,A)=>{A=Object.assign({parseFragmentIdentifier:!0,[o]:!1},A);const{url:y,query:E,fragmentIdentifier:M}=e.parseUrl(u,A);return e.stringifyUrl({url:y,query:s(E,p),fragmentIdentifier:M},A)},e.exclude=(u,p,A)=>{const y=Array.isArray(p)?E=>!p.includes(E):(E,M)=>!p(E,M);return e.pick(u,y,A)}})(sr);const gs=sr.parse(location.search),ms=sr.parse(location.hash.split("?").splice(-1)[0]);let cu=Object.assign({},gs,ms);function ps(){return{mQuery:cu,query:gs,hQuery:ms}}const uu=new Xc,{lang:fu}=ps().mQuery,Me=I.ref(fu||window.sessionStorage.getItem("lang")||"tc"),rn=Fc({locale:Me.value}),ir=I.reactive(ar.tc),Es=e=>{Me.value=rn.global.locale=e||"tc",Object.assign(ir,ar[Me.value],rn.global.messages[Me.value]),window.sessionStorage.setItem("lang",Me.value),console.log("\u5F53\u524D\u8BED\u8A00",Me.value)},du=e=>{Object.keys(e).forEach(n=>{rn.global.setLocaleMessage(n,e[n])}),Object.assign(ir,ar[Me.value],rn.global.messages[Me.value])};Es(Me.value);const hu=e=>{I.watch(Me,()=>{e(Me)},{immediate:!0})};function Au(e,t){return uu.interpolate(e,t)}function an(){return{state:ir,lang:Me,setLang:Es,onLangChange:hu,format:Au,setMessages:du}}const Cs=na();let sn="";function gu(e){let t=!1;const{leftTime:n}=oa(e);if(n>0){sn=e;let r="login-jwt";Cs.save(r,e,n),t=!0}else sn="";return t}async function lr(e,t,n){const r=Object.assign({params:{},headers:{},data:{}},n);if(sn)r.headers.Authorization=`Bearer ${sn}`;else{let s=Cs.customLoad("via:system:login-jwt");s&&(r.headers.Authorization=`Bearer ${s.v}`)}return r.headers.lang=an().lang.value,qt(e,t,r)}async function mu(e,t){return lr("get",e,t)}async function pu(e,t){return lr("post",e,t)}function Eu(){return{host:Pt.host,request:qt,post:Hi,get:Yi,setJwt:gu,authRequest:lr,authGet:mu,authPost:pu,registerResponseInterceptor:Wi}}var on=(e=>(e.LOGIN="v:event:login",e.WEB_HIDDEN="__web-hidden__",e.WEB_RESUME="__web-resume__",e))(on||{});const De={$$vuuid:1,id_0:{$$vuuid:1}},_t={$$vuuid:1};function Cu(e){try{e.$$vuuid=e.$$vuuid||De.$$vuuid++,De[`id_${e.$$vuuid}`]={$$vuuid:1}}catch{console.log(`can't register by ${e}`)}}function Iu(e){try{delete De[`id_${e.$$vuuid}`]}catch{console.log(`can't unregister by ${e}`)}}function Is(e,t,n){typeof(n==null?void 0:n.$$vuuid)=="undefined"&&(n={$$vuuid:0});const r=De[`id_${n.$$vuuid}`];if(!r)throw Error("\u672A\u6CE8\u518Cinstance");let s=r[e]||(r[e]={});t.$$vuuid=t.$$vuuid||r.$$vuuid++,s[t.$$vuuid]=t,yu(e)}function yu(e){_t[e]&&(ys(e,..._t[e]),delete _t[e])}function _u(e,t,n){t.$$once=!0,Is(e,t,n)}function ys(e,...t){for(const n of Object.keys(De)){let s=De[n][e];if(!!s)for(const a of Object.keys(s)){const o=s[a];o.$$once&&ln(e,o,{$$vuuid:n.replace("id_","")}),o(...t)}}}function wu(e,t,...n){const r=De[`id_${t==null?void 0:t.$$vuuid}`];if(!r)return;let s=r[e];if(!!s)for(const a of Object.keys(s)){const o=s[a];o.$$once&&ln(e,o,t),o(...n)}}function bu(e,...t){let n=!1;for(const r of Object.keys(De)){let a=De[r][e];if(!!a)for(const o of Object.keys(a)){const i=a[o];i.$$once&&ln(e,i,{$$vuuid:r.replace("id_","")}),n=!0,i(...t)}}if(!n){_t[e]=t;return}delete _t[e]}function ln(e,t,n){try{n.$$vuuid=n.$$vuuid}catch{n={$$vuuid:0}}if(!t||!t.$$vuuid){for(const a of Object.keys(De)){const o=De[a];delete o[e]}return}const r=De[`id_${n.$$vuuid}`];let s=r[e]||(r[e]={});!s||(t.$$vuuid in s&&delete s[t.$$vuuid],Object.keys(s).length<=0&&delete r[e])}function cn(){return{register:Cu,unregister:Iu,on:Is,once:_u,emit:ys,emitToInstance:wu,stickyEmit:bu,off:ln}}let _s=!1;try{const e={};Object.defineProperty(e,"passive",{get(){_s=!0}}),window.addEventListener("test-passive",null,e)}catch{}function ws(e,t,n,r){if(r&&typeof r!="boolean"){const{passive:a=!1,capture:o=!1}=r;r=_s?{capture:o,passive:a}:o}if(typeof t=="string")return e.addEventListener(t,n,r),()=>{e.removeEventListener(t,n,r)};const s=[];return t.forEach(a=>{e.addEventListener(a,n,r),s.push(()=>{e.removeEventListener(a,n,r)})}),()=>{s.forEach(a=>a())}}const{BASE:Su}=Ft(),{md5:Lu}=Wt(),bs=Su.replace(/\/$/,""),Mu=(()=>{const e=(r,s,a)=>{r.onload=o=>{r.onload=null,s(o)},r.onerror=o=>{r.onerror=null,a(o)}};return{_loadJs:r=>new Promise((s,a)=>{let o=document.createElement("script");e(o,s,a),o.src=r,document.head.appendChild(o)}),_loadCss:r=>new Promise((s,a)=>{let o=document.createElement("link");o.rel="stylesheet",o.type="text/css",e(o,s,a),o.href=r,document.head.appendChild(o)})}})(),wt={},Ss=new Set;async function bt(e,t){!t.startsWith("http")&&!t.startsWith("//")&&(t=(bs||"")+t);const n=Lu(t,16);let r=new Date().getTime();return new Promise((s,a)=>{if(Ss.has(n))return s(!0);if(wt[n]){wt[n].push(s);return}console.log(`%c \u5F00\u59CB\u52A0\u8F7D${e}`,"color: #ffffff;background-color: #2a5caa;padding: 5px",t),wt[n]=[s],Mu[e==="js"?"_loadJs":"_loadCss"](t).then(()=>{const i=new Date().getTime();console.log(`%c ${e}\u52A0\u8F7D\u5B8C\u6210`,"color: #ffffff;background-color: #1d953f;padding: 5px",`\u8017\u65F6\uFF1A${i-r}\u6BEB\u79D2 ${t}`),wt[n].forEach(c=>c(!0)),delete wt[n],Ss.add(n)}).catch(a)}).then(s=>s).catch(s=>{const a=new Date().getTime();throw console.log(`%c ${e}\u52A0\u8F7D\u5931\u8D25`,"color: #ffffff;background-color: #ff0066;padding: 5px",`\u8017\u65F6\uFF1A${a-r}\u6BEB\u79D2 ${t}`),s})}async function Du(e){return bt("js",e)}async function ku(e){return bt("css",e)}async function vu(e,t,n=!1){if(n){for(let r=0;r<t.length;++r)await bt(e,t[r]);return}return Promise.all(t.map(r=>bt(e,r)))}function Ru(){return{baseUrl:bs,load:bt,loadJs:Du,loadCss:ku,loadAssetsList:vu}}const{emit:Ls}=cn();let cr=I.reactive({isHidden:!1}),ze=null;if("hidden"in document?ze="hidden":"webkitHidden"in document?ze="webkitHidden":"mozHidden"in document&&(ze="mozHidden"),ze){const e=ze.replace(/hidden/i,"visibilitychange");ws(document,e,t=>{!ze||(document[ze]?(cr.isHidden=!0,Ls(on.WEB_HIDDEN)):(cr.isHidden=!1,Ls(on.WEB_RESUME)))})}function Bu(){return cr}var Ms=(e=>(e.DEV="dev",e.PROD="prod",e))(Ms||{}),Ds=(e=>(e.web="web",e.wx="wx",e.wxMini="wxMini",e.other="other",e))(Ds||{}),ks=(e=>(e.web="web",e.android="android",e.ios="ios",e))(ks||{});const{isProd:Tu}=Ft();let ur="web",Nu=Tu?"prod":"dev",fr="web";function Ou(){const e=navigator.userAgent;/micromessenger/i.test(e)&&(ur="wx")}function Qu(e){ur=e}function Fu(){const e=navigator.userAgent;(e.indexOf("Android")>-1||e.indexOf("Adr")>-1)&&(fr="android"),e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)&&(fr="ios")}Ou(),Fu();function Pu(){return{runtimeEnv:ur,runtimeMode:Nu,mobileType:fr,setRuntimeEnv:Qu}}/*!
  * vue-router v4.2.5
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */const dt=typeof window!="undefined";function Uu(e){return e.__esModule||e[Symbol.toStringTag]==="Module"}const ce=Object.assign;function dr(e,t){const n={};for(const r in t){const s=t[r];n[r]=Re(s)?s.map(e):e(s)}return n}const St=()=>{},Re=Array.isArray,xu=/\/$/,Gu=e=>e.replace(xu,"");function hr(e,t,n="/"){let r,s={},a="",o="";const i=t.indexOf("#");let c=t.indexOf("?");return i<c&&i>=0&&(c=-1),c>-1&&(r=t.slice(0,c),a=t.slice(c+1,i>-1?i:t.length),s=e(a)),i>-1&&(r=r||t.slice(0,i),o=t.slice(i,t.length)),r=Yu(r!=null?r:t,n),{fullPath:r+(a&&"?")+a+o,path:r,query:s,hash:o}}function Ku(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function vs(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ju(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&ht(t.matched[r],n.matched[s])&&Rs(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function ht(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Rs(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!ju(e[n],t[n]))return!1;return!0}function ju(e,t){return Re(e)?Bs(e,t):Re(t)?Bs(t,e):e===t}function Bs(e,t){return Re(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function Yu(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let a=n.length-1,o,i;for(o=0;o<r.length;o++)if(i=r[o],i!==".")if(i==="..")a>1&&a--;else break;return n.slice(0,a).join("/")+"/"+r.slice(o-(o===r.length?1:0)).join("/")}var Lt;(function(e){e.pop="pop",e.push="push"})(Lt||(Lt={}));var Mt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Mt||(Mt={}));function Hu(e){if(!e)if(dt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Gu(e)}const Wu=/^[^#]+#/;function Vu(e,t){return e.replace(Wu,"#")+t}function qu(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const un=()=>({left:window.pageXOffset,top:window.pageYOffset});function $u(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=qu(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.pageXOffset,t.top!=null?t.top:window.pageYOffset)}function Ts(e,t){return(history.state?history.state.position-t:-1)+e}const Ar=new Map;function Zu(e,t){Ar.set(e,t)}function Xu(e){const t=Ar.get(e);return Ar.delete(e),t}let zu=()=>location.protocol+"//"+location.host;function Ns(e,t){const{pathname:n,search:r,hash:s}=t,a=e.indexOf("#");if(a>-1){let i=s.includes(e.slice(a))?e.slice(a).length:1,c=s.slice(i);return c[0]!=="/"&&(c="/"+c),vs(c,"")}return vs(n,e)+r+s}function ef(e,t,n,r){let s=[],a=[],o=null;const i=({state:d})=>{const _=Ns(e,location),L=n.value,D=t.value;let S=0;if(d){if(n.value=_,t.value=d,o&&o===L){o=null;return}S=D?d.position-D.position:0}else r(_);s.forEach(g=>{g(n.value,L,{delta:S,type:Lt.pop,direction:S?S>0?Mt.forward:Mt.back:Mt.unknown})})};function c(){o=n.value}function f(d){s.push(d);const _=()=>{const L=s.indexOf(d);L>-1&&s.splice(L,1)};return a.push(_),_}function l(){const{history:d}=window;!d.state||d.replaceState(ce({},d.state,{scroll:un()}),"")}function m(){for(const d of a)d();a=[],window.removeEventListener("popstate",i),window.removeEventListener("beforeunload",l)}return window.addEventListener("popstate",i),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:c,listen:f,destroy:m}}function Os(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?un():null}}function tf(e){const{history:t,location:n}=window,r={value:Ns(e,n)},s={value:t.state};s.value||a(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(c,f,l){const m=e.indexOf("#"),d=m>-1?(n.host&&document.querySelector("base")?e:e.slice(m))+c:zu()+e+c;try{t[l?"replaceState":"pushState"](f,"",d),s.value=f}catch(_){console.error(_),n[l?"replace":"assign"](d)}}function o(c,f){const l=ce({},t.state,Os(s.value.back,c,s.value.forward,!0),f,{position:s.value.position});a(c,l,!0),r.value=c}function i(c,f){const l=ce({},s.value,t.state,{forward:c,scroll:un()});a(l.current,l,!0);const m=ce({},Os(r.value,c,null),{position:l.position+1},f);a(c,m,!1),r.value=c}return{location:r,state:s,push:i,replace:o}}function nf(e){e=Hu(e);const t=tf(e),n=ef(e,t.state,t.location,t.replace);function r(a,o=!0){o||n.pauseListeners(),history.go(a)}const s=ce({location:"",base:e,go:r,createHref:Vu.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function rf(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),nf(e)}function af(e){return typeof e=="string"||e&&typeof e=="object"}function Qs(e){return typeof e=="string"||typeof e=="symbol"}const Ve={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},Fs=Symbol("");var Ps;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ps||(Ps={}));function At(e,t){return ce(new Error,{type:e,[Fs]:!0},t)}function Ue(e,t){return e instanceof Error&&Fs in e&&(t==null||!!(e.type&t))}const Us="[^/]+?",sf={sensitive:!1,strict:!1,start:!0,end:!0},of=/[.+*?^${}()[\]/\\]/g;function lf(e,t){const n=ce({},sf,t),r=[];let s=n.start?"^":"";const a=[];for(const f of e){const l=f.length?[]:[90];n.strict&&!f.length&&(s+="/");for(let m=0;m<f.length;m++){const d=f[m];let _=40+(n.sensitive?.25:0);if(d.type===0)m||(s+="/"),s+=d.value.replace(of,"\\$&"),_+=40;else if(d.type===1){const{value:L,repeatable:D,optional:S,regexp:g}=d;a.push({name:L,repeatable:D,optional:S});const u=g||Us;if(u!==Us){_+=10;try{new RegExp(`(${u})`)}catch(A){throw new Error(`Invalid custom RegExp for param "${L}" (${u}): `+A.message)}}let p=D?`((?:${u})(?:/(?:${u}))*)`:`(${u})`;m||(p=S&&f.length<2?`(?:/${p})`:"/"+p),S&&(p+="?"),s+=p,_+=20,S&&(_+=-8),D&&(_+=-20),u===".*"&&(_+=-50)}l.push(_)}r.push(l)}if(n.strict&&n.end){const f=r.length-1;r[f][r[f].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&(s+="(?:/|$)");const o=new RegExp(s,n.sensitive?"":"i");function i(f){const l=f.match(o),m={};if(!l)return null;for(let d=1;d<l.length;d++){const _=l[d]||"",L=a[d-1];m[L.name]=_&&L.repeatable?_.split("/"):_}return m}function c(f){let l="",m=!1;for(const d of e){(!m||!l.endsWith("/"))&&(l+="/"),m=!1;for(const _ of d)if(_.type===0)l+=_.value;else if(_.type===1){const{value:L,repeatable:D,optional:S}=_,g=L in f?f[L]:"";if(Re(g)&&!D)throw new Error(`Provided param "${L}" is an array but it is not repeatable (* or + modifiers)`);const u=Re(g)?g.join("/"):g;if(!u)if(S)d.length<2&&(l.endsWith("/")?l=l.slice(0,-1):m=!0);else throw new Error(`Missing required param "${L}"`);l+=u}}return l||"/"}return{re:o,score:r,keys:a,parse:i,stringify:c}}function cf(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function uf(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const a=cf(r[n],s[n]);if(a)return a;n++}if(Math.abs(s.length-r.length)===1){if(xs(r))return 1;if(xs(s))return-1}return s.length-r.length}function xs(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ff={type:0,value:""},df=/[a-zA-Z0-9_]/;function hf(e){if(!e)return[[]];if(e==="/")return[[ff]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(_){throw new Error(`ERR (${n})/"${f}": ${_}`)}let n=0,r=n;const s=[];let a;function o(){a&&s.push(a),a=[]}let i=0,c,f="",l="";function m(){!f||(n===0?a.push({type:0,value:f}):n===1||n===2||n===3?(a.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:f,regexp:l,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),f="")}function d(){f+=c}for(;i<e.length;){if(c=e[i++],c==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:c==="/"?(f&&m(),o()):c===":"?(m(),n=1):d();break;case 4:d(),n=r;break;case 1:c==="("?n=2:df.test(c)?d():(m(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&i--);break;case 2:c===")"?l[l.length-1]=="\\"?l=l.slice(0,-1)+c:n=3:l+=c;break;case 3:m(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&i--,l="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${f}"`),m(),o(),s}function Af(e,t,n){const r=lf(hf(e.path),n),s=ce(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function gf(e,t){const n=[],r=new Map;t=Js({strict:!1,end:!0,sensitive:!1},t);function s(l){return r.get(l)}function a(l,m,d){const _=!d,L=mf(l);L.aliasOf=d&&d.record;const D=Js(t,l),S=[L];if("alias"in l){const p=typeof l.alias=="string"?[l.alias]:l.alias;for(const A of p)S.push(ce({},L,{components:d?d.record.components:L.components,path:A,aliasOf:d?d.record:L}))}let g,u;for(const p of S){const{path:A}=p;if(m&&A[0]!=="/"){const y=m.record.path,E=y[y.length-1]==="/"?"":"/";p.path=m.record.path+(A&&E+A)}if(g=Af(p,m,D),d?d.alias.push(g):(u=u||g,u!==g&&u.alias.push(g),_&&l.name&&!Ks(g)&&o(l.name)),L.children){const y=L.children;for(let E=0;E<y.length;E++)a(y[E],g,d&&d.children[E])}d=d||g,(g.record.components&&Object.keys(g.record.components).length||g.record.name||g.record.redirect)&&c(g)}return u?()=>{o(u)}:St}function o(l){if(Qs(l)){const m=r.get(l);m&&(r.delete(l),n.splice(n.indexOf(m),1),m.children.forEach(o),m.alias.forEach(o))}else{const m=n.indexOf(l);m>-1&&(n.splice(m,1),l.record.name&&r.delete(l.record.name),l.children.forEach(o),l.alias.forEach(o))}}function i(){return n}function c(l){let m=0;for(;m<n.length&&uf(l,n[m])>=0&&(l.record.path!==n[m].record.path||!js(l,n[m]));)m++;n.splice(m,0,l),l.record.name&&!Ks(l)&&r.set(l.record.name,l)}function f(l,m){let d,_={},L,D;if("name"in l&&l.name){if(d=r.get(l.name),!d)throw At(1,{location:l});D=d.record.name,_=ce(Gs(m.params,d.keys.filter(u=>!u.optional).map(u=>u.name)),l.params&&Gs(l.params,d.keys.map(u=>u.name))),L=d.stringify(_)}else if("path"in l)L=l.path,d=n.find(u=>u.re.test(L)),d&&(_=d.parse(L),D=d.record.name);else{if(d=m.name?r.get(m.name):n.find(u=>u.re.test(m.path)),!d)throw At(1,{location:l,currentLocation:m});D=d.record.name,_=ce({},m.params,l.params),L=d.stringify(_)}const S=[];let g=d;for(;g;)S.unshift(g.record),g=g.parent;return{name:D,path:L,params:_,matched:S,meta:Ef(S)}}return e.forEach(l=>a(l)),{addRoute:a,resolve:f,removeRoute:o,getRoutes:i,getRecordMatcher:s}}function Gs(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function mf(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:pf(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function pf(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Ks(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Ef(e){return e.reduce((t,n)=>ce(t,n.meta),{})}function Js(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function js(e,t){return t.children.some(n=>n===e||js(e,n))}const Ys=/#/g,Cf=/&/g,If=/\//g,yf=/=/g,_f=/\?/g,Hs=/\+/g,wf=/%5B/g,bf=/%5D/g,Ws=/%5E/g,Sf=/%60/g,Vs=/%7B/g,Lf=/%7C/g,qs=/%7D/g,Mf=/%20/g;function gr(e){return encodeURI(""+e).replace(Lf,"|").replace(wf,"[").replace(bf,"]")}function Df(e){return gr(e).replace(Vs,"{").replace(qs,"}").replace(Ws,"^")}function mr(e){return gr(e).replace(Hs,"%2B").replace(Mf,"+").replace(Ys,"%23").replace(Cf,"%26").replace(Sf,"`").replace(Vs,"{").replace(qs,"}").replace(Ws,"^")}function kf(e){return mr(e).replace(yf,"%3D")}function vf(e){return gr(e).replace(Ys,"%23").replace(_f,"%3F")}function Rf(e){return e==null?"":vf(e).replace(If,"%2F")}function fn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}function Bf(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const a=r[s].replace(Hs," "),o=a.indexOf("="),i=fn(o<0?a:a.slice(0,o)),c=o<0?null:fn(a.slice(o+1));if(i in t){let f=t[i];Re(f)||(f=t[i]=[f]),f.push(c)}else t[i]=c}return t}function $s(e){let t="";for(let n in e){const r=e[n];if(n=kf(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Re(r)?r.map(a=>a&&mr(a)):[r&&mr(r)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+n,a!=null&&(t+="="+a))})}return t}function Tf(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Re(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const Nf=Symbol(""),Zs=Symbol(""),pr=Symbol(""),Xs=Symbol(""),Er=Symbol("");function Dt(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function qe(e,t,n,r,s){const a=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((o,i)=>{const c=m=>{m===!1?i(At(4,{from:n,to:t})):m instanceof Error?i(m):af(m)?i(At(2,{from:t,to:m})):(a&&r.enterCallbacks[s]===a&&typeof m=="function"&&a.push(m),o())},f=e.call(r&&r.instances[s],t,n,c);let l=Promise.resolve(f);e.length<3&&(l=l.then(c)),l.catch(m=>i(m))})}function Cr(e,t,n,r){const s=[];for(const a of e)for(const o in a.components){let i=a.components[o];if(!(t!=="beforeRouteEnter"&&!a.instances[o]))if(Of(i)){const f=(i.__vccOpts||i)[t];f&&s.push(qe(f,n,r,a,o))}else{let c=i();s.push(()=>c.then(f=>{if(!f)return Promise.reject(new Error(`Couldn't resolve component "${o}" at "${a.path}"`));const l=Uu(f)?f.default:f;a.components[o]=l;const d=(l.__vccOpts||l)[t];return d&&qe(d,n,r,a,o)()}))}}return s}function Of(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function zs(e){const t=I.inject(pr),n=I.inject(Xs),r=I.computed(()=>t.resolve(I.unref(e.to))),s=I.computed(()=>{const{matched:c}=r.value,{length:f}=c,l=c[f-1],m=n.matched;if(!l||!m.length)return-1;const d=m.findIndex(ht.bind(null,l));if(d>-1)return d;const _=eo(c[f-2]);return f>1&&eo(l)===_&&m[m.length-1].path!==_?m.findIndex(ht.bind(null,c[f-2])):d}),a=I.computed(()=>s.value>-1&&Pf(n.params,r.value.params)),o=I.computed(()=>s.value>-1&&s.value===n.matched.length-1&&Rs(n.params,r.value.params));function i(c={}){return Ff(c)?t[I.unref(e.replace)?"replace":"push"](I.unref(e.to)).catch(St):Promise.resolve()}return{route:r,href:I.computed(()=>r.value.href),isActive:a,isExactActive:o,navigate:i}}const Qf=I.defineComponent({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:zs,setup(e,{slots:t}){const n=I.reactive(zs(e)),{options:r}=I.inject(pr),s=I.computed(()=>({[to(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[to(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const a=t.default&&t.default(n);return e.custom?a:I.h("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},a)}}});function Ff(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Pf(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!Re(s)||s.length!==r.length||r.some((a,o)=>a!==s[o]))return!1}return!0}function eo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const to=(e,t,n)=>e!=null?e:t!=null?t:n,Uf=I.defineComponent({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=I.inject(Er),s=I.computed(()=>e.route||r.value),a=I.inject(Zs,0),o=I.computed(()=>{let f=I.unref(a);const{matched:l}=s.value;let m;for(;(m=l[f])&&!m.components;)f++;return f}),i=I.computed(()=>s.value.matched[o.value]);I.provide(Zs,I.computed(()=>o.value+1)),I.provide(Nf,i),I.provide(Er,s);const c=I.ref();return I.watch(()=>[c.value,i.value,e.name],([f,l,m],[d,_,L])=>{l&&(l.instances[m]=f,_&&_!==l&&f&&f===d&&(l.leaveGuards.size||(l.leaveGuards=_.leaveGuards),l.updateGuards.size||(l.updateGuards=_.updateGuards))),f&&l&&(!_||!ht(l,_)||!d)&&(l.enterCallbacks[m]||[]).forEach(D=>D(f))},{flush:"post"}),()=>{const f=s.value,l=e.name,m=i.value,d=m&&m.components[l];if(!d)return no(n.default,{Component:d,route:f});const _=m.props[l],L=_?_===!0?f.params:typeof _=="function"?_(f):_:null,D=g=>{g.component.isUnmounted&&(m.instances[l]=null)},S=I.h(d,ce({},L,t,{onVnodeUnmounted:D,ref:c}));return no(n.default,{Component:S,route:f})||S}}});function no(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const xf=Uf;function Gf(e){const t=gf(e.routes,e),n=e.parseQuery||Bf,r=e.stringifyQuery||$s,s=e.history,a=Dt(),o=Dt(),i=Dt(),c=I.shallowRef(Ve);let f=Ve;dt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const l=dr.bind(null,w=>""+w),m=dr.bind(null,Rf),d=dr.bind(null,fn);function _(w,T){let B,U;return Qs(w)?(B=t.getRecordMatcher(w),U=T):U=w,t.addRoute(U,B)}function L(w){const T=t.getRecordMatcher(w);T&&t.removeRoute(T)}function D(){return t.getRoutes().map(w=>w.record)}function S(w){return!!t.getRecordMatcher(w)}function g(w,T){if(T=ce({},T||c.value),typeof w=="string"){const h=hr(n,w,T.path),b=t.resolve({path:h.path},T),G=s.createHref(h.fullPath);return ce(h,b,{params:d(b.params),hash:fn(h.hash),redirectedFrom:void 0,href:G})}let B;if("path"in w)B=ce({},w,{path:hr(n,w.path,T.path).path});else{const h=ce({},w.params);for(const b in h)h[b]==null&&delete h[b];B=ce({},w,{params:m(h)}),T.params=m(T.params)}const U=t.resolve(B,T),re=w.hash||"";U.params=l(d(U.params));const oe=Ku(r,ce({},w,{hash:Df(re),path:U.path})),C=s.createHref(oe);return ce({fullPath:oe,hash:re,query:r===$s?Tf(w.query):w.query||{}},U,{redirectedFrom:void 0,href:C})}function u(w){return typeof w=="string"?hr(n,w,c.value.path):ce({},w)}function p(w,T){if(f!==w)return At(8,{from:T,to:w})}function A(w){return M(w)}function y(w){return A(ce(u(w),{replace:!0}))}function E(w){const T=w.matched[w.matched.length-1];if(T&&T.redirect){const{redirect:B}=T;let U=typeof B=="function"?B(w):B;return typeof U=="string"&&(U=U.includes("?")||U.includes("#")?U=u(U):{path:U},U.params={}),ce({query:w.query,hash:w.hash,params:"path"in U?{}:w.params},U)}}function M(w,T){const B=f=g(w),U=c.value,re=w.state,oe=w.force,C=w.replace===!0,h=E(B);if(h)return M(ce(u(h),{state:typeof h=="object"?ce({},re,h.state):re,force:oe,replace:C}),T||B);const b=B;b.redirectedFrom=T;let G;return!oe&&Ju(r,U,B)&&(G=At(16,{to:b,from:U}),se(U,U,!0,!1)),(G?Promise.resolve(G):q(b,U)).catch(V=>Ue(V)?Ue(V,2)?V:X(V):N(V,b,U)).then(V=>{if(V){if(Ue(V,2))return M(ce({replace:C},u(V.to),{state:typeof V.to=="object"?ce({},re,V.to.state):re,force:oe}),T||b)}else V=ee(b,U,!0,C,re);return W(b,U,V),V})}function k(w,T){const B=p(w,T);return B?Promise.reject(B):Promise.resolve()}function j(w){const T=F.values().next().value;return T&&typeof T.runWithContext=="function"?T.runWithContext(w):w()}function q(w,T){let B;const[U,re,oe]=Kf(w,T);B=Cr(U.reverse(),"beforeRouteLeave",w,T);for(const h of U)h.leaveGuards.forEach(b=>{B.push(qe(b,w,T))});const C=k.bind(null,w,T);return B.push(C),$(B).then(()=>{B=[];for(const h of a.list())B.push(qe(h,w,T));return B.push(C),$(B)}).then(()=>{B=Cr(re,"beforeRouteUpdate",w,T);for(const h of re)h.updateGuards.forEach(b=>{B.push(qe(b,w,T))});return B.push(C),$(B)}).then(()=>{B=[];for(const h of oe)if(h.beforeEnter)if(Re(h.beforeEnter))for(const b of h.beforeEnter)B.push(qe(b,w,T));else B.push(qe(h.beforeEnter,w,T));return B.push(C),$(B)}).then(()=>(w.matched.forEach(h=>h.enterCallbacks={}),B=Cr(oe,"beforeRouteEnter",w,T),B.push(C),$(B))).then(()=>{B=[];for(const h of o.list())B.push(qe(h,w,T));return B.push(C),$(B)}).catch(h=>Ue(h,8)?h:Promise.reject(h))}function W(w,T,B){i.list().forEach(U=>j(()=>U(w,T,B)))}function ee(w,T,B,U,re){const oe=p(w,T);if(oe)return oe;const C=T===Ve,h=dt?history.state:{};B&&(U||C?s.replace(w.fullPath,ce({scroll:C&&h&&h.scroll},re)):s.push(w.fullPath,re)),c.value=w,se(w,T,B,C),X()}let ne;function x(){ne||(ne=s.listen((w,T,B)=>{if(!H.listening)return;const U=g(w),re=E(U);if(re){M(ce(re,{replace:!0}),U).catch(St);return}f=U;const oe=c.value;dt&&Zu(Ts(oe.fullPath,B.delta),un()),q(U,oe).catch(C=>Ue(C,12)?C:Ue(C,2)?(M(C.to,U).then(h=>{Ue(h,20)&&!B.delta&&B.type===Lt.pop&&s.go(-1,!1)}).catch(St),Promise.reject()):(B.delta&&s.go(-B.delta,!1),N(C,U,oe))).then(C=>{C=C||ee(U,oe,!1),C&&(B.delta&&!Ue(C,8)?s.go(-B.delta,!1):B.type===Lt.pop&&Ue(C,20)&&s.go(-1,!1)),W(U,oe,C)}).catch(St)}))}let O=Dt(),R=Dt(),Y;function N(w,T,B){X(w);const U=R.list();return U.length?U.forEach(re=>re(w,T,B)):console.error(w),Promise.reject(w)}function Z(){return Y&&c.value!==Ve?Promise.resolve():new Promise((w,T)=>{O.add([w,T])})}function X(w){return Y||(Y=!w,x(),O.list().forEach(([T,B])=>w?B(w):T()),O.reset()),w}function se(w,T,B,U){const{scrollBehavior:re}=e;if(!dt||!re)return Promise.resolve();const oe=!B&&Xu(Ts(w.fullPath,0))||(U||!B)&&history.state&&history.state.scroll||null;return I.nextTick().then(()=>re(w,T,oe)).then(C=>C&&$u(C)).catch(C=>N(C,w,T))}const J=w=>s.go(w);let Q;const F=new Set,H={currentRoute:c,listening:!0,addRoute:_,removeRoute:L,hasRoute:S,getRoutes:D,resolve:g,options:e,push:A,replace:y,go:J,back:()=>J(-1),forward:()=>J(1),beforeEach:a.add,beforeResolve:o.add,afterEach:i.add,onError:R.add,isReady:Z,install(w){const T=this;w.component("RouterLink",Qf),w.component("RouterView",xf),w.config.globalProperties.$router=T,Object.defineProperty(w.config.globalProperties,"$route",{enumerable:!0,get:()=>I.unref(c)}),dt&&!Q&&c.value===Ve&&(Q=!0,A(s.location).catch(re=>{}));const B={};for(const re in Ve)Object.defineProperty(B,re,{get:()=>c.value[re],enumerable:!0});w.provide(pr,T),w.provide(Xs,I.shallowReactive(B)),w.provide(Er,c);const U=w.unmount;F.add(w),w.unmount=function(){F.delete(w),F.size<1&&(f=Ve,ne&&ne(),ne=null,c.value=Ve,Q=!1,Y=!1),U()}}};function $(w){return w.reduce((T,B)=>T.then(()=>j(B)),Promise.resolve())}return H}function Kf(e,t){const n=[],r=[],s=[],a=Math.max(t.matched.length,e.matched.length);for(let o=0;o<a;o++){const i=t.matched[o];i&&(e.matched.find(f=>ht(f,i))?r.push(i):n.push(i));const c=e.matched[o];c&&(t.matched.find(f=>ht(f,c))||s.push(c))}return[n,r,s]}const dn=cn(),ro="ROUTE_CHANGE",et={isRegister:!1};let Ir;function ao(e){dn.emitToInstance(ro,et,e),Ir=e}function so(e,t=!0){et.isRegister||(et.isRegister=!0,dn.register(et)),dn.on(ro,e,et),t&&Ir&&e(Ir)}function Jf(){et.isRegister=!1,dn.unregister(et)}var jf=Object.freeze(Object.defineProperty({__proto__:null,emit:ao,listen:so,remove:Jf},Symbol.toStringTag,{value:"Module"}));const yr=Gf({history:rf(),routes:[],scrollBehavior:()=>({top:0})});yr.afterEach(e=>{ao(e)});function Yf(e){e.forEach(t=>{yr.addRoute(t)})}let oo;so(e=>{oo=e},!0);function io(){return{addRoutes:Yf,router:yr,currentRoute:oo,listener:jf}}const ke={refs:new Map,dialogMapper:new Map,dialogMap:new Map,singleDialogMap:new Map},tt=I.reactive({showList:[]});var kt=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n};const Hf=I.defineComponent({name:"MiddleComponent",props:{uuid:{type:String,default:""},name:{type:String,default:""},componentName:{type:String,default:""},isLocal:{type:Boolean,default:!1},componentProps:{type:Object,default:()=>{}}},emits:["createRef","close"],setup(e,t){const n=I.getCurrentInstance(),r=I.ref(!e.isLocal);I.provide("uuid",e.uuid);function s(){var a;n.components||(n.components={}),n.components[e.componentName]=(a=ke.dialogMap.get(e.uuid))==null?void 0:a.componentOptions,r.value=!0}return I.watch(()=>e.isLocal,()=>{var a;r.value=!1,e.isLocal?s():((a=n==null?void 0:n.components)==null||delete a[e.componentName],r.value=!0)}),I.onMounted(()=>{e.isLocal&&(r.value=!1,s())}),{hadInit:r,createRef(a){t.emit("createRef",a)}}}});function Wf(e,t,n,r,s,a){return e.hadInit?(I.openBlock(),I.createBlock(I.resolveDynamicComponent(e.componentName),I.mergeProps({key:0,ref:e.createRef},e.componentProps,{onClose:t[0]||(t[0]=o=>e.$emit("close"))}),null,16)):I.createCommentVNode("",!0)}var Vf=kt(Hf,[["render",Wf]]),Kd="";const qf={class:"dialog-components"},$f=["id"],Zf=["onClick"],Xf={key:0,class:"content"},zf=I.defineComponent({__name:"DialogApp",setup(e){const t={left:"flex-start",top:"flex-start",right:"flex-end",bottom:"flex-end",center:"center"};function n(s,a=!1){var i,c;const o=Rt();if(a)if(s.opts.maskClose)(i=o.getInstance(s.uuid))==null||i.emit("mask-close");else return;(c=o.getInstance(s.uuid))==null||c.close()}function r(s,a){const o=Rt().getInstance(a.uuid);o&&(o.el=s)}return(s,a)=>(I.openBlock(),I.createElementBlock("div",qf,[I.createVNode(I.TransitionGroup,{name:"fade",appear:""},{default:I.withCtx(()=>[(I.openBlock(!0),I.createElementBlock(I.Fragment,null,I.renderList(I.unref(tt).showList,o=>(I.openBlock(),I.createElementBlock("div",{id:`dialog-${o.name}`,key:`dialog-${o.name}`,class:"dialog-box"},[I.createVNode(I.Transition,{name:o.opts.maskAnimName,apper:""},{default:I.withCtx(()=>[o.isShowing?(I.openBlock(),I.createElementBlock("div",{key:0,class:"mask",style:I.normalizeStyle({backgroundColor:o.opts.maskBgColor})},null,4)):I.createCommentVNode("",!0)]),_:2},1032,["name"]),I.createElementVNode("div",{class:"dialog-content",style:I.normalizeStyle({justifyContent:t[o.opts.alignX||"center"]||"center",alignItems:t[o.opts.alignY||"center"]||"center"})},[I.createElementVNode("div",{class:"click-mask",onClick:i=>n(o,!0)},null,8,Zf),I.createVNode(I.Transition,{name:o.opts.animName,appear:""},{default:I.withCtx(()=>[o.isShowing?(I.openBlock(),I.createElementBlock("div",Xf,[I.createVNode(Vf,{uuid:o.uuid,"is-local":o.isLocal,"component-props":o.props,name:o.name,"component-name":o.componentName,onCreateRef:i=>r(i,o),onClose:i=>n(o)},null,8,["uuid","is-local","component-props","name","component-name","onCreateRef","onClose"])])):I.createCommentVNode("",!0)]),_:2},1032,["name"])],4)],8,$f))),128))]),_:1})]))}});var ed=kt(zf,[["__scopeId","data-v-f87c4512"]]),Jd="";const td=["src"],nd=I.defineComponent({__name:"PhotoPreview",props:{imgUrl:{type:String,default:""}},emits:["close"],setup(e){return(t,n)=>(I.openBlock(),I.createElementBlock("img",{src:e.imgUrl,class:"preview-image",onClick:n[0]||(n[0]=r=>t.$emit("close"))},null,8,td))}});var rd=kt(nd,[["__scopeId","data-v-6f03d742"]]);const ad=[];function sd(e){e.component("PhotoPreview",rd)}const vt=cn();class lo{constructor(t,n,r,s){var o,i,c,f,l;this.state=I.reactive({uuid:"",name:"",componentName:"",props:{},opts:{maskClose:!0,animName:"bounce",maskAnimName:"fade",maskBgColor:"rgba(0, 0, 0, 0.8)"},isLocal:!1,isShowing:!1}),this.componentOptions=null,this.el=null,this.parent=null,this.cacheOpts={},this.componentRegisterOpts={},this.isSingle=!1,this.beforeCloseFn=async()=>!1,vt.register(this),this.isSingle=t,this.state.uuid=`Dialog-${new Date().getTime()}-${Math.floor(Math.random()*1e6)}`,this.state.name=r,this.state.componentName=r,Object.assign(this.cacheOpts,s),this.parent=n;const a=((i=(o=this.parent)==null?void 0:o.localDialog)==null?void 0:i[this.state.name])||((l=(f=(c=this.parent)==null?void 0:c.type)==null?void 0:f.useDialogs)==null?void 0:l[this.state.name]);if(a){if(this.state.isLocal=!0,!a.component){this.componentRegisterOpts=a.dialogOptions||{},this.componentOptions=a;return}this.state.componentName=a.componentName||this.state.name,this.componentRegisterOpts=a.opts||a.component.dialogOptions||{},Object.assign(this.state.opts,this.componentRegisterOpts,this.cacheOpts,s),this.componentOptions=a.component}else{const m=ke.dialogMapper.get(this.state.name);this.state.props.dialogData=m,this.state.componentName=(m==null?void 0:m.name)||(m==null?void 0:m.componentName)||this.state.name,this.componentRegisterOpts=(m==null?void 0:m.opts)||{},Object.assign(this.state.opts,this.componentRegisterOpts,this.cacheOpts,s)}}async show(t,n){if(Object.assign(this.state.props,t),Object.assign(this.state.opts,this.componentRegisterOpts,this.cacheOpts,n),!this.state.isShowing)return this.isSingle?ke.singleDialogMap.set(this.state.name,this):ke.dialogMap.set(this.state.uuid,this),this.state.isShowing=!0,tt.showList.push(this.state),new Promise(r=>{setTimeout(r,500)})}async close(){if(!await this.beforeCloseFn())return this.state.isShowing=!1,new Promise(n=>{setTimeout(()=>{const r=tt.showList.findIndex(s=>s.uuid===this.state.uuid);r>=0&&tt.showList.splice(r,1),vt.unregister(this),this.isSingle?ke.singleDialogMap.delete(this.state.name):ke.dialogMap.delete(this.state.uuid),setTimeout(n,500)},0)})}beforeClose(t){this.beforeCloseFn=t}on(t,n){return vt.on(t,n,this),this}off(t,n){vt.off(t,n,this)}emit(t,...n){vt.emitToInstance(t,this,...n)}}const _r=I.createApp(ed);_r.mount("#app-dialog"),sd(_r);function co(e){ke.dialogMapper.set(e.dialogName||e.name,e)}ad.forEach(co);function Rt(e){const t=I.getCurrentInstance();return t&&(t.localDialog=e),{registerDialog:co,app:_r,state:tt,rawState:ke,get:(n,r)=>new lo(!1,t,n,r),getSingle:(n,r)=>ke.singleDialogMap.get(n)||new lo(!0,t,n,r),getInstance:n=>ke.dialogMap.get(n),closeAll:()=>{var n,r;for(;tt.showList.length;){const s=tt.showList.pop();(n=ke.dialogMap.get(s.uuid))==null||n.close(),(r=ke.singleDialogMap.get(s.name))==null||r.close()}}}}const ye=I.reactive({show:!1,tip:"",icon:"",secondTip:"",wordWrap:!0,type:"center",extStyle:{width:""}});var jd="",Yd="";const od={key:0,class:"toast"},id=["src"],ld={class:"toast-first-tip"},cd={key:1,class:"toast-second-tip"},ud=I.defineComponent({__name:"ToastApp",setup(e){return(t,n)=>I.unref(ye).show?(I.openBlock(),I.createElementBlock("div",od,[I.createElementVNode("div",{class:I.normalizeClass(["lx-toast",`lx-toast-${I.unref(ye).type}`,I.unref(ye).wordWrap?"lx-word-wrap":""]),style:I.normalizeStyle(I.unref(ye).extStyle)},[I.unref(ye).icon?(I.openBlock(),I.createElementBlock("img",{key:0,class:"toast-icon",src:I.unref(ye).icon},null,8,id)):I.createCommentVNode("",!0),I.createElementVNode("div",ld,I.toDisplayString(I.unref(ye).tip),1),I.unref(ye).secondTip?(I.openBlock(),I.createElementBlock("div",cd,I.toDisplayString(I.unref(ye).secondTip),1)):I.createCommentVNode("",!0)],6)])):I.createCommentVNode("",!0)}});var fd=kt(ud,[["__scopeId","data-v-f0f539c4"]]);const uo=I.createApp(fd);uo.mount("#app-toast");const dd={type:"center",duration:2500,wordWrap:!0,width:"auto"};let Bt=-1;function xe(e,t={},n="",r=""){let s={};Object.assign(s,dd,t||{}),Bt!==-1&&(clearTimeout(Bt),Bt=-1),ye.tip=e,ye.wordWrap=s.wordWrap||!1,ye.type=s.type||"center",ye.icon=n,ye.secondTip=r,ye.extStyle.width=s.width||"",ye.show=!0,Bt=window.setTimeout(()=>{ye.show=!1,Bt=-1},s.duration)}xe.top=(e,t={})=>xe(e,Object.assign(t,{type:"top"})),xe.center=(e,t={})=>xe(e,Object.assign(t,{type:"center"})),xe.bottom=(e,t={})=>xe(e,Object.assign(t,{type:"bottom"})),xe.withIcon=(e,t,n,r={})=>xe(t,Object.assign(r,{}),e,n);function hd(){return{app:uo,toast:xe}}var Ad="data:image/gif;base64,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",gd="data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAqCAYAAAD1T9h6AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAASUSURBVGhD7Vf5b1RlFPV/cxcVF9wiJiauoRVaWlqxAUmAVotAQeMPiEBNSqmaiKYJFNSIG1pkmbWdpe1MZ9rpdJvOvs/xnjszBglN573BTEzmJF/y8t733nfPvecu7z78z9Ek0Gg0CTQaTQKNRt0ESsUSCvkSkrE8Zt1J/DK6jO9OhzDycVDX+ZPzem9uJqV78rKX79wrmCZQKpVQFEMyqQJ8k0l80e/Hnq1OdD9rR+dTNux8orw6NtvQJff2bp3A6V4/pieSSCcLSprfqBemCRQLZa//8PUiel50YMejVrRvsmLPy04cfMOF/lY3PtzmxoHXXeh5yYm2TTbdw+ux4TBWFrJColj5mnnURWAplMWJfTPicTv63nbjq0+CuPZjBF5bAiF/GsGplF5fHVvBuWMB9Le40f64TUkMHw0gHMggnyvWFQlDBHhQdVE+qUQBN65EcGFoAa5bcZXT3YzhPe71WOI4ddCv0mp7zIqR40HEVnP/fNMMDBGgbmkkvcYIcGXSBZVSLlu+tx4BPstmiggHMxg6ElASzJdL58J1JXZNBNRDckAsksPlkTCuXlxBMl7QKBiBEpF3gtMpHO+akrywovctF2a9KXWKGdRMgAdcF7nsfsGh1ebWr2uGD1VHyGIkrv8U0dzpesaOi2fDSIlDzKAmApQOQ0+vtTxg0WpyqNWDeUlUM54riJxikTwOt3nR8aQNJ/f7sDiXqTw1hg0JUDrU/fBAWbfb7rfoYllko6KszIA5Mzq4oFGgjLy2eOWJMWxIgAc5xmN6UMuDZeO53nnYgr2vTKgUzICRowzZ5FhWHePRyhNj2JAAD7r58xo+P+DDR9s92P6IVSU00OHFmQ/8cP4Vq+ysHUvzGVh+W8MlKQiM6q6n7Tp+WH+PYlGkSonVippyoIrJGzHsft6B7i12vTajf+IPaWzvPuf4V0S52OTY9LLp2r9riIDHGse+VyfUY1e+XVZ5mQE7MDs4m1nVeEb16E4vVsMyYvxXEeB48GnPtA5oJ96fQVqS22gvIFhGOWIwCq0PCQGpbO9JeaaDjDrFEAFWnG8+m9eE7txsx7QzYSoKrGwkwaGOfYCRuDAkvUDGDaMOMUSAhzquxbD/tUmZPG0yQs9iWQY6GsQGZQQcLSJLOZ2HTkmB4OBH441+xxABHsBZnvWfCde9xaFe5FhB3VY77Z2o3uf7bIpVQ0libTmn0jSbT4YIVI0IB9KacAw9tUsS0ZWcVqX1COTFwEQ0D9fNuBrMvfzW7aTMwBABgsZQSj75s+qTDsp84DgwKD3B/mdUKwy9ylEhLgZH5JpjCBsV/8iYuGf6/FiWHxqzRt8OUwS4GPIpe0IbHJsRqwlldaTdi7Pys3Jekn10MKTXbHqsXNzDvRxLWC4poXphmEAV9B4jMTeTxuUvF3F4h0crilaouyw+o+zGZP7nD77KrRERuBMkwmEv5EvDIrMNE/zYrin0vunSNdDpld/JIMa/X1Xtm+0d66FuAiopMYgeZURIhpWKNZ2L1zSaz6qJy3fuFeom0Gg0CTQaTQKNRpNAo9Ek0Gg0CTQWwN+AsXRU7ujH9gAAAABJRU5ErkJggg==";const gt=I.reactive({show:!1});var Hd="",Wd="";const fo=e=>(I.pushScopeId("data-v-9e44b02e"),e=e(),I.popScopeId(),e),md={key:0,class:"lx-load-mark"},pd=fo(()=>I.createElementVNode("img",{src:Ad,alt:""},null,-1)),Ed=fo(()=>I.createElementVNode("img",{src:gd,alt:""},null,-1)),Cd=I.defineComponent({__name:"LoadingApp",setup(e){let{lang:t}=an(),n=I.ref(!1);function r(){location.reload()}let s={tc:"\u5237\u65B0",sc:"\u5237\u65B0",en:"Refresh"},a=null;return I.watch(()=>gt.show,(o,i)=>{gt.show?a=setTimeout(()=>{gt.show?n.value=!0:clearTimeout(a)},1e4):(a&&clearTimeout(a),n.value=!1)}),(o,i)=>I.unref(gt).show?(I.openBlock(),I.createElementBlock("div",md,[pd,I.unref(n)?(I.openBlock(),I.createElementBlock("div",{key:0,class:"reload",onClick:r},[Ed,I.createTextVNode(I.toDisplayString(I.unref(s)[I.unref(t)]),1)])):I.createCommentVNode("",!0)])):I.createCommentVNode("",!0)}});var Id=kt(Cd,[["__scopeId","data-v-9e44b02e"]]);const ho=I.createApp(Id);ho.mount("#app-loading");function Tt(e){if(e==="close"){gt.show=!1;return}gt.show=!0}Tt.open=()=>Tt("open"),Tt.close=()=>Tt("close");function yd(){return{app:ho,loading:Tt}}class _d extends Ot{}var wr=new _d;const{state:wd,lang:bd,format:Sd}=an();function Ld(e){e.config.globalProperties.$lang=wd,e.config.globalProperties.$langValue=bd,e.config.globalProperties.$langFormat=Sd}const{router:Md}=io();function Dd(e){e.use(Md)}const{app:br}=Rt();class kd extends Ot{use(t,...n){return br.use(t,n),this}directive(t,n){return br.directive(t,n),this}component(t,n){return br.component(t,n),this}}var Sr=new kd;class vd extends Ot{use(t,...n){return Sr.use(t,n),this}directive(t,n){return Sr.directive(t,n),this}component(t,n){return Sr.component(t,n),this}}var Ao=new vd;const{app:go}=Rt();class Rd extends Ot{constructor(t){super(),this.app=t;const n=Object.assign({},this.app.config.globalProperties,go.config.globalProperties);Object.assign(this.app.config.globalProperties,n),go.config.globalProperties=this.app.config.globalProperties,Ld(t),Dd(t)}use(t,...n){return this.app.use(t,n),wr.use(t,n),this}directive(t,n){return this.app.directive(t,n),wr.directive(t,n),Ao.directive(t,n),this}component(t,n){return this.app.component(t,n),wr.component(t,n),Ao.component(t,n),this}}function Bd(e){return new Rd(e)}(function __injectStyle__(e){var n;if(e&&"undefined"!=typeof window)return(n=document.createElement("style")).setAttribute("media","screen"),n.innerHTML=e,document.head.appendChild(n),e})(".lx-load-mark[data-v-9e44b02e]{position:fixed;left:0;top:0;width:100vw;height:100vh;z-index:9999999999;display:flex;justify-content:center;align-items:center;background:rgba(0,0,0,.7)}.lx-load-mark>.loading-img[data-v-9e44b02e]{width:2rem}#app-loading{position:fixed;z-index:5001}.reload{display:flex;position:fixed;bottom:2rem;right:1rem;background-color:#fff;padding:.25rem .5rem;border-radius:.1rem;font-size:.4rem;color:#6a43d1;align-items:center;justify-content:space-around}.reload img{width:40%}#app-toast{position:fixed;z-index:99999999999999999}.toast[data-v-f0f539c4]{display:flex;flex-direction:column;justify-content:space-around;align-items:center}.lx-toast[data-v-f0f539c4]{position:fixed;bottom:1rem;left:50%;max-width:90%;height:.4rem;line-height:.5rem;padding:.2rem .2rem;transform:translateX(-50%);text-align:center;font-size:.45rem;color:#fff;border-radius:.05rem;background:rgba(0,0,0,.8);animation:show-toast-f0f539c4 .5s;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;z-index:9999999999;display:flex;flex-direction:column;justify-content:center;align-items:center}.lx-toast .toast-icon[data-v-f0f539c4]{width:.5rem;margin:.23rem 0}.lx-toast .toast-first-tip[data-v-f0f539c4]{word-wrap:break-word;word-break:break-word}.lx-toast .toast-second-tip[data-v-f0f539c4]{opacity:.6;margin:.07rem 0}.lx-toast.lx-word-wrap[data-v-f0f539c4]{white-space:inherit;word-break:break-all;height:auto}.lx-toast.lx-toast-top[data-v-f0f539c4]{top:50px;bottom:inherit}.lx-toast.lx-toast-center[data-v-f0f539c4]{top:50%;margin-top:-.2rem;bottom:inherit}@-webkit-keyframes show-toast-f0f539c4{from{opacity:0}to{opacity:1}}@keyframes show-toast-f0f539c4{from{opacity:0}to{opacity:1}}.dialog-components[data-v-f87c4512]{position:fixed;top:0;left:0;z-index:5000}.dialog-components .dialog-box[data-v-f87c4512]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:5000}.dialog-components .dialog-box.closing[data-v-f87c4512]{pointer-events:none}.dialog-components .dialog-box>.mask[data-v-f87c4512]{position:absolute;z-index:1;top:0;left:0;width:100%;height:100%;background-color:var(--color-mask-bg)}.dialog-components .dialog-box>.dialog-content[data-v-f87c4512]{display:flex;justify-content:center;align-items:center;width:100%;height:100%}.dialog-components .dialog-box>.dialog-content>.click-mask[data-v-f87c4512]{position:absolute;z-index:1;top:0;left:0;width:100%;height:100%}.dialog-components .dialog-box>.dialog-content>.content[data-v-f87c4512]{z-index:1;position:relative}.fade-enter[data-v-f87c4512],.fade-leave-to[data-v-f87c4512]{opacity:0}.fade-enter-active[data-v-f87c4512],.fade-leave-active[data-v-f87c4512]{transition:all .5s}.bounce-enter-active[data-v-f87c4512]{animation:bounceIn-f87c4512 .5s}.bounce-leave-active[data-v-f87c4512]{animation:bounceOut-f87c4512 .5s}.fade-right-enter-active[data-v-f87c4512]{animation:fadeInRight-f87c4512 .5s}.fade-right-leave-active[data-v-f87c4512]{animation:fadeOutRight-f87c4512 .5s}.fade-down-enter-active[data-v-f87c4512]{animation:fadeInUp-f87c4512 .5s}.fade-down-leave-active[data-v-f87c4512]{animation:fadeOutDown-f87c4512 .5s}@keyframes bounceIn-f87c4512{0%,20%,40%,60%,80%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:scale3d(.3,.3,.3)}20%{transform:scale3d(1.1,1.1,1.1)}40%{transform:scale3d(.9,.9,.9)}60%{opacity:1;transform:scale3d(1.03,1.03,1.03)}80%{transform:scale3d(.97,.97,.97)}to{opacity:1;transform:scaleX(1)}}@keyframes bounceOut-f87c4512{20%{transform:scale3d(.9,.9,.9)}50%,55%{opacity:1;transform:scale3d(1.1,1.1,1.1)}to{opacity:0;transform:scale3d(.3,.3,.3)}}@keyframes fadeInRight-f87c4512{0%{opacity:0;transform:translate3d(100%,0,0)}to{opacity:1;transform:translateZ(0)}}@keyframes fadeOutRight-f87c4512{0%{opacity:1}to{opacity:0;transform:translate3d(100%,0,0)}}@keyframes fadeOutDown-f87c4512{0%{opacity:1}to{opacity:0;transform:translate3d(0,100%,0)}}@keyframes fadeInUp-f87c4512{0%{opacity:0;transform:translate3d(0,100%,0)}to{opacity:1;transform:translateZ(0)}}.preview-image[data-v-6f03d742]{min-width:40vw;min-height:40vw;max-width:60vw;max-height:60vh}"),ue.EventBusKey=on,ue.MobileType=ks,ue.RuntimeEnv=Ds,ue.RuntimeMode=Ms,ue.install=Bd,ue.useApi=Eu,ue.useDayjs=sa,ue.useDialog=Rt,ue.useEnvConfig=Ft,ue.useEventBus=cn,ue.useEventListener=ws,ue.useJwt=oa,ue.useLang=an,ue.useLoader=Ru,ue.useLoading=yd,ue.usePageVisibility=Bu,ue.useRouter=io,ue.useRuntimeEnv=Pu,ue.useStorage=na,ue.useToast=hd,ue.useUtils=Wt,ue.useWindow=ps,Object.defineProperties(ue,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
