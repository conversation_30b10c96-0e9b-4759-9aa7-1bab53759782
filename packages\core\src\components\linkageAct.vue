<template>
  <div class="activity">
      
      <p class="title">{{state.联动奖赏.绿色权益}}</p>
        <div class="pic">
            <p class="lt">{{state.联动奖赏.每月7号}}</p>
            <div class="picitem l">
                <img class="bg" :src="$imgs['package/backgroundlit.png']" alt="">
                <img class="iconcc" :src="$imgs['碳值20.png']" alt="">
                <img class="bling1" :src="$imgs['bling.png']" alt="">
                <img class="bling2" :src="$imgs['bling.png']" alt="">
                <div class="p">
                    <img :src="$imgs['vip1.png']" alt="">
                    {{state.联动奖赏.金会员}}
                </div>
            </div>
            <div class="picitem r">
                <div class="bgc">
                    <img class="bling3" :src="$imgs['bling.png']" alt="">
                    <img class="bling4" :src="$imgs['bling.png']" alt="">
                    <img class="iconcc ll" :src="$imgs['碳值30.png']" alt="">
                    <img class="iconcc rr" :src="$imgs['Double_step.png']" alt="">
                    <img class="bg" :src="$imgs['package/backgroundlit.png']" alt="">
                    <span>+</span>
                    <img class="bg" :src="$imgs['package/backgroundlit.png']" alt="">
                </div>
                
                <div class="p">
                    <img :src="$imgs['vip2.png']" alt="">
                    {{state.联动奖赏.白金会员}}
                    <img class="ss" :src="$imgs['vip3.png']" alt="">
                    {{state.联动奖赏.优越会会员}}
                </div>
            </div>
        </div>

        <div class="pic">
            <p class="lt">{{state.联动奖赏.种成奖励上线}}</p>
            <img class="vipss" :src="$imgs['vipss.png']" alt="">
        </div>

      <p class="text">{{state.联动奖赏.带来全新奖赏福利}}</p>
      <p class="torule" @click="toRule()">{{state.home.详细规则}} ></p>
  </div>
</template>

<script setup>
import { useDialog, useLang } from 'hook';
import { defineComponent, onBeforeMount, onMounted, inject, ref, watch, getCurrentInstance } from 'vue'
const { state } = useLang()
const uuid = inject('uuid')
let emit = defineEmits(['toRule'])

const toRule = () => {
    emit('toRule')
}

</script>

<style lang='less' scoped>
.activity{
    width: 542px;
    min-height: 640px;
    background: #EAF5EF;
    border-radius: 32px 32px 32px 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding-bottom: 34px;
    .pic{
        flex-shrink: 0;
        background: #F9FFF8;
        border-radius: 16px 16px 16px 16px;
        padding-top: 58px;
        padding-bottom: 24px;
        width: 480px;
        display: flex;
        justify-content: space-between;
        position: relative;
        margin-top: 24px;
        .lt{
            position: absolute;
            padding: 0 20px;
            background: linear-gradient(180deg, #C18B6F 0%, #885A42 100%);
            border-radius: 16px 0px 16px 0px;
            white-space: nowrap;
            font-size: 22px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
            line-height: 28px;
            top: 0;
            left: 0;
        }
        .vipss{
            // width: 256px;
            margin-left: 12px;
            height: 84px;
        }
    }
    .picitem{
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        .bling1{
            position: absolute;
            width: 33px;
            top: -8px;
            left: 0px;
        }
        .bling2{
            position: absolute;
            width: 24px;
            top: 10px;
            right: 0px;
        }
        .bling3{
            position: absolute;
            width: 31px;
            top: -8px;
            left: -4px;
        }
        .bling4{
            position: absolute;
            width: 19px;
            top: -5px;
            right: 0px;
        }
        .iconcc{
            position: absolute;
            width: 68px;
            left: 50%;
            transform: translate(-50%, 0);
            top: 24px;
            &.ll{
                transform: none;
                left: 12px;
            }
            &.rr{
                transform: none;
                left: 135px;
                // right: 12px;
            }
        }
        &.l{
            margin-left: 30px;
        }
        &.r{
            margin-right: 10px;
        }
        .bgc{
            align-items: center;
            display: flex;
            position: relative;
            span{
                margin: 0 8px;
                font-size: 24px;
                font-family: PingFang SC-Semibold, PingFang SC;
                font-weight: 600;
                color: #4ECA54;
            }
        }
        .bg{
            width: 91px;
            height: 120px;
        }
        >.p{
            white-space: nowrap;
            margin-top: 12px;
            display: flex;
            align-items: center;
            padding: 5px 16px;
           background: #D9EFE5;
            border-radius: 24px 24px 24px 24px; 
            font-size: 18px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #4E5B7E;
            >img{
                margin-right: 8px;
                transform: translate(0, 3px);
                width: 18px;
                // height: 20px;
            }
            >.ss{
                
                margin-left: 16px;
            }
        }
    }
    >.title{
        padding-top: 48px;
        font-size: 32px;
        font-family: PingFang HK-Semibold, PingFang HK;
        font-weight: 600;
        color: #4E5B7E;
        text-align: center;
    }
    >.time{
        width: 100%;
        text-align: center;
        white-space: pre-line;
        padding-top: 10px;
        font-size: 24px;
        font-family: PingFang HK-Regular, PingFang HK;
        font-weight: 400;
        color: #A7AEC3;
    }
    >img{
        height: 170px;
        padding-top: 24px;
        box-sizing: content-box;
    }
    >.tip{
        max-width: 90%;
        text-align: center;
        padding-top: 32px;
        font-size: 28px;
        font-family: PingFang HK-Medium, PingFang HK;
        font-weight: 550;
        color: #4E5B7E;
        line-height: 32px;
        span{
            color: #FB7655;
            font-size: 36px;
        }
    }
    >.text{
        flex: 1;
        display: flex;
        align-items: center;
        padding-top: 18px;
        padding-bottom: 18px;
        max-width: 90%;
        font-size: 24px;
        font-family: PingFang HK-Regular, PingFang HK;
        font-weight: 400;
        color: #6A6A6A;
        line-height: 36px;
    }
    >.torule{
        // position: absolute;
        // left: 50%;
        // transform: translate(-50%, 0);
        // bottom: 34px;
        display: flex;
        align-items: flex-end;
        font-size: 24px;
        font-family: PingFang HK-Regular, PingFang HK;
        font-weight: 400;
        color: #38BA42;
        line-height: 28px;
    }
}
</style>