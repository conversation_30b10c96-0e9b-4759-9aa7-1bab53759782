.contain {
    min-height: 100vh;
    width: 100vw;
    height: 100vh;
    background: url('@assets/imgs/reductionRecord/background.png') no-repeat;
    background-size: 100%;
    padding-top: 356px;

    .userHead {
        position: absolute;
        left: 50px;
        top: 226px;
    }

    .list {
        background: #F2FBF7;
        box-shadow: inset 0px 6px 0px 2px rgba(255, 255, 255, 0.85);
        border-radius: 48px 48px 0px 0px;
        width: 100%;
        padding-bottom: 40px;
        min-height: 100%;
        // display: flex;
        // flex-wrap: wrap;
        // justify-content: space-around;
        display: grid;
        grid-template-columns: 1fr 1fr;
        > * {
            margin: 40px auto 0;
        }
    }
}
