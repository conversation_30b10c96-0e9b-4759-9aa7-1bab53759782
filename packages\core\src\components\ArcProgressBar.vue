<script lang="tsx">
import { defineComponent, onMounted, ref, watch, getCurrentInstance,PropType, ComponentInternalInstance } from 'vue'
import { useSwiper, Swiper, SwiperSlide } from 'swiper/vue'
import type { Swiper as SwiperClass } from 'swiper/types'
import { useLoading, useRouter, useDialog, useLang, useStorage, useEnvConfig, useToast } from 'hook'
import 'swiper/css'
import { useUserStore } from '@/store'
export default defineComponent({
    name: 'ArcProgressBar',
    components: {
        Swiper,
        SwiperSlide
    },
    props: {
        level: { default: 0 },
        progress: { default: 0 },
        rewardConfigs: { default: ()=>[],type:Array as PropType<Record<string,any>[]> },
        treeCode: { default:'' },
        propList: { default: {} },
        tree_energy: { default: 0 },
        tree_total_energy: { default: 0 },
        demand: { default: ()=>[],type:Array as PropType<number[]>}
    },
    emits: ['showProp', 'toRouter', 'showAchieve'],
    setup(props, context){
        const cns = getCurrentInstance() as ComponentInternalInstance
        const img = cns.appContext.config.globalProperties.$imgs
        const { state } = useLang()
        const arr = ref([
            '種子·一期',
            '種子·二期',
            '發芽期',
            '成苗一期',
            '成苗二期',
            '發育一期',
            '發育二期',
            '成熟一期',
            '成熟二期'
        ])
        const chunks = ref(0) //弧边的分片值
        const pointAngleList = ref<number[]>([]) //弧边上各个点的坐标集合
        const level = ref(0) //当前等级
        level.value = props.level
        const prevAngle = ref(180) //上次选择的角度
        const progress = ref(props.progress) //当前等级的进度
        let swiperIsInit = false
        let move = (rotateAngle?: number, progress?: number) => {}
        let Robj = {}
        let useSwiper:any = null

        const onSwiper = (swiper) => {
            useSwiper = swiper
        };


        onMounted(()=>{
            let R = {
                'img1' : 'icon.png',
                'img2' : 'LV.png',
                'img3' : 'icon2.png',
            }
            let already = 0
            for(let k in R){
                Robj[k] = new Image()
                Robj[k].src = new URL(img[R[k]], import.meta.url).href
                Robj[k].onload = () => {
                    already ++ 
                    if(already == Object.keys(R).length){
                        move = draw(Robj)
                    }
                }
            }
        })
        // TODO 进度条变动
        watch(pointAngleList,()=>{
            level.value = props.level
            prevAngle.value = pointAngleList.value[level.value-1]
            move(prevAngle.value, pointAngleList.value[level.value-1] -  chunks.value * progress.value)
        },{
            deep:true
        })
        watch(props,()=>{
            level.value = props.level
            progress.value = props.progress
            move(prevAngle.value, pointAngleList.value[level.value-1] -  chunks.value * progress.value)
            useSwiper && useSwiper.slideTo(props.level - 1)
        },{
            deep:true
        })

        const draw = (Robj) => {
            //等比换算
            const rr = (document.getElementsByTagName('html')[0].offsetWidth >= 375 ? 375 : document.getElementsByTagName('html')[0].offsetWidth) * 100 / 750 / 100
            // 设置canvas外部容器大小
            const rect = document.querySelector('.main')?.getBoundingClientRect() as DOMRect
            const { width } = rect
            let boxSize = width * 2.5
            let canvasBox = document.getElementById('canvas-box') as HTMLDivElement
            canvasBox.style.width = boxSize + 'px'
            canvasBox.style.height = boxSize + 'px'
            canvasBox.style.left = - (boxSize - width) / 2 + 'px'

            const arcProgressBar = document.querySelector('.box .arc-progress-bar') as HTMLDivElement
            arcProgressBar.style.height = 200 * rr + 'px'
            canvasBox.style.top = -(boxSize - arcProgressBar.clientHeight) + 'px'
            
            const canvas = document.getElementById('my-canvas') as HTMLCanvasElement
            const ctx = canvas.getContext('2d') as CanvasRenderingContext2D
            // 设置canvas css的大小
            canvas.style.width = boxSize + 'px'
            canvas.style.height = boxSize + 'px'

            // 求弧度
            function angleToRadian( angle:number ) {
                return Math.PI / 180 * angle;
            }
            // 获取屏幕是几倍屏
            let getPixelRatio = function(context:any) {
                let backingStore = context.backingStorePixelRatio ||
                    context.webkitBackingStorePixelRatio ||
                    context.mozBackingStorePixelRatio ||
                    context.msBackingStorePixelRatio ||
                    context.oBackingStorePixelRatio ||
                    context.backingStorePixelRatio || 1;
                return (window.devicePixelRatio || 1) / backingStore;
            }

            // 设置画布值
            const pixelRatio = getPixelRatio(canvas)
            canvas.width = boxSize * pixelRatio
            canvas.height = boxSize * pixelRatio
            const x = boxSize/2 * pixelRatio
            const y = (boxSize/2 - 65 * rr) * pixelRatio
            const r = boxSize/2 * pixelRatio

            // 计算每一段之间的占比
            chunks.value = Math.ceil(
                ( 16 * state.plantStage.length > 300 ? 300 : 16 * state.plantStage.length ) / state.plantStage.length
                )
            // 从180度开始逆时针均匀分布每一点的角度
            state.plantStage.forEach((_,index)=>{
                let pointAngle = 180 - index * chunks.value
                pointAngleList.value.push(pointAngle)
            })
            // console.log('每一份'+chunks.value,'各个点的角度:', pointAngleList.value)

            // 运动方法
            const move = (rotateAngle = 180, progress = 0) => {
                ctx.clearRect(0,0,canvas.width,canvas.height)
                ctx.save()
                // 设置旋转点
                ctx.translate(x, y)
                //主要是控制这里整体旋转
                ctx.rotate( angleToRadian(-90 + (180-rotateAngle)) )
                ctx.translate(-x, -y)

                // 绘制默认弧线
                ctx.beginPath()
                ctx.lineWidth = 20 * rr * pixelRatio
                ctx.strokeStyle = '#47b26c'
                ctx.arc(x, y, r, angleToRadian(180), angleToRadian(pointAngleList.value[pointAngleList.value.length-1]), true)
                ctx.stroke()

                // 绘制弧边上的默认圆点
                pointAngleList.value.forEach((point)=>{
                    let rx = x + r * Math.cos( angleToRadian( point ) )
                    let ry = y + r * Math.sin( angleToRadian( point ) )
                    ctx.save()
                    ctx.beginPath()
                    // 设置旋转点
                    ctx.translate(rx,ry)
                    ctx.rotate( angleToRadian(-90 + point) )
                    ctx.translate(-rx,-ry)
                    // 画圆点
                    ctx.fillStyle = '#47b26c'
                    ctx.arc( rx, ry, 18 * rr * pixelRatio, 0, angleToRadian(360) )
                    ctx.fill()
                    ctx.restore()
                })

                // 绘制高亮弧线
                ctx.beginPath()
                ctx.lineWidth = 14 * rr * pixelRatio
                ctx.strokeStyle = '#fcbb21'
                //控制这里显示进度，从π逆时针开始
                ctx.arc(x, y, r, angleToRadian(180), angleToRadian(progress), true)
                ctx.stroke()

                // 绘图标与文案
                pointAngleList.value.forEach((point,index)=>{
                    let rx = x + r * Math.cos( angleToRadian( point ) )
                    let ry = y + r * Math.sin( angleToRadian( point ) )
                    ctx.save()
                    ctx.beginPath()
                    // 设置旋转点
                    ctx.translate(rx,ry)
                    ctx.rotate( angleToRadian(-90 + point) )
                    ctx.translate(-rx,-ry)
                    // 画图标
                    if(index==0){
                        // 绘制高亮圆点
                        ctx.fillStyle = '#fcbb21'
                        ctx.arc( rx, ry, 14 * rr * pixelRatio, 0, angleToRadian(360) )
                        ctx.fill()
                        ctx.drawImage(Robj['img1'], rx - 10 * rr * pixelRatio, ry - 10 * rr * pixelRatio, 20 * rr * pixelRatio, 20 * rr * pixelRatio)
                    }else{
                        if(index == 9 && level.value == 10){
                            // 绘制高亮圆点
                            ctx.fillStyle = '#fcbb21'
                            ctx.arc( rx, ry, 14 * rr * pixelRatio, 0, angleToRadian(360) )
                            ctx.fill()
                            ctx.drawImage(Robj['img3'], rx - 10 * rr * pixelRatio, ry - 10 * rr * pixelRatio, 20 * rr * pixelRatio, 20 * rr * pixelRatio)
                        }else if(index < level.value){
                            // 绘制高亮圆点
                            ctx.fillStyle = '#fcbb21'
                            ctx.arc( rx, ry, 14 * rr * pixelRatio, 0, angleToRadian(360) )
                            ctx.fill()
                            ctx.drawImage(Robj['img1'], rx - 10 * rr * pixelRatio, ry - 10 * rr * pixelRatio, 20 * rr * pixelRatio, 20 * rr * pixelRatio)
                        }
                    }
                    // 画文案
                    ctx.beginPath()
                    ctx.fillStyle = 'black'
                    ctx.font=`${18 * rr * pixelRatio}px Arial`
                    if(index == level.value - 1){
                        ctx.drawImage(Robj['img2'], rx - 40 * rr * pixelRatio, ry + 15 * rr * pixelRatio, 90 * rr * pixelRatio, 50 * rr * pixelRatio)
                        ctx.font="bold 30px Arial";
                        ctx.fillStyle = "#fff"
                        if(index == 9 && level.value == 10){
                            ctx.fillText(state.home.种成, rx - 15 * rr * pixelRatio, ry + 50 * rr * pixelRatio)
                        }else{
                            ctx.fillText(`Lv.${index+1}`,rx - 15 * rr * pixelRatio, ry + 50 * rr * pixelRatio)
                        }
                    }else{
                        if(index == 9){
                            ctx.fillText(state.home.种成,rx - 15 * rr * pixelRatio, ry + 50 * rr * pixelRatio)
                        }else{
                            ctx.fillText(`Lv.${index+1}`,rx - 15 * rr * pixelRatio, ry + 50 * rr * pixelRatio)
                        }
                    }
                    ctx.restore()
                })
                ctx.restore()
            }
            move()
            return move
        }

        // swiper切换
        let animationFrameId = 0
        const slideChange = (swiper:SwiperClass) => {
            // 取消动画帧
            let angle = pointAngleList.value[swiper.activeIndex]
            let animationFn = () => {
                // 请求动画帧
                animationFrameId = requestAnimationFrame(animationFn)
                if(angle < prevAngle.value){
                    prevAngle.value -= 2
                    if(prevAngle.value <= angle){
                        cancelAnimationFrame(animationFrameId)
                    }
                }else{
                    prevAngle.value += 2
                    if(prevAngle.value >= angle){
                        cancelAnimationFrame(animationFrameId)
                    }
                }
                move(prevAngle.value, pointAngleList.value[level.value-1] -  chunks.value * progress.value)
            }
            swiperIsInit && animationFn()
        }

        const toLocale = (num:number) => {
            let unit = 'g'
            if(num >= 100000){
                num = num / 1000
                unit = 'kg'
            }
            return num.toLocaleString() + unit
        }
        toLocale(200000)
        
        let startX = 0//开始位置
        let endX = 0//结束触摸的位置
        let disX = 0//移动距离
        const touchstart = (ev)=> {
            ev = ev || event;
            ev.preventDefault();
            if(ev.touches.length == 1) {    //tounches类数组，等于1时表示此时有只有一只手指在触摸屏幕
                startX = ev.touches[0].clientX // 记录开始位置
            }
        }
        const touchend = (ev) => {
            ev = ev || event;
            ev.preventDefault();
            if(ev.changedTouches.length == 1) {    
                let body = document.body.getBoundingClientRect();
                endX = ev.changedTouches[0].clientX;
                disX = endX - startX
                if(disX > 0){
                    if(disX >= (body.width/5)){
                        useSwiper.slidePrev()
                    }
                }else{
                    if(disX <= (body.width/5)){
                        useSwiper.slideNext()
                    }
                }
            }
        }


        return () => (
            <div class="box">
                <div class="arc-progress-bar" 
                    onTouchstart={touchstart}
                    onTouchend={touchend}
                >
                    <div id="canvas-box">
                        <canvas id="my-canvas"></canvas>
                    </div>
                </div>
                <Swiper
                    class="my-swiper"
                    slidesPerView="auto"
                    centeredSlides={true}
                    spaceBetween={20}
                    initialSlide={level.value-1}
                    onInit={()=>{swiperIsInit = true}}
                    onSlideChangeTransitionStart={slideChange}
                    onSwiper={onSwiper} >
                    {/* 减碳值 */}
                    {
                        state.plantStage.map((item, index)=>{
                            return <SwiperSlide class="item">
                                <div class="flag"><span class={index == level.value-1 ? 'icon' :
                                     (index > level.value-1) ? 'icon icon1' : 'icon icon2'
                                }></span>{
                                    (index == level.value-1) ?  state.home.当前阶段 : 
                                    (index > level.value-1) ? state.home.未达成 : state.home.已完成
                                } <span class='line'></span> <p class="status">{item}</p></div>
                                <div class='hp'>
                                    {
                                        (index == level.value-1) ? <p>{state.home.生命值} <span class='num1'>{ props.tree_energy }</span>/<span class='num2'>{ props.demand[index+1] }</span></p> : 
                                        (index > level.value-1) ? <p>{state.home.生命值需达到x.replace('{x}', props.demand[index])}</p> : <p>{state.home.该阶段已完成}</p>
                                    }
                                    
                                </div>
                                <>{
                                  index >= level.value-1 ? <div class="up" onClick={() => {context.emit('toRouter', '/upStrategy') }}><span></span>{ state.home.升级攻略 }</div> : null
                                }</>
                                
                                <div class="badge"><span></span>{ state.home.奖励 }</div>
                                <div class="award" onClick={() => { index<9? context.emit('showProp', props.rewardConfigs[index].reward_items[0]): context.emit('showAchieve', props.rewardConfigs[index-1].vip_reward_items[0])}}>
                                    
                                     <div class="p">
                                        
                                        <div class='img'>
                                            {
                                                !props.propList[ props.rewardConfigs[index].reward_items[0] ] ? <img class='lock' src={img['lock.png']}/> : null
                                            }
                                            <img class={index < 9? 'im' : 'im achieve'} src={
                                            index<9?
                                            img[`props/${state.propsReward[ props.rewardConfigs[index].reward_items[0] ]?.propType}/${props.rewardConfigs[index].reward_items[0]}.png`] :
                                            new URL("../assets/TT/Icon/chuizi.png", import.meta.url).href
                                            } alt=""/> 
                                            {
                                                index < 9 ? null : <p class="mygarden">MyGarden</p>
                                            }
                                        </div>
                                        
                                        { index<9? state.propsReward[ props.rewardConfigs[index].reward_items[0] ]?.propName : 
                                            useUserStore().isVip ? 
                                            <div class='img img1'>
                                                {
                                                    !props.propList[ props.rewardConfigs[index - 1].vip_reward_items[0] ] ? <img class='lock' src={img['lock.png']}/> : null
                                                }
                                                <img class='im achieve' src={
                                                    img[`props/vip/${props.rewardConfigs[index - 1].vip_reward_items[0]}/1.png`] 
                                                } alt=""/> 
                                                <p class="mygarden mygarden2">{state.home.後付限定}</p>
                                            </div> :
                                            state.home.x成就1.replace('{x}', state.plant[props.treeCode]?.plantName)
                                        }
                                        <p class={ props.propList[ props.rewardConfigs[index].reward_items[0] ] ? 'propStatus' : 'propStatus propStatus1'}>
                                            { props.propList[ props.rewardConfigs[index].reward_items[0] ] ? state.home.已获得 : state.home.未获得 }
                                        </p>
                                    </div> 
                                    
                                    <>{
                                        <div class="view" >{ state.home.查看 } <span></span></div> 
                                    }</>
                                </div>
                                
                                
                            </SwiperSlide>
                        })
                    }
                </Swiper>
            </div>
        )
    }
})
</script>
<style lang="less" scoped>
.box{
    width: 100%;
}
.box .arc-progress-bar{
    overflow: hidden;
    margin-bottom: 30px;
    #canvas-box{
        position: relative;
        pointer-events: none;
    }
}
.box .my-swiper{
    .item{
        width: 650px;
        height: 296px;
        background: url('@/assets/TT/treeCard.png') no-repeat;
        background-size: 100% auto;
        position: relative;
        .flag{
            position: absolute;
            left: 25px;
            top: 10px;
            font-size: 24px;
            font-family: PingFang TC-Semibold, PingFang TC;
            font-weight: 600;
            color: #16741E;
            width: 100%;
            .icon{
                display: inline-block;
                width: 36px;
                height: 36px;
                background: url('@/assets/TT/Icon/flag.png') no-repeat;
                background-size: 100% auto;
                vertical-align: middle;
                margin-left: 15px;
                margin-right: 6px;
                &.icon1{
                    background: url('@/assets/TT/Icon/flag2.png') no-repeat;
                    background-size: 100% auto;
                }
                &.icon2{
                    background: url('@/assets/TT/Icon/flag1.png') no-repeat;
                    background-size: 100% auto;
                }
            }
            .line{
                display: inline-block;
                width: 1px;
                height: 28px;
                vertical-align: middle;
                border-right: 3px solid #16741E;
                transform: translate(0, -2px);
            }
            .status{
                display: inline-block;
                font-size: 32px;
                font-family: PingFang TC-Semibold, PingFang TC;
                font-weight: 600;
                color: #FFFFFF;
            }
        }
        .hp{
            position: absolute;
            left: 40px;
            top: 76px;
            font-size: 28px;
            font-weight: 600;
            color: #4E5B7E;
            line-height: 40px;
            .num1{
                display: inline-block;
                color: #FB7655;
                font-size: 36px;
            }
            .num2{
                display: inline-block;
                color: #4E5B7E;
                font-size: 26px;
            }
        }
        .up{
            position: absolute;
            right: 40px;
            top: 70px;
            background: linear-gradient(360deg, #3CBF48 0%, #3DD13F 100%);
            box-shadow: 0px 4px 0px 2px rgba(56,178,60,1), inset 0px 2px 0px 2px rgba(146,232,152,1);
            border-radius: 16px 16px 16px 16px;
            padding: 10px 16px;
            font-size: 24px;
            font-family: PingFang TC-Semibold, PingFang TC;
            font-weight: 600;
            color: #FFFFFF;
            span{
                display: inline-block;
                width: 26px;
                height: 20px;
                background: url('@/assets/TT/Icon/up.png') no-repeat;
                background-size: 100% auto;
                vertical-align: middle;
                margin-right: 8px;
            }
        }
        .badge{
            position: absolute;
            left: 5px;
            bottom: 102px;
            background: linear-gradient(180deg, #3CBF48 0%, #3DD13F 100%);
            box-shadow: inset 0px 0px 24px 2px rgba(144,255,157,0.4);
            border-radius: 32px 8px 32px 0px;
            padding: 8px 45px;
            font-size: 24px;
            font-family: PingFang TC-Semibold, PingFang TC;
            font-weight: 600;
            color: #FFFFFF;
            span{
                display: inline-block;
                width: 32px;
                height: 38px;
                background: url('@/assets/TT/Icon/badge.png') no-repeat;
                background-size: 100% auto;
                vertical-align: middle;
                margin-right: 10px;
            }
        }
        .award{
            position: absolute;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            bottom: 26px;
            left: 0px;
            font-size: 24px;
            font-family: PingFang TC-Regular, PingFang TC;
            font-weight: 400;
            color: #4E5B7E;
            
            .p{
                margin-left: 36px;
                display: flex;
                align-items: center;
                position: relative;
                .lock{
                    position: absolute;
                    width: 62px;
                    height: 62px;
                    top: -6px;
                    left: -6px;
                }
                .img1{
                    margin-left: 42px;
                }
                .img{
                    flex-shrink: 0;
                    position: relative;
                    margin-right: 16px;
                    width: 60px;
                    height: 60px;
                    display: flex;
                    background: #EFEFEF;
                    opacity: 1;
                    border: 6px solid #E2E2E2;
                    border-radius: 50%;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    .im{
                        max-width: 90%;
                        max-height: 90%;
                    }
                    .achieve{
                        max-width: 120%;
                        max-height: 120%;
                    }
                    .mygarden{
                        position: absolute;
                        left: 50%;
                        transform: translate(-50%, 0);
                        bottom: -20px;
                        background: #40CB46;
                        border-radius: 12px 12px 12px 12px;
                        font-size: 18px;
                        font-family: PingFang HK-Semibold, PingFang HK;
                        font-weight: 600;
                        color: #FFFFFF;
                        padding: 2px 4px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        white-space: nowrap;
                    }
                    .mygarden2{
                        background: linear-gradient(180deg, #C18B6F 0%, #885A42 100%);
                    }
                }
                .propStatus{
                    display: inline-block;
                    font-size: 24px;
                    height: 34px;
                    color: #FFFFFF;
                    background: #4BCF51;
                    padding: 0px 10px;
                    border-radius: 18px 18px 18px 18px;
                    margin-left: 12px;
                    margin-right: 12px;
                    transform: translate(0, 2px);
                    white-space: nowrap;
                    &.propStatus1{
                        background: rgba(167,174,195,0.7);
                        border-radius: 18px 18px 18px 18px;
                    }
                }
            }
            
            .view{
                margin-right: 36px;
                font-size: 24px;
                font-family: PingFang TC-Regular, PingFang TC;
                font-weight: 400;
                color: #A7AEC3;
                white-space: nowrap;
                span{
                    display: inline-block;
                    background: url('@/assets/TT/Icon/jiantou.png') no-repeat;
                    background-size: 100% auto;
                    width: 12px;
                    height: 20px;
                    margin-left: 12px;
                }
            }
        }
        
    }
}
</style>
