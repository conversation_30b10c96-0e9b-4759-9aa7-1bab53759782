import { createRouter, createWebHashHistory, RouteLocationNormalized, RouteRecordRaw } from 'vue-router'
import * as listener from './listener'

const router = createRouter({
  history: createWebHashHistory(),
  routes: [],
  scrollBehavior: () => {
    return { top: 0 }
  }
})

router.afterEach((to) => {
  listener.emit(to)
})

function addRoutes(routes: RouteRecordRaw[]) {
  routes.forEach((route) => {
    router.addRoute(route)
  })
}

let currentRoute: RouteLocationNormalized | undefined
listener.listen((route) => {
  currentRoute = route
}, true)

export function useRouter() {
  return {
    addRoutes,
    router,
    currentRoute,
    listener
  }
}
