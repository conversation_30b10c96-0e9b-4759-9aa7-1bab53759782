<template>
    <div class="detailImg-content">
        <div class="head">
            <div class="text">{{ state.nft.活動限定 }}</div>
        </div>
        <div class="bottom">
            <div ref="sprite"></div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { getImageUrl } from '@/util/unity'
import { ref, onMounted, nextTick } from 'vue'
import { useLang } from 'hook'
import { imgs } from '@/assets/imgs'
const props = defineProps({
    treeCode: {
        type: String,
        default: ''
    }
})
const sprite = ref()
const imageList: Record<string, string> = {}
const { state } = useLang()
onMounted(async () => {
    initSprite()
})

/** 初始化精灵 */
const initSprite = async () => {
    const html = sprite.value
    if (html) {
        // 清除原来的样式
        html.classList.remove('sprite')
        const code = props.treeCode
        if (code) {
            html.style['background-image'] = `url(${imgs[`NTFDetail/animation_small/${code}.png`]})`
            nextTick(() => html.classList.add('sprite'))
        }
    }
}
</script>

<style lang="less" scoped>
@spw: 150px;
@sph: 153px;
.frame(@col:0, @row:0) {
    background-position: -@spw * @col -@sph * @row;
}
.sprite {
    width: 300px;
    height: 306px;
    animation: myAnimation 3s steps(1) /*infinite*/;
    background-size: 4800px 612px;
    // 让动画保持在最后一帧
    animation-fill-mode: forwards;
    // .frame(13,1);
    @keyframes myAnimation {
        0% { background-position: 0px 0px; }
	3.45% { background-position: -300px 0px; }
	6.90% { background-position: -600px 0px; }
	10.34% { background-position: -900px 0px; }
	13.79% { background-position: -1200px 0px; }
	17.24% { background-position: -1500px 0px; }
	20.69% { background-position: -1800px 0px; }
	24.14% { background-position: -2100px 0px; }
	27.59% { background-position: -2400px 0px; }
	31.03% { background-position: -2700px 0px; }
	34.48% { background-position: -3000px 0px; }
	37.93% { background-position: -3300px 0px; }
	41.38% { background-position: -3600px 0px; }
	44.83% { background-position: -3900px 0px; }
	48.28% { background-position: -4200px 0px; }
	51.72% { background-position: -4500px 0px; }
	55.17% { background-position: 0px -306px; }
	58.62% { background-position: -300px -306px; }
	62.07% { background-position: -600px -306px; }
	65.52% { background-position: -900px -306px; }
	68.97% { background-position: -1200px -306px; }
	72.41% { background-position: -1500px -306px; }
	75.86% { background-position: -1800px -306px; }
	79.31% { background-position: -2100px -306px; }
	82.76% { background-position: -2400px -306px; }
	86.21% { background-position: -2700px -306px; }
	89.66% { background-position: -3000px -306px; }
	93.10% { background-position: -3300px -306px; }
	96.55% { background-position: -3600px -306px; }
	100.00% { background-position: -3900px -306px; }
        // 0% {
        //     .frame();
        // }
        // 3.45% {
        //     .frame(1,0);
        // }
        // 6.90% {
        //     .frame(2,0);
        // }
        // 10.34% {
        //     .frame(3,0);
        // }
        // 13.79% {
        //     .frame(4,0);
        // }
        // 17.24% {
        //     .frame(5,0);
        // }
        // 20.69% {
        //     .frame(6,0);
        // }
        // 24.14% {
        //     .frame(7,0);
        // }
        // 27.59% {
        //     .frame(8,0);
        // }
        // 31.03% {
        //     .frame(9,0);
        // }
        // 34.48% {
        //     .frame(10,0);
        // }
        // 37.93% {
        //     .frame(11,0);
        // }
        // 41.38% {
        //     .frame(12,0);
        // }
        // 44.83% {
        //     .frame(13,0);
        // }
        // 48.28% {
        //     .frame(14,0);
        // }
        // 51.72% {
        //     .frame(15,0);
        // }
        // 55.17% {
        //     .frame(0,1);
        // }
        // 58.62% {
        //     .frame(1,1);
        // }
        // 62.07% {
        //     .frame(2,1);
        // }
        // 65.52% {
        //     .frame(3,1);
        // }
        // 68.97% {
        //     .frame(4,1);
        // }
        // 72.41% {
        //     .frame(5,1);
        // }
        // 75.86% {
        //     .frame(6,1);
        // }
        // 79.31% {
        //     .frame(7,1);
        // }
        // 82.76% {
        //     .frame(8,1);
        // }
        // 86.21% {
        //     .frame(9,1);
        // }
        // 89.66% {
        //     .frame(10,1);
        // }
        // 93.10% {
        //     .frame(11,1);
        // }
        // 96.55% {
        //     .frame(12,1);
        // }
        // 100.00% {
        //     .frame(13,1);
        // }
    }
}

.detailImg-content {
    .head {
        position: relative;
        height: 0px;
        .text {
            width: 116px;
            height: 48px;
            background: linear-gradient(95deg, #ffe1bf 0%, #dbaa69 100%);
            border-radius: 24px 4px 24px 4px;

            font-size: 28px;
            color: #965f12;
            line-height: 48px;
            text-align: center;
        }
    }
}
</style>
