import sc from './sc'
import en from './en'
import tc from './tc'
import { useLang } from 'hook'
import { init, scDate, tcDate, enDate } from '@/util/KVToI18'
import { useAppStore,usePlantStore } from '@/store'

// export type CustomLang = typeof tc
export type CustomLang = Record<string,any>

// const messages = {
//     sc: Object.assign(sc, scDate),
//     en: Object.assign(en, enDate),
//     tc: Object.assign(tc, tcDate)
// }

export async function assign() {
    // await init()
    const messages = {
        sc: Object.assign(sc, usePlantStore().scPlant),
        en: Object.assign(en, usePlantStore().enPlant),
        tc: Object.assign(tc, usePlantStore().tcPlant)
    }
    useLang().setMessages(messages)
    useAppStore().changeIsFinishKV(true)
}

// useLang().setMessages(messages)

declare module 'hook' {
    namespace UseLang {
        interface Lang extends CustomLang {}
    }
}
