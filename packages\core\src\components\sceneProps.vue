<template>
  <div class="scene">
      <!-- 铲子 -->
      <img v-if="propStore.propsConfig['铲子']" :class="`chanzi ${propStore.propsConfig['铲子']}`" :src="$imgs[`props/铲子/${propStore.propsConfig['铲子']}.png`]" alt="">
      <!-- 稻草人 -->
      <img v-if="propStore.propsConfig['稻草人']" :class="`daocaoren ${propStore.propsConfig['稻草人']}`" :src="$imgs[`props/稻草人/${propStore.propsConfig['稻草人']}.png`]" alt="">
      <!-- 灯柱 -->
      <img v-if="propStore.propsConfig['灯柱']" :class="`dengzhu ${propStore.propsConfig['灯柱']}`" :src="$imgs[`props/灯柱/${propStore.propsConfig['灯柱']}.png`]" alt="">
      <!-- 木头车 -->
      <img v-if="propStore.propsConfig['木头车']" :class="`mutouche ${propStore.propsConfig['木头车']}`" :src="$imgs[`props/木头车/${propStore.propsConfig['木头车']}.png`]" alt="">
      <!-- 喷水池 -->
      <img v-if="propStore.propsConfig['喷水池']" class="penshuichi" :src="$imgs[`props/喷水池/${propStore.propsConfig['喷水池']}.png`]" alt="">
      <!-- 围栏 -->
      <img v-if="propStore.propsConfig['围栏']" class="weilan" :src="$imgs[`props/围栏/${propStore.propsConfig['围栏']}_s.png`]" alt="">
      <!-- 椅子和桌子 -->
      <div class="zhuodeng">
          <img v-if="propStore.propsConfig['椅子']" :class="`yizi ${propStore.propsConfig['椅子'] == 'yizi6' ? '':'left'} ${propStore.propsConfig['椅子']}`" :src="$imgs[`props/椅子/${propStore.propsConfig['椅子']}.png`]" alt="">
          <img v-if="propStore.propsConfig['桌子']" :class="`zhuozi ${propStore.propsConfig['桌子']}`" :src="$imgs[`props/桌子/${propStore.propsConfig['桌子']}.png`]" alt="">
          <img v-if="propStore.propsConfig['椅子'] && propStore.propsConfig['桌子']" :class="`yizi ${propStore.propsConfig['椅子']} ${propStore.propsConfig['椅子'] == 'yizi6' ? 'left':''}`" :src="$imgs[`props/椅子/${propStore.propsConfig['椅子']}.png`]" alt="">
      </div>
  </div>
</template>

<script lang='ts' setup>
import { ref, computed } from 'vue'
import { usePropStore, useTaskStore, useTreeStore, useFriendStore } from '@/store'

const propStore = computed(() => {
    return useFriendStore().isOwn ? usePropStore() : useFriendStore()
}) 


</script>

<style lang="less" scoped>
.scene{//树z-index 20
    width: 100%;
    height: 100%;
    position: absolute;
    pointer-events: none;
    img{
        z-index: 19;
    }
    .chanzi{
        position: absolute;
        bottom: -50px;
        right: 48px;
        width: 40px;
    }

    .mutouche{
        position: absolute;
        bottom: -40px;
        right: 224px;
    }
    .mutouche1{
        width: 56px;
    }
    .mutouche2{
        width: 75px;
    }
    .mutouche3{
        width: 56px;
    }
    .mutouche4{
        width: 56px;
    }
    .mutouche5{
        width: 75px;
    }
    .mutouche6{
        width: 75px;
    }
    .dengzhu{
        position: absolute;
        bottom: 100px;
        right: 180px;
    }
    .dengzhu1{
        width: 20px;
    }
    .dengzhu2{
        width: 40px;
        height: 99px;
    }
    .dengzhu3{
        width: 40px;
        height: 99px;
    }
    .dengzhu4{
        width: 40px;
        height: 99px;
    }
    .dengzhu5{
        width: 40px;
        height: 99px;
    }
    .penshuichi{
        position: absolute;
        width: 120px;
        bottom: 80px;
        right: 30px;
    }
    .daocaoren{
        position: absolute;
        width: 104px;
        bottom: 45px;
        left: 35px;
        z-index: 0;
    }
    .weilan{
        z-index: 21;
        position: absolute;
        width: 164px;
        bottom: 9px;
        left: 50%;
        transform: translate(-50%, 0);
    }
    .zhuodeng{
        position: absolute;
        bottom: 70px;
        left: 145px;
        .yizi1{
            width: 30px;
        }
        .yizi2{
            width: 40px;
        }
        .yizi3{
            width: 40px;
        }
        .yizi4{
            width: 40px;
        }
        .yizi5{
            width: 36px;
        }
        .yizi6{
            width: 40px;
        }
        .yizi7{
            width: 40px;
        }
        .yizi8{
            width: 40px;
        }
        .yizi9{
            width: 40px;
        }
        .yizi10{
            width: 40px;
        }
        .left{
            transform: rotateY(180deg);
        }
        .zhuozi{
            margin: 0 5px;
        }
        .zhuozi1{
            width: 70px;
        }
        .zhuozi2{
            width: 35px;
        }
        .zhuozi3{
            width: 53px;
        }
        .zhuozi4{
            width: 53px;
        }
        .zhuozi5{
            width: 36px;
        }
        .zhuozi6{
            width: 53px;
        }
        .zhuozi7{
            width: 53px;
        }
        .zhuozi8{
            width: 53px;
        }
        .zhuozi9{
            width: 53px;
        }
        .zhuozi10{
            width: 53px;
        }
    }
}
</style>