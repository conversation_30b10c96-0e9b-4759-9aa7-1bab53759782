declare function load(type: 'js' | 'css', src: string): Promise<unknown>;
declare function loadJs(src: string): Promise<unknown>;
declare function loadCss(src: string): Promise<unknown>;
declare function loadAssetsList(type: 'js' | 'css', list: string[], async?: boolean): Promise<unknown[] | undefined>;
export declare function useLoader(): {
    baseUrl: string;
    load: typeof load;
    loadJs: typeof loadJs;
    loadCss: typeof loadCss;
    loadAssetsList: typeof loadAssetsList;
};
export {};
