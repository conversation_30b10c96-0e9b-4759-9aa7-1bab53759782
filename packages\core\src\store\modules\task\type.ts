import { useTaskStore } from '.'
export interface taskType {
    action_upgrade: any,
    acIntStu: boolean//积分活动
    openTask:boolean
    item:itemType
    taskId:number
    limitTask:Boolean
    upLevelPlantList:any
    banner:bannerItemType[]
}

interface bannerItemType {
    banner_id:number
    image_url:string
    jump_url:string
}

export interface GetBuyHistoryReturnType {
    records: {
        commodity: {
            code: string
            id: number
            name: string
            metadata: {
                countText: string
                description: string
                effectiveText: string
                flowText: string
                name: string
                nameEn: string
                nameSc: string
                icon: string
                iconUrl:string
            }
            target:number
        }
        created_at: string
        expired_at?: string
        energy_value: number
        id: number
        status: number
    }[]
    total: number
}
export type TaskStoreType = ReturnType<typeof useTaskStore>

export interface TaskListType {
    code:string,
    energy_type:string,
    energy_value:number,
    item_codes:string,
    recv_count:number,
    req_count:number,
    save_energy_value:number,
    status:number,
    type:number,
    metadata:Object,
    expired_at:string,
    task_id:number
}

export interface TaskTimeType {
    batch:string,
    id:number,
    stop_at:string | null
}

export interface itemType {
    code:string
    energy_value:number
    id:number
    metadata:any
    name:string
    newest:boolean
    rarity:number
    status:number
    stock_count:number
}