<template>
  <div class="box">
    <p> {{state.dialog.将种下x的种子.replace('{x}', props.plantName)}}</p>
   
    <div class="btnBox">
      <button class="btn cancel" @click="cancel"> {{state.dialog.重新选择}}</button>
      <button class="btn confirm" @click="confirm"> {{state.dialog.确定}} </button>
    </div>
    
  </div>
</template>

<script setup>
import { useDialog, useLang } from 'hook';
import { onMounted, inject, ref, watch, getCurrentInstance } from 'vue'

const props = defineProps({
  plantName: {
      type: String,
      required: false
  }
})
const { state } = useLang()
const uuid = inject('uuid')

const cancel = () =>{
    useDialog().getInstance(uuid)?.emit('cancel')
} 

const confirm = () =>{
    useDialog().getInstance(uuid)?.emit('confirm')
} 

</script>

<style lang='less' scoped>
.box{
    width: 542px;
    height: 284px;
    background: #FFFFFF;
    border-radius: 72px 24px 72px 24px;
    opacity: 1;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-around;
    padding: 20px 0;
    box-sizing: border-box;
    
    position: relative;
    p{
      text-align: center;
      display: inline-block;
      font-size: 30px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #4E5B7E;
      line-height: 44px;
    }
    .btnBox{
      width: 100%;
      display: flex;
      justify-content: space-around;
      .btn{
        width: 220px;
        height: 56px;
        border-radius: 48px 12px 48px 12px;
        opacity: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        font-family: PingFang SC-Medium, PingFang SC, Arial, Helvetica, sans-serif;
        font-weight: 500;
        bottom: 70px;
      }
      .cancel{
        background: #FFFFFF;
        border: 2px solid #9EA5BA;
        color: #A7AEC3;
      }
      .confirm{
        background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
        box-shadow: inset 0px 4px 0px 2px rgba(255,242,178,1);
        border: none;
        color: #FFFFFF;
      }
    }
    
}
</style>