<template>
<div class="home">
     <!-- <div class="box one">one</div>
     <div class="box two">two</div>
     <button @click="goRoute"> 返回上一级</button> -->
    <!-- <achieve type='big' /> -->
    <div class="box">
        <p :class="`Hp ${isRefresh ? 'anima' : ''}`">+10HP</p>
    </div>
    <button @click="refresh">刷新按钮</button>
</div>

</template>

<script setup>
import { useDialog, useWindow, useRouter, useLoading } from 'hook'
import { ref, onMounted } from 'vue'
import { inApp, getUserInfo, closeWebView, getSystem } from '@via/mylink-sdk';
import HomeNotAward from '@/components/propDialog.vue'
import treasure from '@/components/treasure.vue'
import guideDialog from '@/components/guideDialog.vue';
import confirmDialog from '@/components/confirmDialog.vue';
import successPlant from '@/components/successPlant.vue';
import updateDialog from '@/components/updateDialog.vue';
import achieveDialog from '@/components/achieveDialog.vue';
import actDialog from '@/components/actDialog.vue';
import firstLoginDialog from '@/components/firstLoginDialog.vue';
import plantDialog from '@/components/plantDialog.vue';
import achieve from '@/components/achieve.vue';
import { stepCountStatus, nativeBackClick } from '@unity/unity'
const { router, currentRoute } = useRouter()
const loading = useLoading()
const { mQuery } = useWindow()
const system = getSystem()
console.log(mQuery, 'mQuery')
const dialog = useDialog({
  HomeNotAward,
  guideDialog,
  confirmDialog,
  successPlant,
  updateDialog,
  achieveDialog,
  actDialog,
  firstLoginDialog,
  plantDialog
})
loading.loading('close')
// const goRoute = () => {
//     nativeBackClick()
//     // console.log(router)
// }
// onMounted(() => {
//     document.querySelector('.Hp') .addEventListener('animationend',function(e){
//         console.log('播放完了')
//         isRefresh.value = false
//     })
// })
// dialog.get('successPlant').show({treeCode:'yangzijing'})
// dialog.get('confirmDialog').show()
// dialog.get('firstLoginDialog').show({HpData: { n1: 50, n2: 250}}, {maskClose:false})
// const showDialog = () => {
//     dialog.get('HomeNotAward').show({ isInfo: true })
// }
// dialog.get('updateDialog').show({}, {maskClose:false})
// window.stepStatus = (obj) => {
//   console.log(obj, 'obj')
// }
// if(inApp.value){
//     // stepCountStatus({ callbackName: 'stepStatus' })
//     let useInfo = await getUserInfo()
//     console.log('useInfo', useInfo)
// }

let isRefresh = ref(false)
const refresh = () => {
    isRefresh.value = true
}
</script>

<style lang="less" scoped>
.tooltipClass{
    background: #000;
}
.home{
    margin: 0 auto;
    position: relative;
    height: 400px;
    transform-origin: 50% 400px 0;
    .box{
        width: 100%;
        height: 500px;
        background-color: skyblue;
        display: flex;
        align-items: center;
        justify-content: center;
        .Hp{
            font-size: 30px;
            font-weight: 550;
            color: #bd5642;
            display: none;
        }
        .anima{
            display: block;
            animation: float 1.3s forwards;
        }
        @keyframes float{
            from {
                transform: translate(0, 0) scale(1);
                opacity: 1;
            }
            to {
                transform: translate(0, -50px) scale(0.9);
                opacity: 0;
            }
        }
    }
    .shake{
        position: absolute;
        top: 0;
        left: 40%;
        color: green;
    }
    .shugan{
        font-size: 80px;
        position: absolute;
        top: 100px;
        left: 48%;
    }
}

// .home:hover{
//     animation: move 1s linear 1;
// }

@keyframes move {
    0% {
        transform: scaleY(1) ;
    }
    25% {
         transform: scaleY(1.15) ;
    }
    50% {
         transform: scaleY(1.3) ;
    }
    75% {
        transform: scaleY(1.15) ;
    }
    100% {
        transform: scaleY(1) ;
    }
}

</style>
