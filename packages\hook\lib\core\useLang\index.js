import { reactive, ref, watch } from 'vue';
import { createI18n } from 'vue-i18n';
import langAssets from './assets/index';
import Formatter from './format';
import { useWindow } from '../useWindow';
const formatter = new Formatter();
const { lang } = useWindow().mQuery;
const currentLang = ref(lang || window.sessionStorage.getItem('lang') || 'tc');
const i18n = createI18n({
    locale: currentLang.value
});
const state = reactive(langAssets.tc);
const setLang = (locale) => {
    currentLang.value = i18n.global.locale = locale || 'tc';
    Object.assign(state, langAssets[currentLang.value], i18n.global.messages[currentLang.value]);
    window.sessionStorage.setItem('lang', currentLang.value);
    console.log('当前语言', currentLang.value);
};
const setMessages = (messages) => {
    const keys = Object.keys(messages);
    keys.forEach((key) => {
        i18n.global.setLocaleMessage(key, messages[key]);
    });
    Object.assign(state, langAssets[currentLang.value], i18n.global.messages[currentLang.value]);
};
setLang(currentLang.value);
const onLangChange = (cb) => {
    watch(currentLang, () => {
        cb(currentLang);
    }, { immediate: true });
};
function format(message, values) {
    return formatter.interpolate(message, values);
}
export function useLang() {
    return {
        state,
        lang: currentLang,
        setLang,
        onLangChange,
        format,
        setMessages
    };
}
