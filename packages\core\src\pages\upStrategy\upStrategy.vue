<template>
    <div class="home">
        <navComponent :returnType="2" :showTransmit="false" />
        <div class="banner"></div>
        <div class="bubbleAlerts">
            <img src="../../assets/imgs/leaf2.png" alt="">
            <div class="bubble" v-html="state.upStrategy.气泡提醒">
            </div>
        </div>
        <div class="topBox">
            <template v-for="(el, index) in state.workType" :key="index">
                <div class="title">
                    {{ el.title }}
                </div>
                <!-- <div class="tips">
                    <div class="ttips"> {{ state.upStrategy.Tips }}</div>
                    <p class="ttext">{{ state.upStrategy.完成上述行为后 }}</p>
                </div> -->
                <div class="walk">
                    <div class="walkitem" v-for="(item, index) in el.list" :key="index">
                        <img class="icon" :src="$imgs[`work/${item.index}.png`]" alt="">
                        <div class="text">
                            <p class="top">
                                <span>{{ item.name }}</span>
                                <i @click="clickExclamation(item)" v-if="item.detail">
                                    <img src="../../assets/imgs/exclamation2.png" alt="">
                                </i>
                            </p>
                            <p class="bot" v-html="item.tip"></p>
                        </div>
                        <div class="new" v-if="item.index == 17">
                            <span>{{ state.ai.限時 }}</span>
                        </div>
                    </div>
                </div>
            </template>
            <!-- <div class="cardInfo">
                <img class="card22" :src="$imgs['card22.png']" alt="">
                <p class="titleC">{{ state.upStrategy.功能卡使用规则 }}</p>
                <p class="contentC" v-html="state.upStrategy.每次只能使用一张功能卡"></p>
                <div class="shouBtn" @click="isShow = !isShow">
                    <p>{{ !isShow ? state.upStrategy.展开 : state.upStrategy.收起全部 }}</p> <img
                        :class="{ 'fanzhuan': isShow }" :src="$imgs['xiala.png']" alt="">
                </div>
            </div> -->
            <div class="cardInfo2">
                <div class="taskHead">{{ state.upStrategy.功能卡使用规则 }}</div>
                <div class="prompt">{{ state.upStrategy.每次只能使用一张功能卡 }}</div>
                <div class="cardInfoo">
                    <div class="cardInfoo-item" v-for="(item, index) in state.upStrategy.List" :key="index">
                        <img :src="$imgs[item.img]" alt="">
                        <p class="titleU">{{ item.title }}</p>
                        <p class="contentU">{{ item.content }}</p>
                    </div>
                </div>
            </div>
            <!-- 任务 -->
            <div class="taskCard" v-if="useUserStore().isHK == 0">
                <div class="taskHead">{{ state.task.任務玩法攻略 }}</div>
                <div class="taskDes">
                    <div class="task" v-for="(item, index) in 3">
                        <img :src="$imgs[`strategy/${taskImg[index]}.png`]" alt="">
                        <p v-html="state.upStrategy.任务[index]"></p>
                    </div>
                    <div class="arrow">
                        <img :src="$imgs['strategy/jiantou.png']" alt="">
                        <img :src="$imgs['strategy/jiantou.png']" alt="">
                    </div>
                </div>
            </div>
            <div class="resource">
                <div class="line"></div>
                <div class="retext">{{ state.upStrategy.更多减排值来源请等待更新 }}</div>
                <div class="line"></div>
            </div>
        </div>
        <div class="botBox">
            <div class="title">
                {{ state.upStrategy.养成过程 }}
            </div>
            <div class="pic1">
                <div class="status">
                    <img :src="$imgs['strategy/top/1.png']" alt="">
                    <p> <span>1</span> {{ state.upStrategy.选择植物 }}</p>
                </div>
                <div class="status status2">
                    <img :src="$imgs['strategy/top/2.png']" alt="">
                    <p> <span>2</span> {{ state.upStrategy.低碳行为2 }}</p>
                </div>
                <div class="status status3">
                    <img :src="$imgs['strategy/top/3.png']" alt="">
                    <p> <span>3</span> {{ state.upStrategy.收集减排值 }}</p>
                </div>
                <div class="status status4">
                    <img :src="$imgs['strategy/top/4.png']" alt="">
                    <p> <span>4</span> {{ state.upStrategy.植物升级 }}</p>
                </div>
                <div class="status status5">
                    <img :src="$imgs['strategy/top/5.png']" alt="">
                    <p> <span>5</span> {{ state.upStrategy.获得奖励 }}</p>
                </div>
                <div class="line line1"></div>
                <div class="line line2"></div>
                <div class="line line3"></div>
                <div class="line line4"></div>
            </div>
            <div class="title">
                {{ state.upStrategy.种植阶段 }}
            </div>
            <div class="pic2">
                <div :class="`stage 
                r${~~((index) / 3) + 1} 
                c${index <= 2 || index > 5 ? ((index) % 3 + 1) : (3 - (index) % 3)}`
                    " v-for="(item, index) in state.plantStage.slice(0, 9)" :key="index">
                    <div>
                        <img :src="$imgs[`strategy/bot/${index + 1}.png`]" alt="">
                    </div>
                    <p>{{ item }}</p>
                </div>

                <!-- 箭头 -->
                <div class="arr rr1 cc2"></div>
                <div class="arr rr1 cc3"></div>
                <div class="arr down rr2 cc4"></div>
                <div class="arr left rr3 cc2"></div>
                <div class="arr left rr3 cc3"></div>
                <div class="arr down rr4 cc1"></div>
                <div class="arr rr5 cc2"></div>
                <div class="arr rr5 cc3"></div>
            </div>
        </div>
        <ConfigProvider :theme-vars="themeVars">
            <ActionSheet :show="showUpStrategyDialog" :lock-scroll="true">
                <div class="sheetContent">
                    <div class="content">
                        <div class="xx" @click="closeUpStrategyDialog"></div>
                        <upStrategyDialog :title=title :detail=detail :type="type"></upStrategyDialog>
                    </div>
                </div>
            </ActionSheet>
        </ConfigProvider>
        <div class="backToTop" id="back-to-top" @click="clickBackToTop">
            <img :src="$imgs['backToTop.png']" alt="">
            <p>{{ state.backToTop }}</p>
        </div>
    </div>
</template>


<script setup>
import { ActionSheet, ConfigProvider } from 'vant';
import { ref, onBeforeMount, onMounted, onBeforeUnmount } from 'vue'
import { useLoading, useLang, useEventBus } from 'hook'
import { logEventStatistics } from '@via/mylink-sdk'
import navComponent from '@/components/navComponent.vue';
import upStrategyDialog from '@/components/upStrategyDialog.vue';
import { useTaskStore, useUserStore } from "@/store";

const { state } = useLang()
const eventBus = useEventBus()
useLoading().loading('close')
//查看升級攻略訪問量
logEventStatistics('Mygarden_upgrade_tips_page')

// 任务图片
const taskImg = ['c1', 'c2', 'c3']

const title = ref('')

const detail = ref('')

const type = ref('')

let themeVars = {
    popupBackgroundColor: 'none',
    actionSheetMaxHeight: '100%'
};

let isShow = ref(false)
let isShowBackToTop = ref(false)
let showUpStrategyDialog = ref(false)
let first_water_home = ref('')
let first_game_strategy = ref('')

const clickExclamation = (item) => {
    title.value = item.name
    detail.value = item.detail
    type.value = item.type
    showUpStrategyDialog.value = true
}

const closeUpStrategyDialog = () => {
    showUpStrategyDialog.value = false
}

const clickBackToTop = () => {
    if(!isShowBackToTop.value) return 
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

const scrollFunction = () => {
    if (document.body.scrollTop > window.innerHeight || document.documentElement.scrollTop > window.innerHeight) {
        const element = document.getElementById("back-to-top")
        if(element){
            element.style.opacity = "1";
            isShowBackToTop.value = true
        }
    } else {
        const element = document.getElementById("back-to-top")
        if(element){
            element.style.opacity = "0";
            isShowBackToTop.value = false
        }
    }
}

onMounted(()=>{
    window.addEventListener('scroll', scrollFunction);
})

onBeforeUnmount(()=>{
    window.removeEventListener('scroll', scrollFunction);
})

onBeforeMount(async () => {
    eventBus.on('closeUpStrategyDialog', closeUpStrategyDialog)
    if (!useUserStore().inLogin) return
    let info = await useTaskStore().getInfo()
    if (!info.actived_aigc_award_activity) {
        state.workType[1].list.pop()
    }
    first_water_home.value = info.webui_manage_values ? info.webui_manage_values.first_water_home : dayjs().format('YYYY-MM-DD')
    first_game_strategy.value = info.webui_manage_values ? info.webui_manage_values.first_game_strategy : dayjs().format('YYYY-MM-DD')
})
</script>

<style lang="less" scoped>
@import './upStrategy.scss';
</style>
