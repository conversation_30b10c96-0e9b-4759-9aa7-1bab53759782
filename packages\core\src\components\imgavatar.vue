<template>
  <div class="box">
    <img src="../assets/TT/Icon/friend_Avatar.png" v-if="ff.current_tree?.code" @click="toFriend(ff.third_id)" alt="" :class="{imgavatar:true, imgavatar1: !isRank}" />
    <img src="../assets/TT/Icon/friend_Avatar.png" v-else @click="upShare(ff.isOwn)" alt="" :class="{imgavatar:true, imgavatar1: !isRank}" />
    <div class="friendinfo">
        <span class="name">{{`${ff.name}${ff.isOwn ? `(${state.friend.我})` : ''}`}}</span>
        <p class="level" v-if="ff.current_tree?.code">
            {{state.plant[ff.current_tree.code].plantName}} LV.{{ff.current_tree.level}}
        </p>
        <p class="level" v-else>{{ state.friend.未种植 }}</p>
    </div>
    <div class="tanValue" v-if="ff.current_tree?.code || ff.energy_total !==0">
        <div class="value"><img src="../assets/TT/Icon/yezi.png" alt="" /> {{ff.energy_total}}g</div>
        <div class="toGet" v-if="ff.can_collect == 1" @click="toFriend(ff.third_id,ff.name)">
            {{ state.friend.前往领取 }}>
        </div>
        <div class="toSee" v-if="ff.can_collect == 0 && !ff.isOwn" @click="toFriend(ff.third_id,ff.name)">
            {{ state.friend.去查看 }}>
        </div>
    </div>
    <!-- 没种植 -->
    <div class="tanValue" v-else>
        <div class="toInvite" v-if="ff.isOwn" @click="toPlant">{{ state.friend.去种植 }}></div>
        <div class="toInvite" v-if="!ff.isOwn" @click="share(ff.isOwn)">{{ state.friend.去邀请 }}></div>
    </div>
  </div>
</template>

<script setup lang='ts'>
import { ref, watch, reactive, onMounted, onBeforeMount, onBeforeUnmount, getCurrentInstance, computed } from 'vue'
import { useRouter, useLang, useLoading, useDayjs, useStorage, useEnvConfig, useDialog, useToast, useEventBus } from 'hook'
import { useFriendStore, useTreeStore, useUserStore } from '@/store'
import { nativeBackClick, upShare } from '@unity/unity'
import { logEventStatistics } from "@via/mylink-sdk";
const envConfig = useEnvConfig()
const storage = useStorage()
const { router, currentRoute } = useRouter()
const { state, lang } = useLang()
let eventBus = useEventBus()
let emit = defineEmits(['close'])
const props = defineProps({
  ff: {
      type: Object,
      required: true
  },
  isRank: {
      type: Boolean,
      required: false,
      default: false
  }
})

function share(isOwn) {
    logEventStatistics('garden_go_invite_click')
    upShare(isOwn)
}

function toFriend(id,name){
    if (props.ff.isOwn) {
        return
    }
    // if(!useTreeStore().hasPlanted){
    //     eventBus.emit('isPlant')
    //     return
    // }
    if (props.ff.can_collect == 1 && props.ff.current_tree?.code) {
        logEventStatistics('garden_go_collect_click')
    }
    if (props.ff.can_collect == 0 || (!props.ff.current_tree?.code && !props.ff.isOwn)) {
        logEventStatistics('garden_go_view_click')
    }
    eventBus.emit('closeFriendSheet')
    useFriendStore().changeIsOwn(false)
    if (location.href.indexOf("friendId") !== -1) {
        router.replace({
            path: "/home",
            query: {
                friendId: id,
                hideNavigationBar:'true',
                friendname: name
            },
        })
    }else{
        router.push({
            path: "/home",
            query: {
                friendId: id,
                hideNavigationBar:'true',
                friendname: name
            }
        })
    }

}

function toPlant() {
    eventBus.emit('closeFriendSheet')
    router.push({
        path: "/plantSelection",
        query:{
            hideNavigationBar:'true'
        }
    })
}

</script>

<style lang="less" scoped>
.box{
    display: flex;
    align-items: center;
    flex: 1;
    .imglevel {
        width: 22px;
        height: 44px;
        margin-right: 40px;
    }
    .imglevel2 {
        width: 22px;
        height: 44px;
        font-size: 40px;
        font-family: Arial-Bold Italic, Arial;
        font-weight: bold;
        color: #4e5b7e;
        line-height: 28px;
        font-style: italic;
    }
    .imgavatar {
        width: 66px;
        height: 66px;
        margin-right: 24px;
    }
    .imgavatar1{
        margin-left: 62px;
    }
    .friendinfo {
        flex: 1;
        .name {
            color: #4e5b7e;
            display: block;
            width: 340px;
            white-space: nowrap; /* 防止文本换行 */
            overflow: hidden; /* 超出容器部分隐藏 */
            text-overflow: ellipsis; /* 超出部分以省略号表示 */
        }
        .level {
            color: #a7aec3;
            font-size: 24px;
        }
    }
    .tanValue {
        display: flex;
        align-items: center;
        flex-direction: column;
        img {
            width: 20px;
            height: 20px;
        }
        .toInvite{
            font-size: 20px;
            color: #FFFFFF;
            white-space: nowrap;
            align-items: center;
            padding: 4px 24px;
            background: #22992C;
            border-radius: 22px 22px 22px 22px;
        }
        .value {
            display: flex;
            align-items: center;
            margin-left: 8px;
            margin-bottom: 8px;
            font-size: 32px;
            color: #22992c;
            font-weight: bold;
        }
        .toGet{
            font-size: 20px;
            color: #FFFFFF;
            white-space: nowrap;
            align-items: center;
            padding: 4px 9px;
            background: linear-gradient(128deg, #50DD4C 0%, #3CBF48 100%);
            border-radius: 22px 22px 22px 22px;
        }
        .toSee{
            font-size: 20px;
            color: #3CBF48;
            white-space: nowrap;
            align-items: center;
            padding: 4px 24px;
            border: 2px solid #3CBF48;
            border-radius: 22px 22px 22px 22px;
        }
    }
}
</style>
