<template>
    <div class="contain">
        <navComponent :returnType="2" :showTransmit="false" />
        <!-- 用户头像 -->
        <userHead class="userHead" :achieveNum="achieveNum"></userHead>
        <!-- 成就列表 -->
        <div class="list">
            <achieve
                @click="toInfo(item)"
                type="small"
                size="big"
                :item="item"
                v-for="(item, index) in treeList"
                :key="index"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import navComponent from '@/components/navComponent.vue'
import userHead from './components/userHead.vue'
import achieve from '@/components/achieve.vue'
import { useTaskStore, useTreeStore, useUserStore, useNFTStore } from '@/store'
import plantDialog from '@/components/plantDialog.vue'
import { useDialog, useEventBus, usePageVisibility, useToast, useLang } from 'hook'
import upgradeNFTDialog from '@/components/upgradeNFTDialog.vue'
import reCasteDialog from '@/components/reCasteDialog.vue'
const dialog = useDialog({ plantDialog, upgradeNFTDialog, reCasteDialog })
const taskStore = useTaskStore()
const nftStore = useNFTStore()
const eventBus = useEventBus()
const { toast } = useToast()
const { state } = useLang()
/** 成就列表 */
let treeList = ref()
let achieveNum = ref(0)

const loadBeforeMount = async () => {
    // 获取成就列表
    if (!useUserStore().inLogin) {
        treeList.value = await taskStore.getTreeListNoLogin()
    } else {
        useUserStore().userInfo = await taskStore.getInfo()
        treeList.value = await taskStore.getTreeList()
    }
    useTreeStore().treeList = treeList.value
    treeList.value = treeList.value.trees
    if (!useUserStore().userInfo.webui_manage_values?.new_tree_two_achievement) {
        treeList.value.forEach(value => {
            if (value.code === 'huangzhongmu' || value.code === 'shuishirong') {
                value.new = true
            }
        });
    }
    console.log(treeList.value);
    // 计算已解锁成就数
    achieveNum.value = treeList.value.reduce((prev, cur) => {
        return cur.completed ? prev + 1 : prev
    }, 0)

    openNFTDialog()
    useUserStore().setUpData('first_list_two_nft', '1')
    useUserStore().setUpData('first_list_sending_flowers', '1')
    useUserStore().setUpData('new_tree_two_achievement', '1')
}

onMounted(async () => {
    await loadBeforeMount()
    // 在跳转条款页的时候，条款页会把弹窗组件直接隐藏，导致返回的时候弹窗无法出现
    // 所以需要在进入成就页后强行把组件display状态置为初始状态
    let dialogShow = document.querySelector('#app-dialog') as HTMLElement
    dialogShow.style.display = 'initial'

    // 打开送花的弹窗
    if (window.localStorage.getItem('pushSuccess')) {
        toast(state.flower.發送成功)
        window.localStorage.setItem('pushSuccess', '')
    }
})

watch(usePageVisibility(), async () => {
    if (!useUserStore().inLogin) {
        return
    }
    if (!usePageVisibility().isHidden) {
        console.log("页面隐藏后在此显示显示，重新调取成就页数据");
        await loadBeforeMount()
    }
})

watch(()=>useUserStore().inLogin,async ()=>{
    if (useUserStore().inLogin) {
        await loadBeforeMount()
    }
})

const backCallback = async (code) => {
    treeList.value = await taskStore.getTreeList()
    useTreeStore().treeList = treeList.value
    treeList.value = treeList.value.trees
    achieveNum.value = treeList.value.reduce((prev, cur) => {
        return cur.completed ? prev + 1 : prev
    }, 0)
    openNFTDialog()
    useUserStore().setUpData('first_list_two_nft', '1')
    useUserStore().setUpData('first_list_sending_flowers', '1')
    let tree = treeList.value.filter((item) => code.includes(item.code))
    if (tree[0]) {
        toInfo(tree[0])
    }
}
/**
 * 点击成就的花
 * @param item 成就数据对象
 */
const toInfo = async (item) => {
    if (event && event.target instanceof HTMLElement && event.target?.className.includes('give')) {
        return
    }
    // 点击红点后今日不显示
    if (item.nft_exists === false && useUserStore().inLogin) {
        await nftStore.nftAchieveRedDot(item.tree_id) //发送请求今天点击红点的成就了
    }
    // 后端传铸造结果
    let plantDialog = dialog.get('plantDialog')
    plantDialog.beforeClose(async () => {
        eventBus.off('back', backCallback)
        eventBus.on('back', backCallback)
        return false
    })

    plantDialog.show(
        {
            treeCode: item.code,
            tree_id: item.tree_id,
            planted: item.completed,
            foundryed: item.nft_exists,
            nft_group_id: item.nft_group_id,
            owned: item.owned,
            nft_active: item.nft_actived,
            nft_recycled: item.nft_recycled,
            nft_upgrade_notice: item.nft_upgrade_notice,
            nft_recycled_before_upgrade: item.nft_recycled_before_upgrade
        },
        { maskClose: false, maskBgColor: 'rgba(0, 0, 0, 0.4)' }
    )
}

// TODO 改为用业务hook
/** 是否升级nft的提醒 */
let nftUpgradeFlag = false
/** 是否重铸的提醒 */
let nftRecastFlag = false
/** 打开nft相关提示提醒流程 */
const openNFTDialog = () => {
    // 判断是否有植物完成但是未铸造的状态
    const treeList = useTreeStore().treeList.trees
    for (const tree of treeList) {
        // 如果存在种成但是未铸造的状态则显示重铸弹窗
        if(tree.code == 'shuishirong' || tree.code == 'huangzhongmu'){
            continue
        }
        if (tree.completed && !tree.nft_exists) {
            nftRecastFlag = true
        }
        // 存在是否要升级的标记显示升级弹窗
        if (tree.nft_upgrade_notice) {
            nftUpgradeFlag = true
        }
    }

    // 打开nft升级窗口的提醒
    openNFTUpgradeDialog()
}

/** 打开nft升级提示弹窗 */
const openNFTUpgradeDialog = async () => {
    if (!nftUpgradeFlag) {
        // 在不需要显示nft更新弹窗的情况下显示重新铸造的弹窗
        openReCasteDialog()
        return
    }

    // 只弹一次
    const info = useUserStore().userInfo
    if (info.webui_manage_values) {
        const isFirst = info.webui_manage_values.first_nft_upgrade || 0

        // 是否已经出现过nft升级提示的弹窗
        if (isFirst === 1) {
            return
        }
    }

    useUserStore().setUpData('first_nft_upgrade', '1')
    useUserStore().userInfo.webui_manage_values.first_nft_upgrade = 1

    const nftDialog = dialog.get('upgradeNFTDialog')
    nftDialog.on('mask-close', async () => {
        const nftEquityRes = await taskStore.getNFTEquityState()
        // NFT購買權通知
        if (nftEquityRes.newest_cards.length > 0) {
            dialog.get('buyNftDialog').show({ newest_cards: nftEquityRes.newest_cards }, { maskClose: true })
        }
    })
    nftDialog.show({}, {})
}

/** 打开重新铸造弹窗 */
const openReCasteDialog = async () => {
    if (!nftRecastFlag) {
        return
    }

    // 只弹一次
    const info = useUserStore().userInfo
    const isFirst = info.webui_manage_values.first_nft_recaste || 0

    // 是否已经出现过nft升级提示的弹窗
    if (isFirst === 1) {
        return
    }

    useUserStore().setUpData('first_nft_recaste', '1')
    useUserStore().userInfo.webui_manage_values.first_nft_recaste = 1

    const redialog = dialog.get('reCasteDialog')
    redialog.on('mask-close', async () => {
        const nftEquityRes = await taskStore.getNFTEquityState()
        // NFT購買權通知
        if (nftEquityRes.newest_cards.length > 0) {
            dialog.get('buyNftDialog').show({ newest_cards: nftEquityRes.newest_cards }, { maskClose: true })
        }
    })
    redialog.show({}, {})
}
</script>

<style lang="less" scoped>
@import './achievement.less';
</style>
