export interface TypeEventListener {
    addEventListener: (type: string, handler: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions) => void;
    removeEventListener: (type: string, handler: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions) => void;
    [key: string]: any;
}
export declare function useEventListener(scope: TypeEventListener, type: string | string[], handler: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): () => void;
