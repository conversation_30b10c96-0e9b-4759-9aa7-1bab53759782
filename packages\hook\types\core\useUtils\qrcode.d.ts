declare enum QRErrorCorrectLevel {
    L = 1,
    M = 0,
    Q = 3,
    H = 2
}
interface QRCodeOptions {
    width: number;
    height: number;
    colorDark: string;
    colorLight: string;
    correctLevel: QRErrorCorrectLevel;
}
declare class QRCode {
    opt: QRCodeOptions;
    constructor();
    init(opt?: Partial<QRCodeOptions>): this;
    create(text: string, options?: Partial<QRCodeOptions>): string;
}
export default QRCode;
