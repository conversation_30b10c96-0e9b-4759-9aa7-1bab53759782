<template>
    <div class="box">
        <div class="card">
            <img :src="$imgs['task/taskTime.png']" alt="" />
            <p>{{ state.dialog['任務還有一個月就結束啦，快去完成吧'] }}</p>
        </div>
        <div class="know" @click="clickBtn">
            <p>{{ state.dialog.前往減碳任務 }}</p>
        </div>
        <div class="close-icon" @click="close">
            <img src="@/assets/imgs/closeIcon.png" alt="" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { inject } from 'vue'
import { useEventBus, useLang, useDialog } from 'hook'

let emit = defineEmits(['close'])

const { state } = useLang()
const eventBus = useEventBus()
const uuid = inject('uuid')
const clickBtn = () => {
    // 晚一点拉起任务栏
    eventBus.emit('showTaskSheet')
    emit('close')
}
const close = () => {
    emit('close')
}
</script>

<style lang="less" scoped>
.box {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .card {
        padding: 74px 138px 72px 140px;
        background: #ffffff;
        border-radius: 24px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        img {
            width: 264px;
            height: 150px;
            margin-bottom: 40px;
        }

        p {
            font-family: PingFang TC, PingFang TC;
            font-weight: 600;
            font-size: 32px;
            color: #4e5b7e;
            width: 256px;
            text-align: center;
        }
    }

    .know {
        margin-top: 100px;
        min-width: 430px;
        height: 84px;
        background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
        box-shadow: 0px 8px 0px 2px rgba(252, 175, 40, 1), inset 0px 4px 0px 2px rgba(255, 242, 178, 1);
        border-radius: 48px 12px 48px 12px;
        font-size: 36px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #ffffff;
        line-height: 32px;
        text-shadow: 0px 0px 12px #fbac2e;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            width: 40px;
            height: 40px;
            margin-right: 10px;
        }
    }

    .closeBtn {
        margin-top: 44px;
        min-width: 430px;
        height: 84px;
        background: linear-gradient(180deg, #56d3eb 0%, #34aadf 100%);
        box-shadow: 0px 8px 0px 2px #3878b9, inset 0px 4px 0px 2px #8ceff7;
        border-radius: 48px 12px 48px 12px;
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
        font-size: 36px;
        color: #ffffff;
        line-height: 32px;
        text-shadow: 0px 0px 12px #3878b9;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .close-icon {
        position: absolute;
        bottom: -240px;
        width: 56px;
        height: 56px;
        img {
            display: block;
            width: 100%;
            height: 100%;
        }
    }
}
</style>
