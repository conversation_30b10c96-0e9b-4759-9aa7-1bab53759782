<template>
    <div class="box">
        <div class="card">
            <img :src="$imgs['task/taskTimeEnd.png']" v-if="refresh" alt="">
            <img :src="$imgs['task/taskTime.png']" v-else alt="">
            <p>{{ tips }}</p>
        </div>
        <div class="know" @click="clickBtn">
            <img :src="$imgs['task/refresh.png']" alt="" v-if="refresh">
            <p>{{btn}}</p>
        </div>
        <div class="closeBtn" @click="toHome" v-show="refresh" >{{ state.task.返回主頁 }}</div>
        <div class="close-icon" @click="emit('close')" v-show="!refresh">×</div>
    </div>
  </template>

  <script setup lang='ts'>
  import { useDialog, useLang, useRouter,useEventBus } from 'hook';
  import { useTaskStore } from '@/store';
  let emit = defineEmits(['close'])
  const eventBus = useEventBus()
  const { router, currentRoute } = useRouter()
  const {state} = useLang()
  const props = defineProps({
    // 刷新按钮图标
    refresh:{
        default:false,
        type:Boolean
    },
    btn:String,
    tips:String
})

const clickBtn = () => {
    if(props.refresh){
        eventBus.emit('refreshTask')
    }else{
        // 晚一点拉起任务栏
        setTimeout(()=>{
            eventBus.emit('showTaskSheet')
        }, 800)
    }
    emit('close')
}
const toHome = () => {
    eventBus.emit('closeTask')
    emit('close')
}
  </script>

  <style lang='less' scoped>
  .box{
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      .card{
        padding: 74px 138px 72px 140px;
        background: #FFFFFF;
        border-radius: 72px 24px 72px 24px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        img{
            width: 264px;
            height: 150px;
            margin-bottom: 40px;
        }
        p{
            font-family: PingFang TC, PingFang TC;
            font-weight: 600;
            font-size: 32px;
            color: #4E5B7E;
            width: 256px;
            text-align: center;
        }
      }
      .know{
          margin-top: 100px;
          min-width: 430px;
          height: 84px;
          background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
          box-shadow: 0px 8px 0px 2px rgba(252,175,40,1), inset 0px 4px 0px 2px rgba(255,242,178,1);
          border-radius: 48px 12px 48px 12px;
          font-size: 36px;
          font-family: PingFang SC-Bold, PingFang SC;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 32px;
          text-shadow: 0px 0px 12px #FBAC2E;
          display: flex;
          align-items: center;
          justify-content: center;
          img{
            width: 40px;
            height: 40px;
            margin-right: 10px;
          }
      }
      .closeBtn{
          margin-top: 44px;
          min-width: 430px;
          height: 84px;
          background: linear-gradient(180deg, #56D3EB 0%, #34AADF 100%);
          box-shadow: 0px 8px 0px 2px #3878B9, inset 0px 4px 0px 2px #8CEFF7;
          border-radius: 48px 12px 48px 12px;
          font-family: PingFang SC, PingFang SC;
          font-weight: bold;
          font-size: 36px;
          color: #FFFFFF;
          line-height: 32px;
          text-shadow: 0px 0px 12px #3878B9;
          display: flex;
          align-items: center;
          justify-content: center;
      }
      .close-icon{
        position: absolute;
        bottom: -240px;
        width: 56px;
        height: 56px;
        font-size: 60px;
        color: white;
        text-align: center;
        line-height: 54px;
        border: 4px solid white;
        border-radius: 50%;
      }
  }
  </style>
