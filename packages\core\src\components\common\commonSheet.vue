<template>
    <ActionSheet :show="showSheet">
        <div class="sheet-content">
            <!--关闭按钮的图标被遮挡了一部分-->
            <div class="close" @click="clickClose"></div>
            <div class="title">
                <slot name="title"></slot>
            </div>
            <div class="inside" data-isScroller="true">
                <slot name="content"></slot>
            </div>
        </div>
    </ActionSheet>
</template>

<script lang="ts" setup>
import { ActionSheet, ConfigProvider } from 'vant'
const emit = defineEmits(['close'])
defineProps({
    /** 是否显示动作面板 */
    showSheet: {
        type: Boolean,
        require: true
    }
})
// vant样式修改
const themeVars = {}
const clickClose = () => emit('close')
</script>

<style lang="less" scoped>

.sheet-content {
    padding-top: 30px;
    .close {
        position: absolute;
        right: 34px;
        top: -26px;
        width: 56px;
        height: 56px;
        background: url('@assets/TT/Icon/xx.png') no-repeat;
        background-size: 100% 100%;
    }

    .inside {
    }
}
</style>
