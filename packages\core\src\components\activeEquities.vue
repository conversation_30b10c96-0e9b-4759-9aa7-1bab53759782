<template>
    <!-- 活动 -->
  <div class="activeEquities">
    <div class="activeEquities-box">
        <div class="title">
            <span>{{ state.temporarilyStore.權益生效中 }}</span>
        </div>
        <div class="list">
            <div class="item" v-for="item in list">
                <div class="icon">
                    <img :src="$imgs[`NTFDetail/icon/${item.code}.png`]" alt="">
                </div>
                <div class="text">
                    <div class="top">
                        {{ state.nft.flowerName[item.code] }}{{ state.temporarilyStore.權益 }}
                    </div>
                    <div class="time">
                        {{ item.end_time }}{{ state.temporarilyStore.到期 }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="btn" @click="emit('close')">
        {{ state.temporarilyStore.知道啦 }}
    </div>
  </div>
</template>

<script setup lang='ts'>
import { useLang, useRouter } from 'hook'
import { ref, onBeforeMount } from 'vue'
let emit = defineEmits(['close'])
const { state } = useLang()
interface itemType {
    code: string
    end_time: string
    start_time: string
}
const props = defineProps<{
    list:itemType[]
}>()
console.log(props.list);

</script>

<style lang='less' scoped>
.activeEquities{
    .activeEquities-box{
        width: 542px;
        background: #FFFFFF;
        border-radius: 24px 24px 24px 24px;
        overflow: hidden;
        padding-bottom: 24px;
        .title{
            margin-top: 48px;
            text-align: center;
            font-weight: 600;
            font-size: 32px;
            color: #4E5B7E;
            line-height: 44px;
        }
        .list{
            max-height: 630px;
            display: flex;
            flex-direction: column;
            overflow: scroll;
            .item{
                display: flex;
                padding: 0 56px;
                margin-top: 24px;
                .icon{
                    width: 84px;
                    height: 84px;
                    margin-right: 24px;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
                .text{
                    display: flex;
                    flex-direction: column;
                    .top{
                        font-size: 28px;
                        color: #4E5B7E;
                        line-height: 40px;
                    }
                    .time{
                        font-weight: 400;
                        font-size: 24px;
                        color: #A7AEC3;
                        line-height: 40px;
                    }
                }
            }
        }
    }
    .btn{
        width: 430px;
        height: 84px;
        background: linear-gradient( 180deg, #FDDD3E 0%, #FBB629 100%);
        box-shadow: 0px 8px 0px 2px #FCAF28, inset 0px 4px 0px 2px #FFF2B2;
        border-radius: 48px 12px 48px 12px;
        margin: 72px auto 0;
        font-weight: bold;
        font-size: 36px;
        color: #FFFFFF;
        line-height: 32px;
        text-shadow: 0px 0px 12px #FBAC2E;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>