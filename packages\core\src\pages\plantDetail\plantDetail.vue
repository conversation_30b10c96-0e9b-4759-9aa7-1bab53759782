<template>
    <div v-if="show" class="plantDetail">
        <navComponent :returnType="2" :showTransmit="false"/>
        <div class="background">
            <img class="img" src="@imgs/plantDetail/background.png" />
            <div class="tree">
                <img :class="`${key}`" :src="$imgs[`TT/state/${key}/s9.png`]" alt="">
            </div>
            <div class="right" @click="next()" v-if="showRight() && isIds"></div>
            <div class="left" @click="previous()" v-if="showLeft() && isIds"></div>
        </div>
        
        <div class="intro" :class="{ 'vip-img': treeIsVIP }">
            <div class="plant-tips" v-if="isIds && hasPlanted">
                <img :src="$imgs[`${useTreeStore().nowTree.tree.tree_id == treeId ? 'planting' : 'planted'}.png`]" class="plant-img" alt="">
                <p>
                    <span v-if="useTreeStore().nowTree.tree.tree_id == treeId">{{ state.friend.正在种植 }}</span>
                    <span v-else>{{ state.friend.已种成 }}</span>
                </p>
            </div>

            <div class="upStrategy" @click="router.push({name:'upStrategy', query:{'hideNavigationBar': 'true'}})">
                <img class="strategyImg" :src="$imgs['strategyImg.png']" alt="">
                <p>{{state.home.升级攻略}}</p>
                <img class="jiantou" :src="$imgs['j2.png']" alt="">
            </div>
            <div class="title">
                <div class="name"><div>{{ $lang.plant[`${key}`].plantName }}</div><div v-if="treeIsVIP" class="vip" :class="{'vip_en':lang == 'en'}">{{ $lang.Postpaid }}</div></div>
                <div class="target">
                    {{state.detail.目标减排值 + numberToThousands(energy_goal) }}
                </div>
            </div>

            <div class="next">
                <div class="bio">{{ $lang.plant[`${key}`].plantBiologicalKind }}</div>

                <div class="kind" :class="{ 'vip-kind': treeIsVIP }">{{ $lang.plant[`${key}`].plantKind }}</div>
            </div>

            <div class="basic">
                {{ $lang.plant[`${key}`].plantInfo }}
            </div>

            <div class="award-title">
                <img class="img" src="@imgs/plantDetail/award-icon.png" />

                <div class="text">{{state.detail.种植奖励}}</div>
            </div>

            <div class="prop">
                <div v-for="item in propAward" :key="item.name" class="item" @click="showProp(item)">
                    <div class="icon">
                        <img :src="$imgs[`props/${item.propType}/${item.name}.png`]" />
                    </div>
                    <div class="text">{{ state.propsReward[item.name].propName }}</div>
                </div>
            </div>

            <div class="award-title">
                <img class="img" :src="$imgs['vippicon2.png']" />

                <div class="text">{{state.联动奖赏.後付限定獎賞}}</div>
            </div>

            <div class="prop">
                <div v-for="item in propAward_v" :key="item.name" class="item" @click="showVip(item)">
                    <div class="icon">
                        <img :src="$imgs[`props/vip/${item.name}/1.png`]" />
                    </div>

                    <div class="text">{{ state.propsReward[item.name].propName }}</div>
                </div>
            </div>
        </div>

        <div class="info">
            <div v-for="(item, index) in title" :key="item" class="item">
                <div class="title-box">
                    <div class="icon"></div>

                    <div class="title">{{ state.detail[item] }}</div>
                </div>

                <div class="text">{{ $lang.plant[`${key}`][`${ind[index]}`] }}</div>
            </div>
        </div>
        
        <div v-if="!useUserStore().inLogin" class="bottom">
            <div class="btn login" @click="goToLogin">
                <div class="text">{{ state.home.登入并种植 }}</div>
            </div>
        </div>
        <div v-else-if="!hasPlanted" class="bottom">
            <div class="btn" :class="canPlant && (treeIsVIP ? useUserStore().isVip : true)  ? 'can' : 'cannot'" @click="plant">
                <div class="text">{{state.home.开始种植}}</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onBeforeMount,watch,computed } from 'vue'
import { logEventStatistics } from '@via/mylink-sdk'
import { useLang, useRouter, useDialog, useToast, useStorage, useEnvConfig, useLoading } from 'hook'
import { numberToThousands } from '@unity/unity'
import { useTreeStore, useUserStore, useTaskStore } from '@/store'
import PropDialog from '@/components/propDialog.vue'
import confirmDialog from '@/components/confirmDialog.vue'
import successPlant from '@/components/successPlant.vue'
import navComponent from '@/components/navComponent.vue';
import notVipDialog from '@/components/notVipDialog.vue';
import gifPropDialog from '@/components/gifPropDialog.vue'
import { imgs } from '@assets/imgs'

const envConfig = useEnvConfig()
const storage = useStorage()
const { loading } = useLoading()
const { toast } = useToast()
const { state,lang } = useLang()
const { currentRoute, router } = useRouter()
const { getTreeConfig, getTreeConfigShare } = useTreeStore()
const { plantTree } = useTaskStore()
let { isVip } = useUserStore()
console.log('isVip: ' + isVip)

const title = ['赏花地点', '植物小科普', '分布区域', '生态资料', '保育情况', '内容來源']
const ind = [
    'plantViewLocation',
    'plantScience',
    'plantDistributionArea',
    'plantEcologicalData',
    'plantConservation',
    'plantContentSource'
]

const dialog = useDialog({ PropDialog, confirmDialog, successPlant, notVipDialog, gifPropDialog })

const show = ref(false)
const key = ref('')
const canPlant = ref(true)
const treeIsVIP = ref(false)
const propAward = ref<Array<any>>([])
const propAward_v = ref<Array<any>>([])
const hasPlanted = ref(false)
const completed = ref(true)
const energy_goal = ref(0)
const list = ref<any>([])
const treeId = ref(Number(currentRoute?.params.id))
const isIds = computed(()=>{
    let ids:any = []
    list.value.forEach((item:any) => {
        if (item) {
            ids.push(item.tree_id)
        }else{
            ids.push(item)
        }
    });
    return ids.filter((item)=>item == treeId.value).length
})
const next = ()=>{
    let ids:any = []
    list.value.forEach((item:any) => {
        if (item) {
            ids.push(item.tree_id)
        }else{
            ids.push(item)
        }
    });
    console.log(ids.indexOf(treeId.value)+1,ids.length);
    if (ids.indexOf(treeId.value)+1 >= ids.length) {
        return
    }
    treeId.value = ids[ids.indexOf(treeId.value)+1]
}

const showRight = ()=>{
    let ids:any = []
    list.value.forEach((item:any) => {
        console.log(item);
        
        if (item) {
            ids.push(item.tree_id)
        }else{
            ids.push(item)
        }
    });
    if (ids.indexOf(treeId.value)+1 >= ids.length) {
        return false
    }
    return true
}

const previous = ()=>{
    let ids:any = []
    list.value.forEach((item:any) => {
        if (item) {
            ids.push(item.tree_id)
        }else{
            ids.push(item)
        }
    });
    console.log(ids.indexOf(treeId.value)+1,ids.length);
    if (ids.indexOf(treeId.value)-1 < 0) {
        return
    }
    treeId.value = ids[ids.indexOf(treeId.value)-1]
}

const showLeft = ()=>{
    let ids:any = []
    list.value.forEach((item:any) => {
        if (item) {
            ids.push(item.tree_id)
        }else{
            ids.push(item)
        }
    });
    if (ids.indexOf(treeId.value)-1 < 0) {
        return false
    }
    return true
}

watch(treeId,()=>{
    init()
})

onBeforeMount(() => {
    //查看植物詳情訪問量
    logEventStatistics('Mygarden_information_page')
    init()
})

const init = () => {
    list.value = useTreeStore().treeList.trees.filter((item:any)=>item.completed)
    list.value.unshift(useTreeStore().nowTree.tree)
    new Promise(async(reslove, reject) => {
        let res:any
        if(useUserStore().inLogin){
            res = await getTreeConfig(treeId.value)
        }else{
            res = await getTreeConfigShare(treeId.value)
        }
        console.log(currentRoute,'idddddddddddd');
        reslove(res)
    }).then((res) => {
        propAward.value = []
        propAward_v.value = []
        key.value = res.code
        treeIsVIP.value = res.req_vip || false
        canPlant.value =  !res.completed
        completed.value = res.completed
        energy_goal.value = res.target_energy_total
        res.level_configs.forEach((val: any) => {
            if (val.reward_items.length) {
                if(typeof state.propsReward[val.reward_items[0]] == 'undefined')
                    return
                propAward.value.push(
                    ...val.reward_items.map((e: string) => {
                        return Object.assign(
                            { name: e, level: val.level },
                            state.propsReward[e]
                        )
                    })
                )
                propAward_v.value.push(
                    ...val.vip_reward_items.map((e: string) => {
                        return Object.assign(
                            { name: e, level: val.level },
                            state.propsReward[e]
                        )
                    })
                )
            }
        })
        if (storage.customLoad(`via:system:Forest ${envConfig.RUN_ENV}-${useUserStore().phone}-HasPlanted`)) {
            hasPlanted.value =
                storage.customLoad(`via:system:Forest ${envConfig.RUN_ENV}-${useUserStore().phone}-HasPlanted`).v == true ? true : false
        }
        show.value = true
        console.log(propAward.value, propAward_v.value)
    })
}

const goToLogin = async() => {
    loading('open')
    await useUserStore().login()
    if(useUserStore().inLogin){
        let info = await useTaskStore().getInfo()
        useUserStore().changeVip(info.user.vip)
        isVip = info.user.vip
        if(info.tree){//种树了
            // router.replace({ name: 'home', query: {"hideNavigationBar": 'true'}})
            router.go(-2)
        }else{
            plant()
        }
        console.log(info)
    }
    init()
    loading('close')
}

const showProp = (item: any) => {
    dialog.get('PropDialog').show({ 
        isInfo: true, 
        propInstance: Object.assign({} ,item, { icon: imgs[`props/${item.propType}/${item.name}.png`] }), 
        noIndex: item.level 
    })
}

const showVip = (item) => {
    console.log(item)
    let obj = Object.assign({ code: item.name }, item)
    dialog.get('gifPropDialog').show({
        type: 1,
        isInfo: true, 
        propInstance: obj,
        noIndex: item.level 
    })
}

const plant = () => {
    console.log(canPlant.value, 'canPlant')
    console.log(treeIsVIP.value, 'treeIsVIP')
    console.log(isVip, 'isVip')

    if(canPlant.value && (treeIsVIP.value == isVip || isVip)){
        let confirmDlg = dialog.get('confirmDialog')
        confirmDlg.show({ plantName: state.plant[`${key.value}`].plantName }, { maskClose:false })
        confirmDlg.on('cancel', () => cancel(confirmDlg))
        confirmDlg.on('confirm', () => confirm(confirmDlg))
    }else{
        if (treeIsVIP.value && isVip == false) 
            // toast(state.dialog.只有VIP会员可以种.replace("{x}", state.plant[key.value].plantName))
            //  dialog.get('notVipDialog').show({treeCode: key.value}, { maskClose:false })
            toast(state.toast.洋紫荊只限月費服務客戶種植)
        else if(completed.value) toast(state.dialog.你已经种过x啦.replace("{x}", state.plant[key.value].plantName))
        return
    }
}

const cancel = (confirmDlg: any) =>{
    confirmDlg.close()
} 

const confirm = (confirmDlg: any) =>{
    confirmDlg.close()
    plantTree(treeId.value)
    .then((res) => {
        useTreeStore().changeHasPlanted(true)
        useTreeStore().changePlanted(true)
        let successPlant = dialog.get('successPlant')
        successPlant.show({ plantName: state.plant[`${key.value}`].plantName, treeCode: key.value }, { maskClose:false })
        successPlant.on('toHome', () => {
            successPlant.close()
            router.go(-2)
        })
    })
    .catch((err) => {
        toast(state.netError)
    })
} 

</script>

<style lang="less" scoped>
@import './plantDetail.scss';
</style>
