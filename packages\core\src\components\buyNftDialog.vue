<template>
        <div class="box">
            <div class="content">
                <div class="title">{{ state.dialog.已到賬 }}</div>
                <div class="warp">
                    <div class="item" v-for="(card, index) in props.newest_cards" :key="index">
                        <img :src="$imgs[`NTFDetail/icon/${card.code}.png`]" alt="" />
                        <div class="info">
                            <div class="name">【{{state.nft.flowerName[card.code]}}】{{ state.nft.權益包 }}</div>
                            <div class="num">X{{ card.count }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="knowbox">
                <div class="know" style="margin-top: 36px" @click="toRouter('package')">
                    {{ state.shopNft.查看道具 }}
                </div>
                <div class="know" @click="goHome">
                    {{ state.shopNft.去MyHome佈置植物裝飾 }}
                </div>
                <div class="tips">
                    {{ state.shopNft.若已佈置裝飾或擁有富貴竹及桃花NFT請忽略此按鈕 }}
                </div>
            </div>
            <button class="btn" @click="emit('close')"></button>
        </div>
</template>

<script setup lang="ts">
import { getAppVersion, logEventStatistics } from '@via/mylink-sdk'
import { useDialog, useEnvConfig, useLang } from 'hook'
import updateDialog from '@/components/updateDialog.vue'
import router from '@/router'
import { onMounted, onUnmounted } from 'vue';
import { useTaskStore } from '@/store';
const {lang} = useLang()
const dialog = useDialog({
    updateDialog
})
const { state } = useLang()
let emit = defineEmits(['close'])
let isPat = useEnvConfig().RUN_ENV == 'production' || useEnvConfig().RUN_ENV == 'beta' ? true : false

const props = defineProps({
    newest_cards: Array<{
        code: string;
        count: number;
    }>
})


const goHome = async() => {
    logEventStatistics('garden_go_myhome')
    const version = getAppVersion()
    const ver = Number(version.split('.').join(''))
    if (ver >= 1000) {
        window.location.href = `openhkhshlogin://cmcchkhsh://metaverse?url=myhome`
        emit('close')
        return
    } else {
        dialog.get('updateDialog').show({}, { maskClose: false })
        emit('close')
        return
    }
}

const toRouter = async (url: string, obj: object = {}) => {
    emit('close')
    router.push({
        path: url,
        query: {
            hideNavigationBar: 'true',
            ...obj
        }
    })
}

onMounted(async () => {
    await useTaskStore().nftNewestOff()
})

const goRule = () => {
    window.location.href = `https://cdn.mylinkapp.hk/via/entry/vcqiGDnstPtzIcqsWJUzIbFL.html?lang=${lang.value}#/main`
    emit('close')
}
</script>

<style lang="less" scoped>
.box {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100vw;
    position: relative;
    .content {
        width: 542px;
        background: #ffffff;
        border-radius: 24px 24px 24px 24px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 24px;
        .title {
            width: 420px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 32px;
            color: #4e5b7e;
            line-height: 44px;
            text-align: center;
        }
        .warp{
            max-height: 420px;
            overflow: scroll;
        }
        .item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 24px;
            img {
                width: 84px;
                height: 84px;
                border-radius: 8px 8px 8px 8px;
                border: 2px solid rgba(0, 0, 0);
            }
        }
        .info {
            display: flex;
            flex-direction: column;
            align-items: start;
            margin-left: 24px;
            .name {
                width: 318px;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 24px;
                color: #4e5b7e;
                line-height: 40px;
                text-align: left;
            }
            .num {
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 28px;
                color: #4e5b7e;
                line-height: 40px;
                text-align: left;
                margin-top: 16px;
            }
        }
    }
    .knowbox {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        > .know {
            margin-bottom: 40px;
        }
        > .know:nth-child(2) {
            background: linear-gradient(180deg, #56d3eb 0%, #34aadf 100%);
            box-shadow: 0px 8px 0px 2px rgba(56, 120, 185, 1), inset 0px 4px 0px 2px rgba(140, 239, 247, 1);
            margin-bottom: 0px;
        }
    }
    .tips{
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        line-height: 28px;
        text-align: center;
        padding: 20px 0;
    }
    .know {
        min-width: 430px;
        height: 84px;
        background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
        box-shadow: 0px 8px 0px 2px rgba(252, 175, 40, 1), inset 0px 4px 0px 2px rgba(255, 242, 178, 1);
        border-radius: 48px 12px 48px 12px;
        font-size: 36px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #ffffff;
        line-height: 32px;
        text-shadow: 0px 0px 12px #fbac2e;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .btn {
        position: absolute;
        width: 56px;
        height: 56px;
        background: url('@assets/imgs/closeIcon.png') no-repeat;
        background-size: 100% 100%;
        border: none;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -100px;
    }
}
</style>
