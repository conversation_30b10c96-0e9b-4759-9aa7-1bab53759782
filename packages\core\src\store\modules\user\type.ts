export interface userType {
    inLogin: boolean //是否登入
    isVip: boolean //是否是vip,
    isGuild: boolean //是否完成新手引导,
    isSlash: boolean //是否Slash用户,
    name: string
    headLogo: string //头像地址
    frameUrl: string //挂饰
    phone: string //电话号码
    usingCard: boolean
    firstAct: boolean
    day_limit: number
    isHK: number
    userInfo: Record<string, any>
    waterNum: number
    third_id: string
    amm_vip: boolean // link着数会员: 只有是后付才有可能为true
}