import { BaseLinkVue } from '../../base/baseLikeVue'
import { App, Component, Directive, Plugin } from 'vue'
import { useDialog } from '.'

const { app } = useDialog()

class Dialog extends BaseLinkVue {
  use(plugin: Plugin, ...options: any[]) {
    app.use(plugin, options)
    return this
  }

  directive(name: string, directive: Directive) {
    app.directive(name, directive)
    return this
  }

  component(name: string, component: Component) {
    app.component(name, component)
    return this
  }
}

export default new Dialog()
