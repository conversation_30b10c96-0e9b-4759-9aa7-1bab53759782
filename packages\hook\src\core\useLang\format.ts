import { useUtils } from '../useUtils'

const { isObject } = useUtils()

export default class BaseFormatter {
  _caches: { [key: string]: Array<Token> }

  constructor() {
    this._caches = Object.create(null)
  }

  interpolate(message: string, values: any): string {
    if (!values) {
      return message
    }
    let tokens: Array<Token> = this._caches[message]
    if (!tokens) {
      tokens = parse(message)
      this._caches[message] = tokens
    }
    return compile(tokens, values).join('')
  }
}

type Token = {
  type: 'text' | 'named' | 'list' | 'unknown'
  value: string
}

const RE_TOKEN_LIST_VALUE = /^(?:\d)+/
const RE_TOKEN_NAMED_VALUE = /^(?:\w)+/

export function parse(format: string): Array<Token> {
  const tokens: Array<Token> = []
  let position = 0

  let text = ''
  while (position < format.length) {
    let char: string = format[position++]
    if (char === '{') {
      if (text) {
        tokens.push({ type: 'text', value: text })
      }

      text = ''
      let sub = ''
      char = format[position++]
      while (char !== undefined && char !== '}') {
        sub += char
        char = format[position++]
      }
      const isClosed = char === '}'

      let type: 'text' | 'named' | 'list' | 'unknown' = 'unknown'
      if (RE_TOKEN_LIST_VALUE.test(sub)) {
        type = 'list'
      } else if (isClosed && RE_TOKEN_NAMED_VALUE.test(sub)) {
        type = 'named'
      }
      tokens.push({ value: sub, type })
    } else if (char === '%') {
      if (format[position] !== '{') {
        text += char
      }
    } else {
      text += char
    }
  }

  text && tokens.push({ type: 'text', value: text })

  return tokens
}

export function compile(tokens: Array<Token>, values: Object | Array<any>): Array<any> {
  const compiled: Array<any> = []
  let index = 0

  const mode: string = Array.isArray(values) ? 'list' : isObject(values) ? 'named' : 'unknown'
  if (mode === 'unknown') {
    return compiled
  }

  while (index < tokens.length) {
    const token: Token = tokens[index]
    switch (token.type) {
      case 'text':
        compiled.push(token.value)
        break
      case 'list':
        compiled.push(values[parseInt(token.value, 10)])
        break
      case 'named':
        if (mode === 'named') {
          compiled.push(values[token.value])
        }
        break
      case 'unknown':
        break
    }
    index++
  }

  return compiled
}
