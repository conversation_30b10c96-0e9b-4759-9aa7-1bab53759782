<template>
    <div class="package">
        <navComponent :returnType="2" :showTransmit="false" />
        <!--背景-->
        <div class="img" v-if="tabIndex != 2">
            <img v-if="isGifTab" :src="$imgs['vipBG.png']" />
            <img v-else src="@imgs/package/top-background.png" />
            <!-- 场景道具 -->
            <sceneProps v-if="!isGifTab" class="sceneProps" />
        </div>
        <!-- 小狸猫图片-->
        <div v-if="isGifTab" class="BearImg">
            <img
                :src="
                    usePropStore().propsConfig['GIF']
                        ? $imgs[`props/vip/${usePropStore().propsConfig['GIF']}/2.png`]
                        : $imgs['bear1.png']
                "
                alt=""
            />
        </div>
        <!-- 树图片-->
        <div v-if="tree.level && !isGifTab && tabIndex != 2" class="tree-entity">
            <tree-entity
                class="tree-item"
                :shake="false"
                :level="tree.level"
                :prop="wearList"
                :treeCode="useTreeStore().treeCode"
            ></tree-entity>
            <img class="nitu" v-if="tree.level > 0 && tree.level <= 2" :src="$imgs[`TT/state/泥土.png`]" alt="" />
            <img
                class="yinying"
                v-if="tree.level > 2 && useTreeStore().treeCode != 'xiuqiuhua'"
                :src="$imgs[`TT/state/${useTreeStore().treeCode}/阴影.png`]"
                alt=""
            />
            <img
                class="yinying xiuqiu"
                v-if="tree.level > 2 && useTreeStore().treeCode == 'xiuqiuhua'"
                :src="$imgs[`TT/state/${useTreeStore().treeCode}/阴影.png`]"
                alt=""
            />
        </div>
        <div class="shopping-layer" v-if="tabIndex == 2"></div>
        <div class="positionTab">
            <div class="kindTab" :class="{ 'shopping-kindTab': tabIndex == 2 }">
                <!-- <div class="prompt">
                    <div class="promptText">
                        <img :src="$imgs['package/horn.png']" alt="">
                        <template v-if="!useUserStore().isVip">
                            <p>{{ state.tips.推出減碳商城 }}</p>
                        </template>
                        <template v-else>
                        <p v-if="!firstEnter && !tips" :ref="firstPromptText" class="firstPromptText1">{{ state.tips.會員道具提示 }}</p>
                        <p v-else>{{ state.tips.推出減碳商城 }}</p>
                        </template>
                    </div>
                    <div class="triangle"><img :src="$imgs['package/triangle2.png']" alt=""></div>
                </div> -->
                <!-- <div @click="changeTab(0)" :class="`kinditem l${tabIndex == 0 ? '2' : '1'}`"> -->
                <div class="tab-choose">
                    <div :class="`kinditem l${tabIndex == 0 || tabIndex == 1 ? '2' : '1'}`" @click="changeTab(0)">
                        <img v-if="tabIndex == 0 || tabIndex == 1" class="tm l" :src="$imgs['Tengman.png']" alt="" />
                        MyGarden
                    </div>
                </div>
                <!-- <div v-if="!useUserStore().isSlash" @click="changeTab(1)" :class="`kinditem r${tabIndex == 1 ? '2' : '1'}`">
                    <img class='tm r' v-if="tabIndex == 1" :src="$imgs['Tengman.png']" alt="">
                    {{state.package_.会员成长}}
                    <div v-if="cardPoint1" class="redpoint"></div>
                </div> -->
            </div>
            <!-- 关闭按钮 -->
            <!-- <img class="close" src="@imgs/package/close.png" @click="closeAndGoHome" /> -->
            <div class="bktitle">
                <div class="mgTitle" v-show="tabIndex == 0">
                    <img class="bglong" :src="$imgs['bklongtitle.png']" alt="" />
                    <div @click="changeMg(0)" :class="`jj ${kindIndex == 0 ? 'cxk1' : 'cxk'}`">
                        <img v-if="kindIndex == 0" :src="$imgs['bktitle.png']" alt="" />
                        <p>
                            <img :src="$imgs[`${kindIndex == 0 ? 'proicon2' : 'proicon1'}.png`]" alt="" />
                            {{ state.package_.装饰道具 }}
                        </p>
                    </div>
                    <div
                        @click="changeMg(1)"
                        :class="`jj ${kindIndex == 1 ? 'cxk1' : 'cxk'} ${cardPoint ? 'cardPoint' : ''}`"
                    >
                        <img v-if="kindIndex == 1" :src="$imgs['bktitle.png']" alt="" />
                        <p>
                            <img :src="$imgs[`${kindIndex == 1 ? 'starbang2' : 'starbang1'}.png`]" alt="" />
                            {{ state.package_.功能道具 }}
                        </p>
                    </div>
                </div>
                <div class="mgTitle" v-show="tabIndex == 1">
                    <!-- <img :src="$imgs['bktitle.png']" alt="">
                    <p>{{state.package_.装饰道具}}</p> -->
                    <img class="bglong" :src="$imgs['bklongtitle.png']" alt="" />
                    <div @click="changeMg(0)" :class="`jj ${kindIndex == 0 ? 'cxk1' : 'cxk'}`">
                        <img v-if="kindIndex == 0" :src="$imgs['bktitle.png']" alt="" />
                        <p>
                            <img :src="$imgs[`${kindIndex == 0 ? 'proicon2' : 'proicon1'}.png`]" alt="" />
                            {{ state.package_.装饰道具 }}
                        </p>
                    </div>
                    <div
                        @click="changeMg(1)"
                        :class="`jj ${kindIndex == 1 ? 'cxk1' : 'cxk'} ${
                            cardPoint && tabIndex == 0 ? 'cardPoint' : ''
                        }`"
                    >
                        <img v-if="kindIndex == 1" :src="$imgs['bktitle.png']" alt="" />
                        <p>
                            <img :src="$imgs[`${kindIndex == 1 ? 'starbang2' : 'starbang1'}.png`]" alt="" />
                            {{ state.package_.功能道具 }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="title-decorator" v-if="isShowTitleDecorator">
            <div class="inside">
                <div class="mask"></div>
                <headBox :totalEnergy="numberToThousands(useTotalEnergy_cpu)"></headBox>
            </div>
        </div>
        <div class="container" v-show="isShow">
            <div class="tab-kind">
                <div class="tab-k-container">
                    <div v-for="(item, index) in state.package.types[titleLists[mgindex]][kindIndex]" :key="index">
                        <p
                            :class="`tab-item ${chooseKind == index ? 'tab-item1' : ''}`"
                            @click="
                                changeChoose(
                                    index,
                                    flag_on_receive_item_kinds.find(
                                        (val) => val == state.package.types['我的花园'][kindIndex][index]
                                    )
                                )
                            "
                        >
                            {{ state.package_[item] }}
                            <span
                                v-if="
                                    flag_on_receive_item_kinds.find(
                                        (val) =>
                                            val == state.package.types['我的花园'][kindIndex][index] ||
                                            val == 'xiaolimao'
                                    )
                                "
                            ></span>
                        </p>
                    </div>
                    <img
                        @click="showLjt"
                        v-if="kindIndex == 1"
                        class="ljt"
                        :src="$imgs[`${openLjt ? 'ljt1' : 'ljt'}.png`]"
                        alt=""
                    />
                </div>
                <div class="clearTab" v-if="openLjt">
                    <p>{{ state.联动奖赏.我的失效道具 }}</p>
                    <div @click="cleanCard" class="clearBtn">{{ state.联动奖赏.清空 }}</div>
                </div>
            </div>

            <!-- <p>{{propList[nowTitle][state.package.type[kindIndex]?.[chooseKind]]}}</p> -->
            <PropItem
                ref="propItemRef"
                v-if="isShow && tabIndex != 2"
                class="PropItem"
                :list="propList[titleLists[tabIndex]]"
                :tabIndex="tabIndex"
                :kindIndex="kindIndex"
                :chooseKind="chooseKind"
                :max-wear="tree.maxWear"
                :wearList="wearList"
                :cardNum="cardNum"
                :isUsingCard="useUserStore().usingCard"
                :curTabName="state.package.types['我的花园'][tabIndex][chooseKind]"
                @updata:chooseKind="chooseKind = $event"
                @updata:isUsingCard="updataUse"
                @toLog="toLog"
                @init="init"
                @toMyCard="toMyCard"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, reactive, onMounted, onBeforeMount, onBeforeUnmount, getCurrentInstance, computed } from 'vue'
import {
    useRouter,
    useWindow,
    useLang,
    useLoading,
    useDayjs,
    useEventBus,
    useStorage,
    useEnvConfig,
    useDialog,
    useToast
} from 'hook'
import { usePropStore, useTaskStore, useTreeStore, useUserStore, useFriendStore } from '@/store'
import { numberToThousands, nativeBackClick, upShare } from '@unity/unity'
import PropItem from '@/components/package/PropItem.vue'
import TreeEntity from '@/components/treeEntity.vue'
import sceneProps from '@/components/sceneProps.vue'
import navComponent from '@/components/navComponent.vue'
import cleanDialog from '@/components/cleanDialog.vue'
import { logEventStatistics } from '@via/mylink-sdk'
import { nextTick } from 'vue'
// 头像组件
import headBox from '@/pages/home/<USER>/headBox.vue'
const { mQuery, query, hQuery } = useWindow()
const taskStore = useTaskStore() //api
const eventBus = useEventBus()
const envConfig = useEnvConfig()
const storage = useStorage()
const friendStore = useFriendStore()
const { dayjs } = useDayjs()
const { router, currentRoute } = useRouter()
const { state: langState, lang } = useLang()
// 由于下面的代码会直接对state内的数据进行直接操作，所以需要对原始数据进行拷贝
const state = JSON.parse(JSON.stringify(langState))
const { getPropList, getInfo, getShareList } = useTaskStore()
const { loading } = useLoading()
// const titleLists = ['我的花园', '会员成长', '功能道具']
// 二级tab
const titleLists = ['我的花园', '会员成长']
const dialog = useDialog({
    cleanDialog
})
const { toast } = useToast()

const refList: any = ref([])
const propList = ref({})
const nowTitle = ref(titleLists[0])
const typeList = ref(state.package.type[0])
const showSelect = ref(false)
const choose = ref('')
const isShow = ref(false)
// let treeCode = ref(useTreeStore().treeCode)
let tree: any = ref({})
let wearList = ref<Array<any>>([]) //装饰列表
let tabIndex = ref(mQuery.tabIndex == '2' ? 2 : 0) //第一个的tab 0mygarden 1会员成长 2 减碳商城
let mgindex = ref(0) //花园的tab 0装饰 1功能
let kindIndex = ref(0) //tab种类 0装饰 1功能 2会员
let chooseKind = ref(0) //tab种类指针
let cardPoint = ref(false) //我的花园盲盒功能道具小红点
let cardPoint1 = ref(false) //会员成长小红点
let propItemRef = ref(null)
let firstEnter = ref<Boolean>(true) //首次进入
let firstPromptText = ref()

let cardNum = ref({
    //0步数 1减碳值
    0: 0,
    1: 0
})
let openLjt = ref(false) //打开垃圾桶

/** 判断是否进入小狸猫界面 */
let isGifTab = computed(() => {
    // 当前页签为1并且页签名称为'会员成长'时，进入小狸猫界面
    if (tabIndex.value == 1 && state.package.types['我的花园'][0][chooseKind.value] === '会员成长') {
        return true
    } else {
        return false
    }
})

/** 用户信息 */
let userInfo = null
/** 判断是否展示标题装饰 */
const isShowTitleDecorator = computed(() => {
    // 当前页签为1并且页签名称为'会员成长'时，进入小狸猫界面
    if (tabIndex.value == 0 && state.package.types['我的花园'][tabIndex.value][chooseKind.value] === '标题装饰') {
        return true
    } else {
        return false
    }
})

// TODO 这里可以同首页的状态合并到一个pina状态中
/** 获取用户总能量 */
let useTotalEnergy_cpu = computed(() => {
    return (friendStore.isOwn ? info?.user?.energy_total : info?.friend?.energy_total) || 0
})

const flag_on_receive_item_kinds_list = {
    item: '挂饰',
    chanzi: '铲子',
    weilan: '围栏',
    dengzhu: '灯柱',
    daocaoren: '稻草人',
    mutouche: '木头车',
    penshuichi: '喷水池',
    zhuozi: '桌子',
    yizi: '椅子',
    xiaolimao: '会员成长',
    title: '标题装饰'
    // x2:'双倍卡',
    // energy:'减碳卡'
}

let flag_on_receive_item_kinds = ref<String[]>([])

function getRedRadio(flag_on_receive_item_kinds) {
    for (const key in flag_on_receive_item_kinds_list) {
        let index = flag_on_receive_item_kinds.indexOf(key)
        if (index != -1) {
            flag_on_receive_item_kinds[index] = flag_on_receive_item_kinds_list[key]
        }
    }
}

onBeforeMount(async () => {
    //點擊查看背包人數
    logEventStatistics('Mygarden_backpage_page')
    loading('open')
    const cns = getCurrentInstance()
    await cns.appContext.config.globalProperties.$onWait
    init()
})

const tips = ref(false)
onMounted(() => {
    setTimeout(() => {
        tips.value = true
    }, 5000)
    // dialog.get('shoppingDialog').show({
    //     item:{
    //         num:300,
    //         name:'道具名称',
    //         label:true,
    //         description:'「紫色燈柱」1個+「紫色推車」1個+「戶外椅子」1個 +「炫酷小狸貓套裝」1個',
    //         rarity:3,
    //         countText:'只能購買一次。數量有限，先到先得。購買後不可退換，減碳值消耗後不可退回',
    //         effectiveText:'購買後，可永久使用',
    //         flowText:'購買後，在「背包」-「裝飾道具」中可 查看道具，點擊後即可使用'
    //     },
    //     status:'1'
    // })
    eventBus.on('reloadState', async () => {
        let treeDate = await getInfo()
        myEnergy_total.value = treeDate.user.energy_total
        await usePropStore()
            .commodityList()
            .then((res) => {
                PropPackList.value = res.commodities
            })
    })
    eventBus.on('changeXiaolimao', () => {
        document.querySelector('.tab-k-container')?.scrollTo(999, 0)
        tabIndex.value = 1
        chooseKind.value = 10
        kindIndex.value = 0
    })

    // 跳转到标题装饰的事件
    eventBus.on('changeTitle', () => {
        document.querySelector('.tab-k-container')?.scrollTo(999, 0)
        const index = state.package.types['我的花园'][0].indexOf('标题装饰')
        chooseKind.value = index
    })
    if (mQuery.changeTitle) {
        document.querySelector('.tab-k-container')?.scrollTo(999, 0)
        const index = state.package.types['我的花园'][0].indexOf('标题装饰')
        chooseKind.value = index
    }
    if (mQuery.changePendant) {
        const index = state.package.types['我的花园'][0].indexOf('挂饰')
        chooseKind.value = index
    }
    // 初始化
    // eventBus.on('shoppingInit', mallInit)
})

let myEnergy_total = ref(0)
let shoppingNew = ref(false)

// TODO 以下代码请加入kv平台
/** 标题装饰code列表 */
//TODO11.2
const titleDecorateCodeList = [
    'title_lanhuaying',
    'title_diaozhonghua',
    'title_yangzijing',
    'title_jieguojueming',
    'title_xiuqiuhua',
    'title_mumian',
    'title_yumushu',
    'title_fuguizhu',
    'title_taohua'
]
/** 用户信息 */
let info = null
// 初始化背包
async function init() {
    loading('open')
    for (let t of titleLists) {
        propList.value[t] = {
            功能道具: []
        }
    }
    propList.value['我的花园']['全部'] = []
    if (useUserStore().inLogin) {
        Promise.all([
            //getPropList
            getInfo(),
            getPropList(1, -1), //全部卡
            getPropList(0, -1) //全部装饰
        ]).then(async ([treeDate, card_all, resAll]) => {
            let result
            // if(useUserStore().isVip){
            //     result =  resAll.items
            // }else{
            //     result  = res.items
            result = resAll.items
            // }
            cardPoint1.value = treeDate.flag_on_received_vip_item
            flag_on_receive_item_kinds.value = treeDate.flag_on_receive_item_kinds
            myEnergy_total.value = treeDate.user.energy_total
            shoppingNew.value = treeDate.webui_manage_values
                ? treeDate.webui_manage_values.shopping_new == '1'
                    ? false
                    : true
                : true
            getRedRadio(flag_on_receive_item_kinds.value)
            useUserStore().changeVip(treeDate.user.vip)
            useUserStore().changeSlash(treeDate.user.slash)
            // useUserStore().changeVip(true)
            // useUserStore().changeVip(false)
            tree.value = treeDate.tree
            if (tree.value) {
                //种树了
                useTreeStore().changeHasPlanted(true)
                storage.save(`Forest ${envConfig.RUN_ENV}-${useUserStore().phone}-HasPlanted`, true)
                useTreeStore().changeTreeCode(tree.value.code)
                if (tree.value.level < 3) {
                    tree.value.maxWear = 0 //低于3级不能使用装饰
                } else if (tree.value.level < 6) {
                    tree.value.maxWear = 1 //3-6级可以选一件
                } else tree.value.maxWear = 3 //6级及以上可以选三件
            } else {
                //没种树
                useTreeStore().changeHasPlanted(false)
                storage.save(`Forest ${envConfig.RUN_ENV}-${useUserStore().phone}-HasPlanted`, false)
                tree.value = {}
                tree.value.maxWear = -1
            }
            if (treeDate.active_used_items.length > 0) {
                useUserStore().changeUsingCard(true)
            }
            for (let p in state.propsReward) {
                //后台里的数据\kv配置数据
                let item = result.find((val: any) => val.code == p)
                if (!item) continue
                if (!propList.value[state.propsReward[p].propKind][state.propsReward[p].propType]) {
                    propList.value[state.propsReward[p].propKind][state.propsReward[p].propType] = []
                }
                propList.value[state.propsReward[p].propKind][state.propsReward[p].propType].push(
                    Object.assign(item, state.propsReward[p])
                )
            }

            // TODO 以下代码请写入到kv平台
            // 添加标题装饰栏
            propList.value['我的花园']['标题装饰'] = []
            titleDecorateCodeList.forEach((code) => {
                const prop = result.find((value) => value.code === code)
                // 补充大类别
                prop.propKind = '我的花园'
                // 补充稀有度数据
                prop.propValue = prop.rarity.toString()
                // 补充分类数据
                prop.propType = '标题装饰'
                // TODO 道具名称应由kv平台进行三语获取
                // 补充道具名称
                prop.propName = state.prop[prop.code]
                prop && propList.value['我的花园']['标题装饰'].push(prop)
            })

            // 判断是否是会员
            if (
                !treeDate.user.vip &&
                resAll.items.filter((val) => val.propKind == '会员成长' && val.owned).length == 0
            ) {
                for (let i = 0; i < state.package.types['我的花园'][0].length; i++) {
                    if (state.package.types['我的花园'][0][i] === '会员成长') {
                        state.package.types['我的花园'][0].splice(i, 1)
                        break
                    }
                }
            } else {
                if (!state.package.types['我的花园'][0].includes('会员成长')) {
                    state.package.types['我的花园'][0].push('会员成长')
                }
                propList.value['我的花园']['会员成长'] = resAll.items.filter(
                    (val) =>
                        val.propKind == '会员成长' &&
                        ((val.code !== 'master' &&
                            val.code !== 'fairy' &&
                            val.code !== 'sculptor' &&
                            useUserStore().isVip) ||
                            val.owned)
                )
            }

            // 非会员去掉碳值卡
            if (!treeDate.user.vip && state.package.types['我的花园'][1].length == 3) {
                state.package.types['我的花园'][1].splice(2, 1)
            }
            for (let i in state.package.types['我的花园'][0]) {
                if (i == '0') continue
                const proList = propList.value['我的花园'][state.package.types['我的花园'][0][i]]
                // TODO 这里最好添加一个容错，但是为了方便出错调试，暂时没有添加
                propList.value['我的花园']['全部'].push(...proList)
            }

            for (let p in propList.value) {
                //背包排序 优先显示以获得的
                for (let q in propList.value[p]) {
                    if (Array.isArray(propList.value[p][q]) || propList.value[p][q]) {
                        propList.value[p][q].sort((a: any, b: any) =>
                            b.owned == a.owned
                                ? b.propValue == a.propValue
                                    ? a.code - b.code
                                    : b.propValue - a.propValue
                                : b.owned - a.owned
                        )
                    }
                }
            }

            if (treeDate.active_items.length) {
                //当前装备上的装饰
                wearList.value = treeDate.active_items.map((val: any) => {
                    //查看已经装上的装饰下标

                    if (
                        val.code == 'astronaut' ||
                        val.code == 'hiker' ||
                        val.code == 'musician' ||
                        val.code == 'magician' ||
                        val.code == 'arborist' ||
                        val.code == 'sculptor' ||
                        val.code == 'master' ||
                        val.code == 'fairy' ||
                        val.code == 'candymaster' ||
                        val.code == 'nauticalexplorer' ||
                        val.code == 'windchaser' ||
                        val.code == 'springwalkers'
                    ) {
                        return propList.value['我的花园']['会员成长'].find((value: any) => value.code == val.code)
                    } else {
                        if (propList.value['我的花园']['全部']) {
                            return propList.value['我的花园']['全部'].find((value: any) => value.code == val.code)
                        }
                    }
                })
            }

            // 非会员去掉小狸猫卡片
            if (!treeDate.user.vip && state.package.types['我的花园'][0].length == 11) {
                // state.package.types['我的花园'][0].splice(10,1)
                const vipCat = ['arborist', 'astronaut', 'hiker', 'magician', 'musician','nauticalexplorer','candymaster','windchaser','springwalkers']
                propList.value['我的花园']['全部'] = propList.value['我的花园']['全部'].filter((value: any) => {
                    return !vipCat.includes(value.code)
                })
            }

            // propList.value['我的花园']['功能道具'] = cardSort(propList.value['我的花园']['功能道具'], card, 0)
            // propList.value['会员成长']['功能道具'] = cardSort(propList.value['我的花园']['功能道具'], card_v, 1)
            // 合并
            // if(!useUserStore().isVip){
            //     propList.value['会员成长']['功能道具'] = [...cardSort(propList.value['我的花园']['功能道具'], card, 0)]
            // }else{
            // propList.value['会员成长']['功能道具'] = [...cardSort(propList.value['我的花园']['功能道具'], card, 0),...cardSort(propList.value['我的花园']['功能道具'], card_v, 1)]
            propList.value['会员成长']['功能道具'] = [...cardSort(propList.value['我的花园']['功能道具'], card_all, 0)]
            // }

            // console.log(propList.value['会员成长']['功能道具']);

            // propList.value['我的花园']['失效卡'] = disable.items
            // propList.value['会员成长']['失效卡'] = disable_v.items
            // 合并卡
            // propList.value['会员成长']['失效卡'] = [...disable.items]

            propList.value['会员成长']['失效卡'] = []

            propList.value['会员成长']['双倍卡'] = propList.value['会员成长']['功能道具'].filter((item) => {
                return item.code.includes('x2')
            })
            propList.value['会员成长']['碳值卡'] = propList.value['会员成长']['功能道具'].filter((item) => {
                return !item.code.includes('x2')
            })

            if (currentRoute?.query.cardAdd == 'true') {
                cardPoint.value = true
                currentRoute.query.cardAdd = 'false'
                // if(treeDate.user.vip){
                //     mgindex.value = 1
                // }
            }
            if (currentRoute?.query.tabIndex == '2') {
                changeTab(2)
                currentRoute.query.tabIndex = ''
            }
            if (usePropStore().toCard) {
                tabIndex.value = 0 //第一个的tab 0mygarden 1会员成长
                // nowTitle.value = titleLists[2]
                // mgindex.value = 1//花园的tab 0装饰 1功能
                kindIndex.value = 1 //tab种类 0装饰 1功能 2会员
            } else if (!useUserStore().isSlash && usePropStore().toCard1) {
                useUserStore().updatePackageFuncFlag1()
                // cardPoint1.value = false
                cardPoint.value = false
                tabIndex.value = 1
                kindIndex.value = 1
                usePropStore().toCard1 = false
            } else if (!useUserStore().isSlash && usePropStore().toCard2) {
                tabIndex.value = 1
                kindIndex.value = 0
                usePropStore().toCard1 = false
            }
            isShow.value = true //加载完成
            loading('close')

            info = await taskStore.getInfo()
            firstEnter.value = info.webui_manage_values
                ? info.webui_manage_values.first_enter_activity
                    ? true
                    : false
                : false
            // firstEnter.value = false
            useUserStore().setUpData('first_enter_activity', '1')

            propList.value['会员成长']['会员成长'] = propList.value['我的花园']['会员成长']
            if (location.href.includes('changeTitle=true')) {
                document.querySelector('.tab-k-container')?.scrollTo(999, 0)
                tabIndex.value = 0
                chooseKind.value =
                    state.package.types['我的花园'][0].indexOf('标题装饰') >= 0
                        ? state.package.types['我的花园'][0].indexOf('标题装饰')
                        : 0
            }
            if (location.href.includes('changePendant=true')) {
                tabIndex.value = 0
                chooseKind.value =
                    state.package.types['我的花园'][0].indexOf('挂饰') >= 0
                        ? state.package.types['我的花园'][0].indexOf('挂饰')
                        : 0
            }
        })
        taskStore.changeNewest(tabIndex.value)
    } else {
        console.log('未登录')
        await usePropStore()
            .commodityListNoLogin()
            .then((res) => {
                PropPackList.value = res.commodities
            })
        Promise.all([
            //getPropList
            //target  0-装饰道具，1-功能道具
            //bag -1-全部, 0-普通, 1-VIP限定
            getShareList()
            // getShareList(1,-1)
        ]).then(([res1]) => {
            let result = [...res1.items]
            for (let p in state.propsReward) {
                //后台里的数据\
                let item = result.find((val: any) => val.code == p)
                if (!item) continue
                if (!propList.value[state.propsReward[p].propKind][state.propsReward[p].propType]) {
                    propList.value[state.propsReward[p].propKind][state.propsReward[p].propType] = []
                }
                propList.value[state.propsReward[p].propKind][state.propsReward[p].propType].push(
                    Object.assign(item, state.propsReward[p])
                )
            }

            // TODO 以下代码请写入到kv平台
            // 添加标题装饰栏
            propList.value['我的花园']['标题装饰'] = []
            titleDecorateCodeList.forEach((code) => {
                const prop = result.find((value) => value.code === code)
                // 补充大类别
                prop.propKind = '我的花园'
                // 补充稀有度数据
                prop.propValue = prop.rarity.toString()
                // 补充分类数据
                prop.propType = '标题装饰'
                // TODO 道具名称应由kv平台进行三语获取
                // 补充道具名称
                prop.propName = state.prop[prop.code]
                prop && propList.value['我的花园']['标题装饰'].push(prop)
            })

            // state.package.types['我的花园'][0].splice(10, 1)
            const index = state.package.types['我的花园'][0].indexOf('会员成长')
            if (index >= 0) {
                state.package.types['我的花园'][0].splice(index, 1)
            }

            for (let i in state.package.types['我的花园'][0]) {
                if (i == '0') continue
                propList.value['我的花园']['全部'].push(
                    ...propList.value['我的花园'][state.package.types['我的花园'][0][i]]
                )
            }

            for (let p in propList.value) {
                //背包排序 优先显示以获得的
                for (let q in propList.value[p]) {
                    propList.value[p][q].sort((a: any, b: any) =>
                        b.owned == a.owned
                            ? b.propValue == a.propValue
                                ? a.code - b.code
                                : b.propValue - a.propValue
                            : b.owned - a.owned
                    )
                }
            }
            propList.value['我的花园']['功能道具'] = []
            propList.value['会员成长']['功能道具'] = []
            propList.value['我的花园']['失效卡'] = []
            propList.value['会员成长']['失效卡'] = []

            propList.value['会员成长']['双倍卡'] = []
            propList.value['会员成长']['碳值卡'] = []
            isShow.value = true //加载完成
            loading('close')
        })
    }

    if (tabIndex.value === 2) {
        await usePropStore()
            .commodityList()
            .then((res) => {
                PropPackList.value = res.commodities
            })
        await useUserStore().setUpData('shopping_new', '1')
        shoppingNew.value = false
    }
}

const toLog = async () => {
    console.log('登录')
    loading('open')
    await useUserStore().login()
    init()
    loading('close')
}

// 功能卡排序
const cardSort = (arr, cards, type) => {
    arr = cards.items
        .filter((item) => item.actived == false)
        .map((item) => {
            if (item.code == 'x2energy') {
                item.type = 1
            } else if (item.code == 'x2wewalk') {
                item.type = 0
            } else if (item.code == 'energy20') {
                item.type = 2
            } else if (item.code == 'energy30') {
                item.type = 3
            } else if (item.code == 'energy10') {
                item.type = 4
            }
            return item
        })
    arr.sort((a, b) => {
        return new Date(b.destory_at) - new Date(a.destory_at)
    })
    if (type == 0) {
        arr.forEach((item) => {
            switch (item.type) {
                case 0:
                    cardNum.value['0']++
                    break
                case 1:
                    cardNum.value['1']++
                    break
            }
        })
    }
    return arr
}

//打开垃圾桶
const showLjt = async () => {
    // if(tabIndex.value == 1 && !useUserStore().isVip){
    //     toast(state.联动奖赏.你不是会员)
    //     return
    // }
    openLjt.value = !openLjt.value
    if (openLjt.value) {
        chooseKind.value = -1
        if (!useUserStore().inLogin) return
        const disable = await getPropList(-1, -1) //过期
        propList.value['会员成长']['失效卡'] = [...disable.items]
    } else {
        chooseKind.value = 0
    }
}

const updataUse = (index, item) => {
    // let obj = propList.value[titleLists[tabIndex.value]]['功能道具'].splice(index, 1)
    if (tabIndex.value == 0) {
        let p = propList.value['我的花园']['功能道具'].find((i) => {
            return i.id == item.id
        })
        let n = propList.value['我的花园']['功能道具'].indexOf(p)
        cardNum.value[p.type]--
        let obj = propList.value['我的花园']['功能道具'].splice(n, 1)
        propList.value['我的花园']['失效卡'].unshift(...obj)
        useUserStore().changeUsingCard(true)
    } else if (tabIndex.value == 1) {
        let obj = []
        if (item.code == 'energy30' || item.code == 'energy20' || item.code == 'energy10') {
            let p = propList.value['会员成长']['碳值卡'].find((i) => {
                return i.id == item.id
            })
            let n = propList.value['会员成长']['功能道具'].indexOf(p)
            let p1 = propList.value['会员成长']['碳值卡'].find((i) => {
                return i.id == item.id
            })
            let n1 = propList.value['会员成长']['碳值卡'].indexOf(p1)
            obj = propList.value['会员成长']['功能道具'].splice(n, 1)
            propList.value['会员成长']['碳值卡'].splice(n1, 1)
        } else {
            let p = propList.value['会员成长']['功能道具'].find((i) => {
                return i.id == item.id
            })
            let n = propList.value['会员成长']['功能道具'].indexOf(p)
            let p1 = propList.value['会员成长']['双倍卡'].find((i) => {
                return i.id == item.id
            })
            let n1 = propList.value['会员成长']['双倍卡'].indexOf(p1)
            propList.value['会员成长']['功能道具'].splice(n, 1)
            obj = propList.value['会员成长']['双倍卡'].splice(n1, 1)
            useUserStore().changeUsingCard(true)
        }
        propList.value['会员成长']['失效卡'].unshift(...obj)
    }
}

function changeTab(index) {
    //控制上面的tab
    if (tabIndex.value == index) {
        return
    }
    if (index == 1) {
        logEventStatistics('garden_membership_click')
        if (cardPoint1.value) {
            useUserStore().updatePackageFuncFlag1()
            cardPoint1.value = false
        }
    }
    if (index == 2) {
        logEventStatistics('graden_cr_mall')
    }
    tabIndex.value = index
    kindIndex.value = 0
    chooseKind.value = 0
    nowTitle.value = titleLists[index]
    chooseKind.value = 0
    openLjt.value = false
}

function changeMg(index) {
    //控制中间的tab
    if (kindIndex.value == index) return
    openLjt.value = false

    kindIndex.value = index
    nowTitle.value = titleLists[index]
    // 判断是否是会员
    // if(useUserStore().isVip){
    //     mgindex.value = index
    // }

    if (index == 0) {
        if (tabIndex.value == 1) {
            logEventStatistics('garden_membership_click')
        } else {
            logEventStatistics('garden_decoration_button_click')
        }
    } else if (index == 1) {
        if (tabIndex.value == 1) {
            logEventStatistics('garden_member_item_click')
        } else {
            logEventStatistics('garden_item_button_click')
        }
        if (useUserStore().inLogin) {
            cardPoint.value = false
            useUserStore().updatePackageFuncFlag()
            useUserStore().updatePackageFuncFlag1()
        }
    }
    chooseKind.value = 0
    tabIndex.value = index
}

// 跳转我的功能卡
const toMyCard = () => {
    changeChoose(0)
}

// 背包导航栏选项切换
async function changeChoose(index, name?) {
    if (name) {
        let nameList = {
            挂饰: 'item',
            铲子: 'chanzi',
            围栏: 'weilan',
            灯柱: 'dengzhu',
            稻草人: 'daocaoren',
            木头车: 'mutouche',
            喷水池: 'penshuichi',
            桌子: 'zhuozi',
            椅子: 'yizi',
            会员成长: 'xiaolimao',
            双倍卡: 'x2',
            减碳卡: 'energy',
            标题装饰: 'title'
        }
        flag_on_receive_item_kinds.value = flag_on_receive_item_kinds.value.filter((val) => val != name)
        await usePropStore().updateUserFlagOnReceviedItemKind(nameList[name])
    }

    // 判断当前点击的标签是否会员成长,如果是则切换页签
    if (state.package.types['我的花园'][tabIndex.value][index] == '会员成长') {
        changeTab(1)
    } else {
        if (state.package.types[titleLists[mgindex.value]][kindIndex.value].length <= 3) {
            changeTab(1)
        } else {
            changeTab(0)
        }
    }

    if (tabIndex.value == 1 && kindIndex.value == 0 && chooseKind.value == 0) {
        logEventStatistics('garden_member_decoration_gif')
    }
    if (tabIndex.value == 1 && kindIndex.value == 1 && chooseKind.value == 0) {
        logEventStatistics('garden_member_itemcard_tab')
    }
    if (tabIndex.value == 1 && kindIndex.value == 1 && chooseKind.value == 1) {
        logEventStatistics('garden_member_doublecard_tab')
    }
    if (tabIndex.value == 1 && kindIndex.value == 1 && chooseKind.value == 2) {
        logEventStatistics('garden_member_crexpcard_tab')
    }
    if (tabIndex.value == 1 && kindIndex.value == 1 && chooseKind.value == -1) {
        logEventStatistics('garden_member_invalid')
    }
    chooseKind.value = index
    openLjt.value = false
}

// 关闭背包切换回首页
function closeAndGoHome() {
    if (router.options.history.state.back) {
        router.go(-1)
    } else {
        router.replace({
            path: '/home',
            query: {
                hideNavigationBar: 'true'
            }
        })
    }
}

// 清空失效卡
const cleanCard = () => {
    if (propList.value[titleLists[tabIndex.value]]['失效卡'].length == 0) {
        return
    }
    let cleanDlg = dialog.get('cleanDialog')
    cleanDlg.on('confirm', () => {
        logEventStatistics('garden_member_invalid_clear')
        // 记得解开
        // if(tabIndex.value == 0){
        //     usePropStore().cleanProp(0)
        // }else if(tabIndex.value == 1){
        //     usePropStore().cleanProp(1)
        // }
        usePropStore().cleanProp(-1)
        propItemRef.value.cleanListArr()
        propList.value[titleLists[tabIndex.value]]['失效卡'] = []
        cleanDlg.close()
    })
    cleanDlg.show({}, { maskClose: false })
}

watch(
    wearList,
    (val, oldVal) => {
        usePropStore().propsHandle(wearList.value)
    },
    { deep: true }
)

let shoppingList = ref()
let shoppingFooterShow = ref(false)
let PropPackList = ref()
watch(
    tabIndex,
    async () => {
        if (tabIndex.value == 2) {
            if (useUserStore().inLogin) {
                await usePropStore()
                    .commodityList()
                    .then((res) => {
                        PropPackList.value = res.commodities
                    })
                await useUserStore().setUpData('shopping_new', '1')
                shoppingNew.value = false
            } else {
                await usePropStore()
                    .commodityListNoLogin()
                    .then((res) => {
                        PropPackList.value = res.commodities
                    })
            }
            await nextTick(() => {
                let timer: null | NodeJS.Timeout = null
                let shoppingListDom = document.querySelector('.shopping-list') as HTMLElement
                shoppingListDom?.addEventListener('scroll', () => {
                    clearTimeout(timer as NodeJS.Timeout)
                    timer = setTimeout(() => {
                        if (
                            shoppingListDom?.scrollHeight <=
                            shoppingListDom?.scrollTop + 20 + shoppingListDom?.clientHeight
                        ) {
                            shoppingFooterShow.value = true
                        } else {
                            shoppingFooterShow.value = false
                        }
                    }, 100)
                })
            })
        } else if (tabIndex.value !== 2 && kindIndex.value == 0 && chooseKind.value == 0) {
            await init()
        }
    },
    { deep: true }
)
</script>

<style lang="less" scoped>
@import './Package.scss';
</style>
