<template>
  <div class="box">
      <img class="huajian" :src="$imgs['huojian.png']" alt="">
        <p class="vt">{{state.dialog.发现新版本}}</p>
        <p class="vn">v11.0</p>
        <div class="container">
            <p class="include">{{state.dialog.新版本功能包括}}</p>
            <p class="content">
                {{state.dialog.新版本内容}}
            </p>
        </div>

      <div @click="toStore" class="bottom_btn">
          {{state.dialog.立即升级}}
      </div>
      <img class="xx" @click="emit('close')" :src="$imgs['xx.png']" alt="">
  </div>
</template>

<script setup>
import { getSystem } from '@via/mylink-sdk'
import { useLang } from 'hook'

const system = getSystem()
const { state } = useLang()
let emit = defineEmits(['close'])

const toStore = () => {
    if(system == 'android'){
        window.location.href = 'https://play.google.com/store/apps/details?id=com.ChinaMobile'
    }else if(system == 'ios'){
        window.location.href = 'https://itunes.apple.com/hk/app/mylink-%E9%80%A3%E6%8E%A5-%E7%94%9F%E6%B4%BB/id483513425?mt=8'
    }
}
</script>

<style lang="less" scoped>
.box{
    width: 622px;
    box-sizing: border-box;
    padding-top: 266px;
    padding-bottom: 180px;
    padding-left: 58px;
    padding-right: 58px;
    background: #FFFFFF;
    border-radius: 48px 48px 48px 48px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    .xx{
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -80px;
        width: 56px;
    }
    .vt{
        font-size: 44px;
        font-family: PingFang HK-Medium, PingFang HK;
        font-weight: 500;
        color: #6A6A6A;
        margin-bottom: 8px;
    }
    .vn{
        font-size: 28px;
        font-family: PingFang HK-Regular, PingFang HK;
        font-weight: 400;
        color: #A7AEC3;
        margin-bottom: 32px;
    }
    .container{
        .include{
            font-size: 32px;
            font-family: PingFang HK-Medium, PingFang HK;
            font-weight: 500;
            color: #6A6A6A;
            margin-bottom: 16px;
        }
        .content{
            font-size: 28px;
            font-family: PingFang HK-Regular, PingFang HK;
            font-weight: 400;
            color: #6A6A6A;
            line-height: 42px;
            white-space: pre-line;
        }
    }
    .huajian{
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        top: -78px;
        width: 340px;
    }
    .bottom_btn{
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 50px;
        width: 526px;
        height: 80px;
        background: linear-gradient(0deg, #3CBF48 0%, #3DD13F 100%);
        box-shadow: 0px 4px 0px 2px rgba(0,0,0,0.16), inset 0px 4px 0px 2px rgba(100,222,105,1);
        border-radius: 40px 40px 40px 40px;
        font-size: 28px;
        font-family: PingFang HK-Regular, PingFang HK;
        font-weight: 400;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
