{"name": "hook", "version": "1.0.0", "description": "一些通用方法", "license": "MIT", "author": "CodeBear", "main": "src/index.ts", "files": ["lib", "src", "types"], "typings": "types/index.d.ts", "scripts": {"preview": "vite build", "build": " yarn build:ts && vite build && node script/move.js", "build:ts": "tsc -p . && tsc-alias"}, "devDependencies": {"@babel/types": "^7.17.0", "@types/node": "^17.0.25", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "@vitejs/plugin-vue": "^2.3.1", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^10.0.0", "clean-css": "^5.3.0", "cross-env": "^7.0.3", "eslint": "^8.13.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.6.0", "less": "^4.1.2", "path": "^0.12.7", "prettier": "^2.6.2", "tsc-alias": "^1.6.6", "typescript": "^4.6.3", "vite": "^2.9.5", "vue-eslint-parser": "^8.3.0", "vue-tsc": "^0.34.7"}, "dependencies": {"axios": "^0.26.1", "clipboard": "^2.0.10", "dayjs": "^1.11.0", "jwt-decode": "^3.1.2", "md5js": "^1.0.7", "query-string": "^7.1.1", "vue": "^3.2.33", "vue-i18n": "^9.2.0-beta.35", "vue-router": "^4.0.14"}}