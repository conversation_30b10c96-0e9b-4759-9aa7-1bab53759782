import { App, Component, Directive, Plugin } from 'vue';
import { BaseLinkVue } from './base/baseLikeVue';
export * from './core';
export * from './ui';
declare class Hook extends BaseLinkVue {
    app: App;
    constructor(app: App);
    use(plugin: Plugin, ...options: any[]): this;
    directive(name: string, directive: Directive): this;
    component(name: string, component: Component): this;
}
export declare function install(app: App): Hook;
