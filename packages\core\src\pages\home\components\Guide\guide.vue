<template>
    <!-- 新手引导 -->
    <div class="guideContainer" v-if="finLoad && ((!guide_completed && buildTree) || (!no_first_login && buildTree))">
        <div ref="guideBox" @click="stepWalk" class="guideBox guideBox1"></div>
        <div ref="stepInfo" class="stepInfo">{{ state.guideStep[stepIndex] }}</div>
        <!-- <div class="lastStep" v-if="stepIndex == steps.length && steps.length != 0">
            <p>{{ state.guideStep[stepIndex] }}</p>
        </div> -->
        <div class="guideH5" @click="stepWalk">
            <div
                class="tiaoguo"
                @click.stop="pass"
            >
                {{ state.home.跳过 }}
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, nextTick, inject } from 'vue'
import type { GuideBox, GuideProps } from './types'
import { useLang } from 'hook'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const { state, lang } = useLang()

// home.vue 中传入的数据
const {
    virtualBub,
    showSheet,
    finLoad,
    stepIndex,
    level,
    guide_completed,
    buildTree,
    no_first_login,
    notified_first_logic1,
    notified_first_logic2,
    notified_first_logic3,
    notified_first_logic4,
    tree_energy,
    demand,
    judgePopUpNotification,
    ifLotArr,
    openNFTDialog
} = inject<GuideProps>('Guide')!

// 组件数据
let guideBox = ref<GuideBox>({
    className: '',
    style: {
        width: '',
        height: '',
        left: '',
        top: '',
        transition: ''
    }
})
// 新手引导方框位置
let steps = ref<Array<number[]>>([])
let stepInfo = ref<Record<string, any>>({})
let body = document.body.getBoundingClientRect()
let passTimer: NodeJS.Timeout | null = null

// guideFn / stepWalk 中使用该函数
const stepInfoPos = () => {
    stepInfo.value.style.display = 'block'
    let style = guideBox.value.style
    let style_top = Number(style.top.replace('px', ''))
    let style_height = Number(style.height.replace('px', ''))
    let style_left = Number(style.left.replace('px', ''))
    let style_width = Number(style.width.replace('px', ''))
    let top = false
    let left = false
    if (style_top + style_height / 2 < body.height / 2) {
        top = true
    }
    if (style_left + style_width / 2 < body.width / 2) left = true
    if (top) {
        if (!left) {
            stepInfo.value.className = 'stepInfo rd'
            stepInfo.value.style.top = style_top + style_height + 20 + 'px'
            stepInfo.value.style.left = ''
            stepInfo.value.style.right = body.width - style_left - style_width + 'px'
            stepInfo.value.style.bottom = ''
        } else {
            stepInfo.value.className = 'stepInfo ld'
            stepInfo.value.style.top = style_top + style_height + 20 + 'px'
            stepInfo.value.style.left = style_left + 'px'
            stepInfo.value.style.bottom = ''
        }
    } else {
        if (!left) {
            stepInfo.value.className = 'stepInfo rt'
            stepInfo.value.style.top = ''
            stepInfo.value.style.bottom = body.height - style_top + 20 + 'px'
            stepInfo.value.style.left = ''
            stepInfo.value.style.right = body.width - style_left - style_width + 'px'
        } else {
            stepInfo.value.className = 'stepInfo lt'
            stepInfo.value.style.top = ''
            stepInfo.value.style.bottom = body.height - style_top + 20 + 'px'
            stepInfo.value.style.left = style_left + 'px'
            stepInfo.value.style.right = ''
        }
    }
}

// 下一步
const stepWalk = () => {
    if (passTimer) return //已经开启结束新手引导函数
    let guideContainer: HTMLElement = document.querySelector(`.guideContainer`) as HTMLElement
    if (stepIndex.value == steps.value.length - 1) {
        stepIndex.value++
        pass()
        return
    } else if (stepIndex.value == steps.value.length) {
        pass()
        return
    }
    if (!no_first_login.value && stepIndex.value == steps.value.length) {
        pass()
        return
    }
    stepIndex.value++
    // if ((stepIndex.value == 11 && useUserStore().isHK == 0) || (stepIndex.value == 12 && useUserStore().isHK == 0)) {
    //     showSheet.value = true
    // } else {
    //     showSheet.value = false
    // }

    // if (stepIndex.value == 6) {
    //     window.scrollTo({
    //         top: 1000,
    //         behavior: 'smooth'
    //     })
    // } else {
    //     window.scrollTo({
    //         top: 0,
    //         behavior: 'smooth'
    //     })
    // }

    if (stepIndex.value == 6) {
        guideBox.value.className = 'guideBox guideBox1'
        guideContainer.className = `guideContainer  ${'NFT' + lang.value}`
    // } else if (stepIndex.value == 11 || stepIndex.value == 12) {
    //     guideBox.value.className = 'guideBox guideBox1'
    //     guideContainer.className = `guideContainer ${'NFT' + lang.value}`
    } else {
        guideBox.value.className = 'guideBox1 guideBox'
        guideContainer.className = 'guideContainer'
    }

    guideBox.value.style.width = steps.value[stepIndex.value][0] + 'px'
    guideBox.value.style.height = steps.value[stepIndex.value][1] + 'px'
    guideBox.value.style.top = steps.value[stepIndex.value][2] + 'px'
    guideBox.value.style.left = steps.value[stepIndex.value][3] + 'px'
    stepInfo.value.style.display = 'none'
    setTimeout(() => {
        stepInfo.value.style.display = ''
        stepInfoPos()
    }, 300)
}

// 跳过新手引导
const pass = () => {
    document.body.style.overflow = 'auto'
    if (passTimer) return
    if (virtualBub.value) {
        virtualBub.value = false
    } else {
        document.querySelector(`.bub`)?.removeAttribute('style') //去掉行内属性，完成新手引导使用
    }
    let guideContainer: HTMLElement = document.querySelector(`.guideContainer`) as HTMLElement
    guideContainer.style.opacity = '0'
    passTimer = setTimeout(async () => {
        guide_completed.value = true
        //这里写完成新手引导函数
        if (!no_first_login.value || !notified_first_logic1.value || !notified_first_logic2.value) {
            no_first_login.value = true
            judgePopUpNotification(tree_energy.value, demand.value[level.value], async() => {
                openNFTDialog()
            })
            useUserStore().updateFirstLogin()
            // 新手引导结束
            useUserStore().guildCom()
        } else {
            no_first_login.value = true
            useUserStore().updateFirstLogin()
            notified_first_logic1.value =
                notified_first_logic2.value =
                notified_first_logic3.value =
                notified_first_logic4.value =
                    true
            if (!useUserStore().isSlash) {
                useUserStore().updateFirstLogin1()
                useUserStore().updateFirstLogin2()
            }
            useUserStore().updateFirstLogin3()
            useUserStore().updateFirstLogin4()
            // 新手引导结束
            useUserStore().guildCom()
            ifLotArr()
        }
    }, 600)
}

// 新手引导开启
const guideFn = async () => {
    document.body.style.overflow = 'hidden'
    let cal = 750 / body.width
    showSheet.value = true
    await nextTick()
    let bubDom: HTMLElement = document.querySelector(`.bub`) as HTMLElement // 第一个减碳球
    bubDom.style.animation = 'none'
    let bub: DOMRect = document.querySelector(`.bub`)?.getBoundingClientRect() as DOMRect // 第一个减碳球
    // bubDom.removeAttribute('style')//去掉行内属性，完成新手引导使用
    let type: DOMRect = document.querySelector(`.type1`)?.getBoundingClientRect() as DOMRect // 植物详情
    let headBox: DOMRect = document.querySelector(`.headbox`)?.getBoundingClientRect() as DOMRect // 头像
    // let hp: DOMRect = document.querySelectorAll(`.hp`)[level.value - 1]?.getBoundingClientRect() as DOMRect// 生命值
    let progressBox: DOMRect = document.querySelector(`.progressBox`)?.getBoundingClientRect() as DOMRect // 生命值
    let scheduleImg: DOMRect = document
        .querySelectorAll(`.scheduleImg`)
        [level.value - 1]?.getBoundingClientRect() as DOMRect // 生命值
    let myEquip: DOMRect = document.querySelector(`.myEquip`)?.getBoundingClientRect() as DOMRect // 背包成就
    let friendComponent: DOMRect = document.querySelector(`.friendComponent`)?.getBoundingClientRect() as DOMRect // 好友动态
    // let taskEquip: DOMRect = document.querySelector(`.taskEquip`)?.getBoundingClientRect() as DOMRect// 任务
    // let award: DOMRect = document.querySelector(`.award`)?.getBoundingClientRect() as DOMRect// 奖励详情
    // let up: DOMRect = document.querySelector(`.up`)?.getBoundingClientRect() as DOMRect// 升级攻略
    let findnextfrd: DOMRect = document.querySelector(`.findnextfrd`)?.getBoundingClientRect() as DOMRect // 找减碳值
    let mygameImg: DOMRect = document.querySelector(`.mygameImg`)?.getBoundingClientRect() as DOMRect // mygame
    // let marqueeBox: DOMRect = document.querySelector(`.marqueeBox`)?.getBoundingClientRect() as DOMRect// 跑马灯
    let grow: DOMRect = document.querySelector(`.waterIcon`)?.getBoundingClientRect() as DOMRect // 浇水
    let achieve: DOMRect = document.querySelector(`.ach`)?.getBoundingClientRect() as DOMRect // 成就
    let give: DOMRect = document.querySelector(`.give`)?.getBoundingClientRect() as DOMRect // 送花
    let nftSize: DOMRect = document.querySelector('.achieveBox1')?.getBoundingClientRect() as DOMRect
    let myMall: DOMRect = document.querySelector(`.myMall`)?.getBoundingClientRect() as DOMRect // 减碳商城
    let bearJump: DOMRect = document.querySelector(`.bear`)?.getBoundingClientRect() as DOMRect // 熊
    let sheetContent: DOMRect = document.querySelector('.sheetContent')?.getBoundingClientRect() as DOMRect //
    let up: DOMRect = document.querySelector(`.up`)?.getBoundingClientRect() as DOMRect // 升级攻略
    let myBlindBottomIcon: DOMRect = document.querySelector(`.myBlindBottomIcon`)?.getBoundingClientRect() as DOMRect // icon

    // 增加新手指引送花
    // steps.value.splice(7,0,[give.width +8, give.height+8, give.top-document.querySelector('.sheetContent')?.getBoundingClientRect().height-5, give.left-4])
    // 新手引导方框位置
    steps.value = [
        [bub.width + 10 * cal, bub.height + 10 * cal, bub.top - 5 * cal, bub.left - 5 * cal],
        // [type.width + 4 * cal, type.height + 4 * cal, type.top - 2 * cal, type.left - 2 * cal],
        // [headBox.width + 4 * cal, headBox.height + 4.5 * cal, headBox.top - 1 * cal, headBox.left - 2 * cal],
        [
            progressBox.width - 34 * cal,
            progressBox.height + 4.5 * cal,
            progressBox.top - 1 * cal,
            progressBox.left + 16 * cal
        ],
        // [
        //     scheduleImg.width + 4 * cal,
        //     scheduleImg.height + 4.5 * cal,
        //     scheduleImg.top - 1 * cal,
        //     scheduleImg.left - 2 * cal
        // ],
        [myEquip.width + 8 * cal, myEquip.height + 10 * cal, myEquip.top - 4 * cal, myEquip.left - 2 * cal],
        // [
        //     friendComponent.width + 8 * cal,
        //     friendComponent.height + 15 * cal,
        //     friendComponent.top + 30 * cal,
        //     friendComponent.left - 3 * cal
        // ],
        [findnextfrd.width, findnextfrd.height + 12 * cal, findnextfrd.top - 8 * cal, findnextfrd.left - 2 * cal],
        [grow.width + 15 * cal, grow.height + 15 * cal, grow.top - 8 * cal, grow.left - 8 * cal],
        // [marqueeBox.width, marqueeBox.height + 20 * cal, marqueeBox.top - 15 * cal, marqueeBox.left - 2 * cal],
        // [mygameImg.width, mygameImg.height, mygameImg.top, mygameImg.left],
        [achieve.width + 3 * cal, achieve.height + 3 * cal, achieve.top, achieve.left - 2 * cal],
        // [290 / cal, 70 / cal, 748 / cal, 394 / cal],
        // [318 / cal, 318 / cal, 398 / cal, 384 / cal],
        // [nftSize.width * 3 - 20, nftSize.height * 2 - 30, give.top - sheetContent.height - 115, 20],
        // [bearJump.width, bearJump.height, bearJump.top, bearJump.left],
        // [myMall.width + 14 * cal, myMall.height + 6 * cal, myMall.top, myMall.left - 8 * cal],
        [318 / cal, 424 / cal, 398 / cal, 384 / cal],
        // [
        //     myBlindBottomIcon.width + 40,
        //     myBlindBottomIcon.height + 10,
        //     myBlindBottomIcon.top,
        //     myBlindBottomIcon.left - 20
        // ]
        // [award.width - 4 * cal, award.height + 6 * cal, award.top - 3 * cal, award.left],
        // [up.width + 10 * cal, up.height + 8 * cal, up.top - 4 * cal, up.left - 5 * cal]
    ]
    showSheet.value = false
    guideBox.value.style.width = steps.value[stepIndex.value][0] + 'px'
    guideBox.value.style.height = steps.value[stepIndex.value][1] + 'px'
    guideBox.value.style.top = steps.value[stepIndex.value][2] + 'px'
    guideBox.value.style.left = steps.value[stepIndex.value][3] + 'px'
    stepInfoPos()
}

// 导出新手引导函数
defineExpose({ guideFn })
</script>
<style lang="less">
.guideContainer {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    transition: all 0.6s ease;

    .stepInfo {
        max-width: 550px;
        text-align: center;
        position: absolute;
        padding: 36px 22px;
        background: #ffffff;
        border-radius: 28px;
        font-size: 32px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 500;
        color: #1b1b1b;
        display: none;
    }

    .stepInfo::after {
        content: '';
        display: block;
        position: absolute;
        width: 38px;
        height: 20px;
        background: url('@imgs/jianjian.png') no-repeat;
        background-size: 100% 100%;
        transform-origin: center center;
    }

    .ld::after {
        top: -18px;
        left: 50px;
        transform: rotate(180deg);
    }

    .lt::after {
        bottom: -18px;
        left: 50px;
    }

    .rt::after {
        bottom: -18px;
        right: 50px;
    }

    .rd::after {
        top: -18px;
        right: 50px;
        transform: rotate(180deg);
    }

    .guideBox {
        position: absolute;
        transition: all 0.3s;
        border-radius: 24px;
    }

    .guideBox1 {
        box-shadow: rgb(33 33 33 / 50%) 0px 0px 0px 2000px;
    }

    .lastStep {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 522px;
        height: 770px;
        background: url('@imgs/last-step.png') no-repeat;
        background-size: 100% 100%;
        animation: fdsseq1 0.3s ease;

        p {
            white-space: pre-line;
            width: 450px;
            height: 129px;
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            font-size: 32px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    @keyframes fdsseq1 {
        from {
            transform: translate(-50%, -50%) scale(0);
        }

        to {
            transform: translate(-50%, -50%) scale(1);
        }
    }

    .guideH5 {
        position: absolute;
        width: 100%;
        height: 100%;

        .tiaoguo {
            position: absolute;
            right: 44px;
            bottom: 100px;
            padding: 6px 44px;
            border-radius: 46px 46px 46px 46px;
            border: 2px solid #f0f0f0;
            font-size: 36px;
            font-family: PingFang SC-Bold, PingFang SC, Arial, Helvetica, sans-serif;
            font-weight: 500;
            color: #ffffff;
            background: rgba(0,0,0,0.3);
            border: 2px solid #F0F0F0;
        }
    }
}
</style>
