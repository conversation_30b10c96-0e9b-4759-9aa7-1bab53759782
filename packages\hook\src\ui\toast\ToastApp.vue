<template>
  <div v-if="state.show" class="toast">
    <div :class="['lx-toast', `lx-toast-${state.type}`, state.wordWrap ? 'lx-word-wrap' : '']" :style="state.extStyle">
      <img v-if="state.icon" class="toast-icon" :src="state.icon" />
      <div class="toast-first-tip">{{ state.tip }}</div>
      <div v-if="state.secondTip" class="toast-second-tip">{{ state.secondTip }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { state } from './state'
</script>

<style lang="less">
#app-toast {
  position: fixed;
  z-index: 99999999999999999;
}
</style>

<style lang="less" scoped>
.toast {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

.lx-toast {
  position: fixed;
  bottom: 1rem;
  left: 50%;
  max-width: 90%;
  height: 0.4rem;
  line-height: 0.5rem;
  padding: 0.2rem 0.2rem;
  transform: translateX(-50%);
  text-align: center;
  font-size: 0.45rem;
  color: #fff;
  border-radius: 0.05rem;
  background: rgba(0, 0, 0, 0.8);
  animation: show-toast 0.5s;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 9999999999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .toast-icon {
    width: 0.5rem;
    margin: 0.23rem 0;
  }
  .toast-first-tip {
    word-wrap: break-word;
    word-break: break-word;
  }
  .toast-second-tip {
    opacity: 0.6;
    margin: 0.07rem 0;
  }
}

.lx-toast.lx-word-wrap {
  white-space: inherit;
  word-break: break-all;
  height: auto;
}

.lx-toast.lx-toast-top {
  top: 50px;
  bottom: inherit;
}

.lx-toast.lx-toast-center {
  top: 50%;
  margin-top: -0.2rem;
  bottom: inherit;
}

@-webkit-keyframes show-toast {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes show-toast {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
