<template>
    <div class="shopping-dialog">
            <div class="example">
                <template v-if="item.target == 0">
                    <img class="top-content" :src="$imgs[`props/vip/${daojuBag[item.code]}/${lang}/4.png`]" alt="">
                    <div class="ip">
                        <img :class="daojuBag[item.code]" :src="$imgs[`props/vip/${daojuBag[item.code]}/0.png`]" alt="">
                    </div>
                    <div class="bg">
                        <img :src="$imgs[`props/vip/${daojuBag[item.code]}/${lang}/3.png`]" alt="">
                    </div>
                </template>
                <template v-else>
                    <div class="mygame-icon">
                        <img :src="item.iconUrl" alt="">
                    </div>
                </template>
            </div>
        <div class="illustrate" :class="{'illustrate-padding':target_value}">
            <div class="close" @click="emit('close')"></div>
            <div class="illustrate-seize"></div>
            <div class="grammage">
                <div class="icon"></div>
                <div class="num">
                    <span>{{item.num}}g</span>
                </div>
            </div>
            <div class="illustrate-box">
                <div class="illustrate-name">
                    <span>{{ item.name }}</span>
                </div>
                <div class="label" v-if="item.label">
                    <span>{{ state.shopping.新品 }}</span>
                </div>
                <div class="prop-description">
                    <span>{{ item.description }}</span>
                </div>
                <div class="rarity">
                    <span>{{ state.shopping.稀有度 }}:</span>
                    <img v-for="i in item.rarity" :key="i" :src="$imgs[`plantSelection/star.png`]" alt="">
                </div>
                <div class="copywriting-box">
                    <div class="copywriting-title">
                        <span>{{ state.shopping.購買說明 }}</span>
                    </div>
                    <div class="count" v-if="item.countText">
                        <div class="copywriting-label">
                            <span>{{ state.shopping.購買次數 }}:</span>
                        </div>
                        <p>{{ item.countText }}</p>
                    </div>
                    <div class="effective" v-if="item.effectiveText">
                        <div class="copywriting-label">
                            <span>{{ state.shopping.有效期 }}</span>
                        </div>
                        <p>{{ item.effectiveText }}</p>
                    </div>
                    <div class="flow" v-if="item.flowText">
                        <div class="copywriting-label">
                            <span>{{ state.shopping.如何使用 }}</span>
                        </div>
                        <p v-html="item.flowText"></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="shopping-btn" :class="{'shopping-btn-target_value':target_value}">
            <div class="copyText" v-if="target_value && status != 3">
                {{ state.shopping.道具券碼 }}：{{ target_value }}
                <img :src="$imgs['shopping/copyIcon.png']" @click="copyTarget(target_value)" alt="">
            </div>
            <div class="btn-content" :class="{'highlight':((status == 1 || status == 2) && useUserStore().inLogin)}" @click="shoppingEvent">
                <div class="content-top">
                    <div class="icon" v-if="status==1 && !buyer"></div>
                    <span>{{ getBtnText() }}</span>
                </div>
                <p v-if="expired_at && status == 2">{{ state.shopping.失效時間 }}{{ expired_at }}</p>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, reactive, onMounted, onBeforeMount, onBeforeUnmount, getCurrentInstance, computed,nextTick } from 'vue'
import { useRouter, useLang, useLoading, useDayjs, useEventBus, useEnvConfig, useDialog, useToast,useUtils } from 'hook'
import { usePropStore, useTaskStore, useTreeStore, useUserStore } from '@/store'
import { nativeBackClick, upShare } from '@unity/unity'
import purchaseConfirmDialog from '@/components/purchaseConfirmDialog.vue'
import purchaseSuccessfulDialog from '@/components/purchaseSuccessfulDialog.vue'
import { logEventStatistics } from '@via/mylink-sdk'
const { state,lang } = useLang()
const { copyText } = useUtils()
const eventBus = useEventBus()
const loading = useLoading()
const props = defineProps({
    item: {
      type:  Object,
      required: true
    },
    status: {
        type:  Number,
        required: true
    },
    jump_url: {
        type:  String,
        required: false
    },
    expired_at: {
        type:  String,
        required: false
    },
    target_value: {
        type:  String,
        required: false
    },
    id:{
        type:Number,
        required:true
    },
    init:{
        type:Function,
        required:false
    }
    // default: false
})
const daojuBag = ref({
    "fulonghesui":'master',
    "senlinmijing":'fairy',
    "bingxuetiandi":'sculptor'
})

const dialog = useDialog({
    purchaseConfirmDialog,
    purchaseSuccessfulDialog
})
const emit = defineEmits(['close'])
const getBtnText = ()=>{
    if(!useUserStore().inLogin) return props.item.num + 'g'
    if(buyer.value)return state.shopping.前往使用
    switch (props.status) {
        case 1:
            return props.item.num + 'g'
        case 2:
            if (props.item.target === 0) {
                return state.shopping.前往使用
            }else{
                return state.shopping.進入遊戲
            }
        case 3:
            return state.shopping.道具已失效
        case -2:
            return state.shopping.減碳值不足
        case 0:
            return state.shopping.來晚啦已售罄
        case -1:
            return state.shopping.已下架
    }
    return ''
}

let buyer = ref(false)
const shoppingEvent = async()=>{
    if(!useUserStore().inLogin){
        loading.loading('open')
        emit('close')
        await useUserStore().login()
        eventBus.emit('reloadState')
        loading.loading('close')
    }else{
        console.log(props.status,props.item.code);
        if(props.status == 1){
            switch (props.item.code) {
                case "fulonghesui":
                logEventStatistics('garden_cr_mall_dragon_buy')
                break;
            case "senlinmijing":
                logEventStatistics('garden_cr_mall_forest_buy')
                break;
            case "bingxuetiandi":
                logEventStatistics('garden_cr_mall_snow_buy')
                break;
            }
        }
        if (props.status == 2 || buyer.value) {
            dialog.closeAll()
            if (props.jump_url && props.target_value) {
                copyText(props.target_value)
                location.href = props.jump_url
                return
            }
            eventBus.emit('goUse')
            return
        }
        if(props.status != 1)return
        let purchaseConfirmDia = dialog.get('purchaseConfirmDialog')
        let purchaseSuccessfulDia = dialog.get('purchaseSuccessfulDialog')
        purchaseConfirmDia
        .on('confirm',async ()=>{
            let res = await usePropStore().commodityBuy(props.id)
            if (res.result != 1) {
                // -2-失败，-1-已下架，0-无库存，1-成
                switch (res.result) {
                    case -2:
                        useToast().toast(state.shopping.減碳值不足)
                        getBtnText()
                        break;
                    case -1:
                        useToast().toast(state.shopping.已下架)
                        getBtnText()
                        break;
                    case 0:
                        useToast().toast(state.shopping.來晚啦已售罄)
                        getBtnText()
                        break;
                }
                eventBus.emit('reloadState')
                purchaseConfirmDia.close()
                return
            }
            if (daojuBag.value[props.item.code]) {
                logEventStatistics('garden_cr_mall_confirm_buy')
            }else{
                logEventStatistics(props.item.code + '_confirm')
            }
            purchaseConfirmDia.close()
            eventBus.emit('reloadState')
            buyer.value = true
            // 调用购买接口
            purchaseSuccessfulDia
            .on('confirm',()=>{
                // 前往使用
                dialog.closeAll()
                if (res.jump_url) {
                    location.href = res.jump_url
                    return
                }
                eventBus.emit('goUse')
            })
            .on('cancel',()=>{
                dialog.closeAll()
            })
            .show({
                target_value:res.target_value,
                jump_url:res.jump_url
            })
        })
        .on('cancel',()=>{
            purchaseConfirmDia.close()
        })
        .show({
            num:props.item.num,
            propBag:props.item.name,
            desc: props.item.target == 0 ? state.dialogs.purchaseConfirm.tips : state.shopping.此道具包可於MyGame遊戲中使用,
            target:props.item.target
        })
    }
    
}

const copyTarget = (code)=>{
    copyText(code)
    useToast().toast(state.shopping.複製成功)
}
</script>

<style lang="less" scoped>
.shopping-dialog{
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .example{
        width: 560px;
        min-height: 400px;
        margin: 168px auto 142px;
        position: relative;
        .mygame-icon{
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            img{
                width: 240px;
                height: 240px;
            }
        }
        img{
            // width: 372px;
            // height: 372px;
        }
        .top-content{
            height: 64px;
            margin: 0 auto;
            display: block;
        }
        .ip{
            width: 200px;
            height: 200px;
            overflow: hidden;
            position: relative;
            margin: 0 auto;
            transform: translate(-20px,-20px);
        }
        .bg{
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            width: 560px;
            height: 260px;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .sculptor{
            position: absolute;
            width: 4800px;
            height: 1440px;
            animation: sculptorAni 4.60s steps(1) forwards;
        }

        .fairy{
            position: absolute;
            width: 4800px;
            height: 1440px;
            animation: fairyAni 4.32s steps(1) forwards;
        }

        .master{
            position: absolute;
            width: 4800px;
            height: 1440px;
            animation: masterAni 4.20s steps(1) forwards;
        }
        @keyframes sculptorAni {
            0% { left:0px; top:0px; }
            0.88% { left:-240px; top:0px; }
            1.75% { left:-480px; top:0px; }
            2.63% { left:-720px; top:0px; }
            3.51% { left:-960px; top:0px; }
            4.39% { left:-1200px; top:0px; }
            5.26% { left:-1440px; top:0px; }
            6.14% { left:-1680px; top:0px; }
            7.02% { left:-1920px; top:0px; }
            7.89% { left:-2160px; top:0px; }
            8.77% { left:-2400px; top:0px; }
            9.65% { left:-2640px; top:0px; }
            10.53% { left:-2880px; top:0px; }
            11.40% { left:-3120px; top:0px; }
            12.28% { left:-3360px; top:0px; }
            13.16% { left:-3600px; top:0px; }
            14.04% { left:-3840px; top:0px; }
            14.91% { left:-4080px; top:0px; }
            15.79% { left:-4320px; top:0px; }
            16.67% { left:-4560px; top:0px; }
            17.54% { left:0px;top: -240px; }
            18.42% { left:-240px; top:-240px; }
            19.30% { left:-480px; top:-240px; }
            20.18% { left:-720px; top:-240px; }
            21.05% { left:-960px; top:-240px; }
            21.93% { left:-1200px; top:-240px; }
            22.81% { left:-1440px; top:-240px; }
            23.68% { left:-1680px; top:-240px; }
            24.56% { left:-1920px; top:-240px; }
            25.44% { left:-2160px; top:-240px; }
            26.32% { left:-2400px; top:-240px; }
            27.19% { left:-2640px; top:-240px; }
            28.07% { left:-2880px; top:-240px; }
            28.95% { left:-3120px; top:-240px; }
            29.82% { left:-3360px; top:-240px; }
            30.70% { left:-3600px; top:-240px; }
            31.58% { left:-3840px; top:-240px; }
            32.46% { left:-4080px; top:-240px; }
            33.33% { left:-4320px; top:-240px; }
            34.21% { left:-4560px; top:-240px; }
            35.09% { left:0px; top:-480px; }
            35.96% { left:-240px; top:-480px; }
            36.84% { left:-480px; top:-480px; }
            37.72% { left:-720px; top:-480px; }
            38.60% { left:-960px; top:-480px; }
            39.47% { left:-1200px; top:-480px; }
            40.35% { left:-1440px; top:-480px; }
            41.23% { left:-1680px; top:-480px; }
            42.11% { left:-1920px; top:-480px; }
            42.98% { left:-2160px; top:-480px; }
            43.86% { left:-2400px; top:-480px; }
            44.74% { left:-2640px; top:-480px; }
            45.61% { left:-2880px; top:-480px; }
            46.49% { left:-3120px; top:-480px; }
            47.37% { left:-3360px; top:-480px; }
            48.25% { left:-3600px; top:-480px; }
            49.12% { left:-3840px; top:-480px; }
            50.00% { left:-4080px; top:-480px; }
            50.88% { left:-4320px; top:-480px; }
            51.75% { left:-4560px; top:-480px; }
            52.63% { left:0px; top:-720px; }
            53.51% { left:-240px; top:-720px; }
            54.39% { left:-480px; top:-720px; }
            55.26% { left:-720px; top:-720px; }
            56.14% { left:-960px; top:-720px; }
            57.02% { left:-1200px; top:-720px; }
            57.89% { left:-1440px; top:-720px; }
            58.77% { left:-1680px; top:-720px; }
            59.65% { left:-1920px; top:-720px; }
            60.53% { left:-2160px; top:-720px; }
            61.40% { left:-2400px; top:-720px; }
            62.28% { left:-2640px; top:-720px; }
            63.16% { left:-2880px; top:-720px; }
            64.04% { left:-3120px; top:-720px; }
            64.91% { left:-3360px; top:-720px; }
            65.79% { left:-3600px; top:-720px; }
            66.67% { left:-3840px; top:-720px; }
            67.54% { left:-4080px; top:-720px; }
            68.42% { left:-4320px; top:-720px; }
            69.30% { left:-4560px; top:-720px; }
            70.18% { left:0px; top:-960px; }
            71.05% { left:-240px; top:-960px; }
            71.93% { left:-480px; top:-960px; }
            72.81% { left:-720px; top:-960px; }
            73.68% { left:-960px; top:-960px; }
            74.56% { left:-1200px; top:-960px; }
            75.44% { left:-1440px; top:-960px; }
            76.32% { left:-1680px; top:-960px; }
            77.19% { left:-1920px; top:-960px; }
            78.07% { left:-2160px; top:-960px; }
            78.95% { left:-2400px; top:-960px; }
            79.82% { left:-2640px; top:-960px; }
            80.70% { left:-2880px; top:-960px; }
            81.58% { left:-3120px; top:-960px; }
            82.46% { left:-3360px; top:-960px; }
            83.33% { left:-3600px; top:-960px; }
            84.21% { left:-3840px; top:-960px; }
            85.09% { left:-4080px; top:-960px; }
            85.96% { left:-4320px; top:-960px; }
            86.84% { left:-4560px; top:-960px; }
            87.72% { left:0px; top:-1200px; }
            88.60% { left:-240px; top:-1200px; }
            89.47% { left:-480px; top:-1200px; }
            90.35% { left:-720px; top:-1200px; }
            91.23% { left:-960px; top:-1200px; }
            92.11% { left:-1200px; top:-1200px; }
            92.98% { left:-1440px; top:-1200px; }
            93.86% { left:-1680px; top:-1200px; }
            94.74% { left:-1920px; top:-1200px; }
            95.61% { left:-2160px; top:-1200px; }
            96.49% { left:-2400px; top:-1200px; }
            97.37% { left:-2640px; top:-1200px; }
            98.25% { left:-2880px; top:-1200px; }
            99.12% { left:-3120px; top:-1200px; }
            100.00% { left:-3360px; top:-1200px; }
        }
        @keyframes fairyAni {
            0% { left:0p;xtop: 0px };
            0.93% { left:-240px; top:0px; }
            1.87% { left:-480px; top:0px; }
            2.80% { left:-720px; top:0px; }
            3.74% { left:-960px; top:0px; }
            4.67% { left:-1200px; top:0px; }
            5.61% { left:-1440px; top:0px; }
            6.54% { left:-1680px; top:0px; }
            7.48% { left:-1920px; top:0px; }
            8.41% { left:-2160px; top:0px; }
            9.35% { left:-2400px; top:0px; }
            10.28% { left:-2640px; top:0px; }
            11.21% { left:-2880px; top:0px; }
            12.15% { left:-3120px; top:0px; }
            13.08% { left:-3360px; top:0px; }
            14.02% { left:-3600px; top:0px; }
            14.95% { left:-3840px; top:0px; }
            15.89% { left:-4080px; top:0px; }
            16.82% { left:-4320px; top:0px; }
            17.76% { left:-4560px; top:0px; }
            18.69% { left:0px; top:-240px; }
            19.63% { left:-240px; top:-240px; }
            20.56% { left:-480px; top:-240px; }
            21.50% { left:-720px; top:-240px; }
            22.43% { left:-960px; top:-240px; }
            23.36% { left:-1200px; top:-240px; }
            24.30% { left:-1440px; top:-240px; }
            25.23% { left:-1680px; top:-240px; }
            26.17% { left:-1920px; top:-240px; }
            27.10% { left:-2160px; top:-240px; }
            28.04% { left:-2400px; top:-240px; }
            28.97% { left:-2640px; top:-240px; }
            29.91% { left:-2880px; top:-240px; }
            30.84% { left:-3120px; top:-240px; }
            31.78% { left:-3360px; top:-240px; }
            32.71% { left:-3600px; top:-240px; }
            33.64% { left:-3840px; top:-240px; }
            34.58% { left:-4080px; top:-240px; }
            35.51% { left:-4320px; top:-240px; }
            36.45% { left:-4560px; top:-240px; }
            37.38% { left:0px; top:-480px; }
            38.32% { left:-240px; top:-480px; }
            39.25% { left:-480px; top:-480px; }
            40.19% { left:-720px; top:-480px; }
            41.12% { left:-960px; top:-480px; }
            42.06% { left:-1200px; top:-480px; }
            42.99% { left:-1440px; top:-480px; }
            43.93% { left:-1680px; top:-480px; }
            44.86% { left:-1920px; top:-480px; }
            45.79% { left:-2160px; top:-480px; }
            46.73% { left:-2400px; top:-480px; }
            47.66% { left:-2640px; top:-480px; }
            48.60% { left:-2880px; top:-480px; }
            49.53% { left:-3120px; top:-480px; }
            50.47% { left:-3360px; top:-480px; }
            51.40% { left:-3600px; top:-480px; }
            52.34% { left:-3840px; top:-480px; }
            53.27% { left:-4080px; top:-480px; }
            54.21% { left:-4320px; top:-480px; }
            55.14% { left:-4560px; top:-480px; }
            56.07% { left:0px; top:-720px; }
            57.01% { left:-240px; top:-720px; }
            57.94% { left:-480px; top:-720px; }
            58.88% { left:-720px; top:-720px; }
            59.81% { left:-960px; top:-720px; }
            60.75% { left:-1200px; top:-720px; }
            61.68% { left:-1440px; top:-720px; }
            62.62% { left:-1680px; top:-720px; }
            63.55% { left:-1920px; top:-720px; }
            64.49% { left:-2160px; top:-720px; }
            65.42% { left:-2400px; top:-720px; }
            66.36% { left:-2640px; top:-720px; }
            67.29% { left:-2880px; top:-720px; }
            68.22% { left:-3120px; top:-720px; }
            69.16% { left:-3360px; top:-720px; }
            70.09% { left:-3600px; top:-720px; }
            71.03% { left:-3840px; top:-720px; }
            71.96% { left:-4080px; top:-720px; }
            72.90% { left:-4320px; top:-720px; }
            73.83% { left:-4560px; top:-720px; }
            74.77% { left:0px; top:-960px; }
            75.70% { left:-240px; top:-960px; }
            76.64% { left:-480px; top:-960px; }
            77.57% { left:-720px; top:-960px; }
            78.50% { left:-960px; top:-960px; }
            79.44% { left:-1200px; top:-960px; }
            80.37% { left:-1440px; top:-960px; }
            81.31% { left:-1680px; top:-960px; }
            82.24% { left:-1920px; top:-960px; }
            83.18% { left:-2160px; top:-960px; }
            84.11% { left:-2400px; top:-960px; }
            85.05% { left:-2640px; top:-960px; }
            85.98% { left:-2880px; top:-960px; }
            86.92% { left:-3120px; top:-960px; }
            87.85% { left:-3360px; top:-960px; }
            88.79% { left:-3600px; top:-960px; }
            89.72% { left:-3840px; top:-960px; }
            90.65% { left:-4080px; top:-960px; }
            91.59% { left:-4320px; top:-960px; }
            92.52% { left:-4560px; top:-960px; }
            93.46% { left:0px; top:-1200px; }
            94.39% { left:-240px; top:-1200px; }
            95.33% { left:-480px; top:-1200px; }
            96.26% { left:-720px; top:-1200px; }
            97.20% { left:-960px; top:-1200px; }
            98.13% { left:-1200px; top:-1200px; }
            99.07% { left:-1440px; top:-1200px; }
            100.00% { left:-1680px; top:-1200px; }
        }
        @keyframes masterAni {
            0% { left:0p;xtop: 0px };
            0.96% { left:-240px; top:0px; }
            1.92% { left:-480px; top:0px; }
            2.88% { left:-720px; top:0px; }
            3.85% { left:-960px; top:0px; }
            4.81% { left:-1200px; top:0px; }
            5.77% { left:-1440px; top:0px; }
            6.73% { left:-1680px; top:0px; }
            7.69% { left:-1920px; top:0px; }
            8.65% { left:-2160px; top:0px; }
            9.62% { left:-2400px; top:0px; }
            10.58% { left:-2640px; top:0px; }
            11.54% { left:-2880px; top:0px; }
            12.50% { left:-3120px; top:0px; }
            13.46% { left:-3360px; top:0px; }
            14.42% { left:-3600px; top:0px; }
            15.38% { left:-3840px; top:0px; }
            16.35% { left:-4080px; top:0px; }
            17.31% { left:-4320px; top:0px; }
            18.27% { left:-4560px; top:0px; }
            19.23% { left:0px; top:-240px; }
            20.19% { left:-240px; top:-240px; }
            21.15% { left:-480px; top:-240px; }
            22.12% { left:-720px; top:-240px; }
            23.08% { left:-960px; top:-240px; }
            24.04% { left:-1200px; top:-240px; }
            25.00% { left:-1440px; top:-240px; }
            25.96% { left:-1680px; top:-240px; }
            26.92% { left:-1920px; top:-240px; }
            27.88% { left:-2160px; top:-240px; }
            28.85% { left:-2400px; top:-240px; }
            29.81% { left:-2640px; top:-240px; }
            30.77% { left:-2880px; top:-240px; }
            31.73% { left:-3120px; top:-240px; }
            32.69% { left:-3360px; top:-240px; }
            33.65% { left:-3600px; top:-240px; }
            34.62% { left:-3840px; top:-240px; }
            35.58% { left:-4080px; top:-240px; }
            36.54% { left:-4320px; top:-240px; }
            37.50% { left:-4560px; top:-240px; }
            38.46% { left:0px; top:-480px; }
            39.42% { left:-240px; top:-480px; }
            40.38% { left:-480px; top:-480px; }
            41.35% { left:-720px; top:-480px; }
            42.31% { left:-960px; top:-480px; }
            43.27% { left:-1200px; top:-480px; }
            44.23% { left:-1440px; top:-480px; }
            45.19% { left:-1680px; top:-480px; }
            46.15% { left:-1920px; top:-480px; }
            47.12% { left:-2160px; top:-480px; }
            48.08% { left:-2400px; top:-480px; }
            49.04% { left:-2640px; top:-480px; }
            50.00% { left:-2880px; top:-480px; }
            50.96% { left:-3120px; top:-480px; }
            51.92% { left:-3360px; top:-480px; }
            52.88% { left:-3600px; top:-480px; }
            53.85% { left:-3840px; top:-480px; }
            54.81% { left:-4080px; top:-480px; }
            55.77% { left:-4320px; top:-480px; }
            56.73% { left:-4560px; top:-480px; }
            57.69% { left:0px; top:-720px; }
            58.65% { left:-240px; top:-720px; }
            59.62% { left:-480px; top:-720px; }
            60.58% { left:-720px; top:-720px; }
            61.54% { left:-960px; top:-720px; }
            62.50% { left:-1200px; top:-720px; }
            63.46% { left:-1440px; top:-720px; }
            64.42% { left:-1680px; top:-720px; }
            65.38% { left:-1920px; top:-720px; }
            66.35% { left:-2160px; top:-720px; }
            67.31% { left:-2400px; top:-720px; }
            68.27% { left:-2640px; top:-720px; }
            69.23% { left:-2880px; top:-720px; }
            70.19% { left:-3120px; top:-720px; }
            71.15% { left:-3360px; top:-720px; }
            72.12% { left:-3600px; top:-720px; }
            73.08% { left:-3840px; top:-720px; }
            74.04% { left:-4080px; top:-720px; }
            75.00% { left:-4320px; top:-720px; }
            75.96% { left:-4560px; top:-720px; }
            76.92% { left:0px; top:-960px; }
            77.88% { left:-240px; top:-960px; }
            78.85% { left:-480px; top:-960px; }
            79.81% { left:-720px; top:-960px; }
            80.77% { left:-960px; top:-960px; }
            81.73% { left:-1200px; top:-960px; }
            82.69% { left:-1440px; top:-960px; }
            83.65% { left:-1680px; top:-960px; }
            84.62% { left:-1920px; top:-960px; }
            85.58% { left:-2160px; top:-960px; }
            86.54% { left:-2400px; top:-960px; }
            87.50% { left:-2640px; top:-960px; }
            88.46% { left:-2880px; top:-960px; }
            89.42% { left:-3120px; top:-960px; }
            90.38% { left:-3360px; top:-960px; }
            91.35% { left:-3600px; top:-960px; }
            92.31% { left:-3840px; top:-960px; }
            93.27% { left:-4080px; top:-960px; }
            94.23% { left:-4320px; top:-960px; }
            95.19% { left:-4560px; top:-960px; }
            96.15% { left:0px; top:-1200px; }
            97.12% { left:-240px; top:-1200px; }
            98.08% { left:-480px; top:-1200px; }
            99.04% { left:-720px; top:-1200px; }
            100.00% { left:-960px; top:-1200px; }
        }
    }
    .illustrate{
        flex: 1;
        position: relative;
        border-radius: 48px 48px 0 0;
        background: #fff;
        padding-bottom: 160px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .close{
            background: url('@assets/imgs/shopping-close.png') no-repeat;
            background-size: cover;
            width: 24px;
            z-index: 10;
            height: 24px;
            position: absolute;
            right: 34px;
            top: 20px;
        }
        .illustrate-seize{
            position: absolute;
            width: 100%;
            top: 0;
            z-index: 0;
            height: 140px;
            background: linear-gradient(180deg, #D9F4E7 0%, #FFFFFF 100%);
            border-radius: 48px 48px 0 0;
        }
        .grammage{
            z-index: 1;
            position: relative;
            display: flex;
            align-items: center;
            margin-top: 72px;
            margin-left: 64px;
            .icon{
                width: 48px;
                height: 48px;
                background: url('@assets/imgs/leaf.png') no-repeat;
                background-size: cover;
                margin-right: 12px;
            }
            .num{
                font-size: 56px;
                font-family: Arial, Arial;
                font-weight: bold;
                color: #2EB206;
            }
        }
        .illustrate-box{
            overflow: hidden;
            overflow-y: scroll;
            flex: 1;
        }
        .illustrate-name{
            width: 622px;
            font-size: 32px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            color: #4E5B7E;
            margin: 0 auto;
            margin-top: 32px;
        }
        .label{
            width: 72px;
            height: 32px;
            background: linear-gradient(180deg, #FF8888 0%, #E22121 100%);
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 8px 0 0 64px;
            span{
                font-size: 20px;
                font-family: PingFang SC, PingFang SC;
                font-weight: 500;
                color: #FFFFFF;
            }
        }
        .prop-description{
            margin: 16px auto 0;
            width: 622px;
            font-size: 24px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #2EB206;
            line-height: 32px;
        }
        .rarity{
            margin: 16px 0 0 64px;
            font-size: 24px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: rgba(0,0,0,0.6);
            line-height: 32px;
            display: flex;
            align-items: center;
            span{
                margin-right: 16px;
            }
            img{
                width: 32px;
                height: 30px;
            }
        }
        .copywriting-box{
            margin: 48px auto 0;
            width: 670px;
            background: #F5FAF5;
            padding: 32px 24px;
            border-radius: 16px;
            .copywriting-title{
                font-size: 28px;
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                color: #4E5B7E;
                line-height: 32px;
            }
            .count,
            .effective,
            .flow{
                display: flex;
                justify-content: space-between;
                margin-top: 24px;
                .copywriting-label{
                    font-size: 24px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: bold;
                    color: rgba(78,91,126,0.6);
                    line-height: 32px;
                }
                p{
                    width: 504px;
                    font-size: 24px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    color: #4E5B7E;
                    line-height: 36px;
                }
            }
        }
    }
    .illustrate-padding{
        padding-bottom: 200px;
    }
    .shopping-btn{
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 160px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .btn-content{
            width: 568px;
            height: 96px;
            background: linear-gradient(180deg, #E5E5E5 0%, #ACB4AF 100%);
            border-radius: 48px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            .content-top{
                display: flex;
                justify-content: center;
                align-items: center;
            }
            span{
                font-size: 36px;
                font-family: PingFang TC, PingFang TC;
                font-weight: 600;
                color: rgba(67,67,67,0.4);
            }
            .icon{
                width: 44px;
                height: 44px;
                background: url('@assets/imgs/leaf.png') no-repeat;
                background-size: cover;
                margin-right: 15px;
            }
        }
        .highlight{
            background: linear-gradient(0deg, #FDC23E 0%, #FDED9E 100%);
            border-radius: 44px;
            span{
                color: #6A520F;
            }
            p{
                font-size: 20px;
                color: #6A520F;
            }
        }
    }
    .shopping-btn-target_value{
        height: 200px;
        justify-content: space-around;
        background-color: #fff;
        .copyText{
            font-family: PingFang TC, PingFang TC;
            font-weight: 500;
            font-size: 12px;
            color: #000000;
            line-height: 17px;
            img{
                width: 24px;
                height: 24px;
            }
        }
    }
}
</style>