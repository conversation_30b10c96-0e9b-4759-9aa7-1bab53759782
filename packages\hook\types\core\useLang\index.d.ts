import { Ref } from 'vue';
import { Lang } from './assets/index';
declare const currentLang: Ref<"sc" | "tc" | "en">;
type State = Lang & UseLang.Lang;
declare function format(message: string, values: any): string;
export declare function useLang(): {
    state: UseLang.Lang;
    lang: Ref<"sc" | "tc" | "en">;
    setLang: (locale: "sc" | "tc" | "en") => void;
    onLangChange: (cb: (lang: Ref<"sc" | "tc" | "en">) => void) => void;
    format: typeof format;
    setMessages: (messages: Record<"sc" | "tc" | "en", UseLang.Lang>) => void;
};
export type LangState = State;
export type LangValue = typeof currentLang;
export type LangFormat = typeof format;
export declare namespace UseLang {
    interface Lang {
    }
}
declare module '@vue/runtime-core' {
    interface ComponentCustomProperties {
        $lang: LangState;
        $langValue: LangValue;
        $langFormat: LangFormat;
    }
}
export {};
