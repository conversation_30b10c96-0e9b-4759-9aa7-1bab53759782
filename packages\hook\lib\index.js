import { BaseLinkVue } from './base/baseLikeVue';
export * from './core';
export * from './ui';
import { useDialog } from './ui';
import core from './core/install';
import { install as langInstall } from './core/useLang/install';
import { install as routerInstall } from './core/useRouter/install';
import ui from './ui/install';
const { app: dialogApp } = useDialog();
class Hook extends BaseLinkVue {
    app;
    constructor(app) {
        super();
        this.app = app;
        const config = Object.assign({}, this.app.config.globalProperties, dialogApp.config.globalProperties);
        Object.assign(this.app.config.globalProperties, config);
        dialogApp.config.globalProperties = this.app.config.globalProperties;
        langInstall(app);
        routerInstall(app);
    }
    use(plugin, ...options) {
        this.app.use(plugin, options);
        core.use(plugin, options);
        // ui.use(plugin, options)
        return this;
    }
    directive(name, directive) {
        this.app.directive(name, directive);
        core.directive(name, directive);
        ui.directive(name, directive);
        return this;
    }
    component(name, component) {
        this.app.component(name, component);
        core.component(name, component);
        ui.component(name, component);
        return this;
    }
}
export function install(app) {
    return new Hook(app);
}
