<template>
    <div class="reductionRecord">
        <navComponent :returnType="2" :showTransmit="false" />
        <div class="top-img"></div>

        <div class="title">
            <div class="head">
                <img class="logo" :src="useUserStore().headLogo" />
                <img class="headlogoFrame" :src="useUserStore().frameUrl" v-if="useUserStore().frameUrl" alt="" />
            </div>

            <div class="total">
                <div class="text">{{ state.record.减排值 }}</div>
                <!-- 减排值数量 -->
                <div class="value">{{ numberToThousands(energy_user) }}</div>
            </div>
        </div>

        <div class="content">
            <div class="tabs">
                <a
                    :class="{
                        tab: true,
                        active: item.active
                    }"
                    v-for="(item, index) in tabs"
                    @click="changeTab(item, index)"
                >
                    {{ item.text }}
                </a>
            </div>
            <div class="time">
                <span @click="showActionSheet">
                    {{ startTime == endTime ? startTime : startTime + ' ' + state.record.至 + ' ' + endTime }}
                </span>

                <img class="row" src="@imgs/reductionRecord/row.png" @click="showActionSheet" />
            </div>

            <div class="cumulative">
                <span v-if="tabIndex == 0">{{ state.record.累计领取 }} {{ energy_time + 'g' }}</span>
                <span v-else>{{ $lang.record.累计消耗 }}: {{ buyTotal }}g</span>
            </div>
        </div>
        <template v-if="tabIndex == 0">
            <div v-if="nowRecordList.length" class="lists">
                <div
                    v-for="(item, index) in nowRecordList"
                    :key="item.time"
                    class="list-item"
                    :class="{ obb: !(index % 2) }"
                >
                    <div class="left">
                        <img class="icon" :src="item.icon" />

                        <div class="mid">
                            <div class="item-title">{{ item.title }}</div>

                            <div class="item-time">{{ item.time }}</div>
                        </div>
                    </div>

                    <div class="item-value">{{ item.value + 'g' }}</div>
                </div>
            </div>

            <div v-else class="empty">
                <img class="empty-img" :src="$imgs['record-none.png']" alt="" />

                <div class="empty-text">{{ state.record.没有领取记录 }}</div>

                <div class="btn" @click="showPicker = true">{{ state.record.选择时间 }}</div>
            </div>
        </template>
        <template v-else>
            <div class="lists" v-if="buyHistory.total > 0">
                <div v-for="(item, index) in buyHistory.records" class="list-item" :class="{ obb: !(index % 2) }">
                    <div class="left">
                        <div class="icon-box">
                            <div class="expired" v-if="item.status == 3">
                                <span>{{ state.shopping.已失效 }}</span>
                            </div>
                            <img v-if="item.commodity.target !== 1" class="icon" :src="$imgs[`reductionRecord/${item.commodity.code}.png`]" />
                            <img v-else :src="item.commodity.metadata.iconUrl" class="icon" alt="">
                        </div>
                        <div class="mid">
                            <!-- <div class="item-title">{{ $langValue.value === 'tc' ? item.commodity.metadata.name : item.commodity.metadata[`name${$langValue.value}`] }}</div> -->
                            <div class="item-title">{{ item.commodity.metadata[langObj[lang]] }}</div>
                            <div class="item-time">{{ item.created_at }}</div>
                            <!-- TODO加上&&失效状态 -->
                            <div v-if="item.expired_at && item.status != 3">
                                <div class="expired_at">{{ state.shopping.失效時間 }}{{ item.expired_at }}</div>
                                <div class="be-about-to" v-if="isLessThanDay(item.expired_at)">({{ state.shopping.即將過期 }})</div>
                            </div>
                        </div>
                    </div>

                    <div class="card-right">
                        <div class="item-value">-{{ item.energy_value }}g</div>
                        <div class="see-detail" :class="{'see-detail-expired':item.status == 3}" @click="seeDetail(item)">
                            {{ state.shopping.查看詳情}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="empty" v-else>
                <img class="empty-img" :src="$imgs['record-none.png']" alt="" />

                <div class="empty-text">{{ $lang.record.暂无购买纪录 }}</div>

                <div class="btn" @click="toOffShelf">{{ $lang.record.前往减碳商城 }}</div>
            </div>
        </template>

        <ConfigProvider :theme-vars="popUpConfig">
            <ActionSheet v-model:show="showPicker" @close="cancel" @closed="reInit">
                <div class="picker">
                    <div class="picker-top">
                        <div
                            class="picker-top-text underline"
                            :class="{ 'picker-top-select': !selectSOE }"
                            @click="changeSelect(false)"
                        >
                            {{ selectGroup[0] }}
                        </div>

                        <div class="picker-top-text">{{ state.record.至 }}</div>

                        <div
                            class="picker-top-text underline"
                            :class="{ 'picker-top-select': selectSOE }"
                            @click="changeSelect(true)"
                        >
                            {{ selectGroup[1] }}
                        </div>
                    </div>

                    <div class="picker-tip">
                        {{ tabIndex == 0 ? state.record.三个月的领取记录 : state.record.三个月的消费记录 }}
                    </div>

                    <Picker
                        ref="timePicker"
                        :columns="timeList"
                        :item-height="92"
                        :visible-item-count="3"
                        @change="changeTime"
                    >
                        <template #toolbar></template>
                    </Picker>

                    <div class="btn-group">
                        <div class="cancel" @click="cancel">{{ state.record.取消 }}</div>

                        <div class="confirm" @click="confirm">{{ state.record.确定 }}</div>
                    </div>
                </div>
            </ActionSheet>
        </ConfigProvider>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, Ref, onBeforeMount, computed } from 'vue'
import { numberToThousands } from '@/util/unity'
import { ActionSheet, ConfigProvider, Picker } from 'vant'
import { useDayjs, useToast, useLang, useRouter, useDialog,useEventBus } from 'hook'
import { useTaskStore, useTreeStore, useUserStore } from '@/store'
import { GetBuyHistoryReturnType } from '@/store/modules/task/type'
import { logEventStatistics } from '@via/mylink-sdk'
import navComponent from '@/components/navComponent.vue'
import shoppingDialog from '@/components/shoppingDialog.vue';
import { imgs } from '@/assets/imgs'
import router from '@/router'

// getRecord
const eventBus = useEventBus()
const { dayjs } = useDayjs()
const { toast } = useToast()
const { state, lang } = useLang()
const taskStore = useTaskStore()
const { currentRoute } = useRouter()
const dialog = useDialog({
    shoppingDialog
})
let recordList = [
    {
        time: '2022.06.23 13:23',
        title: '电子支付',
        value: 10
    }
]

let langObj = {
    sc: 'nameSc',
    en: 'nameEn',
    tc: 'name'
}

const totalCarbon = ref(1000) //用户需要的减碳值
const nowNum = ref(0) //积累领取的减碳值
const startTime = ref('') //开始时间
const endTime = ref(dayjs().format('YYYY-MM')) //结束时间
const showPicker = ref(false) //设置时间sheet
const selectSOE = ref(false) //
const energy_user = ref(0) //用户的减碳值
const energy_time = ref(0) //累计减碳值
const timePicker: Ref<HTMLElement | null | any> = ref(null)
let timeList: any = ref([])
let selectGroup = ref<string[]>(new Array(2))
let nowRecordList = ref([])
const buyHistory = ref<GetBuyHistoryReturnType>({} as GetBuyHistoryReturnType) // 购买历史
const buyTotal = computed(() => {
    return buyHistory.value?.records?.reduce((pre, item) => {
        pre += item.energy_value
        return pre
    }, 0)
})

const popUpConfig = {
    popupBackgroundColor: 'none'
}

let originTimeList: any = {}
let yearList: any
let cannotClick = false
let monthList: Array<string> = []
let yearToMonth: any = {} //区间范围

onBeforeMount(() => {
    //減碳值記錄頁面訪問量
    logEventStatistics('Mygarden_carbonreduction_exp_page')
    init()
    eventBus.on('goUse', () => {
        router.push({
            path:'/package',
            query: {
                "hideNavigationBar": 'true'
            }
        })
    })
})

const useTabsFn = () => {
    const tabs = ref([
        {
            active: false,
            text: state.record.领取记录
        },
        {
            active: false,
            text: state.record.购买纪录
        }
    ])
    const queryIndex = currentRoute?.query.tab ? (isNaN(+currentRoute?.query.tab) ? 0 : +currentRoute?.query.tab) : 0
    const tabIndex = ref(queryIndex)
    tabs.value[tabIndex.value].active = true
    const changeTab = async (item: (typeof tabs.value)[0], index: number) => {
        if (tabIndex.value == index) {
            return
        }
        tabIndex.value = index
        tabs.value.forEach((tab) => {
            tab.active = false
        })
        item.active = true
        if (tabIndex.value == 0) {
            logEventStatistics('garden_total_collect')
            nowRecordList.value = await timeFilter()
        } else {
            logEventStatistics('garden_total_history')
            if (startTime.value == '') {
                startTime.value = dayjs(endTime.value).format('YYYY-MM').toString()
            }
            buyHistory.value = await taskStore.getBuyHistory(startTime.value, endTime.value)
        }
    }
    return {
        tabs,
        tabIndex,
        changeTab
    }
}
const { tabs, tabIndex, changeTab } = useTabsFn()

const init = async () => {
    if (!useUserStore().inLogin) {
        return await useUserStore().login()
    }
    let info = await taskStore.getInfo()
    energy_user.value = info.user.energy_total

    let tempTime: any = dayjs(endTime.value)
    let init: any = null
    if (dayjs(dayjs(endTime.value).subtract(2, 'month')).isSameOrBefore(dayjs('2022-10'))) {
        init = dayjs('2022-10')
    } else {
        init = dayjs(dayjs(endTime.value).subtract(2, 'month').format('YYYY-MM'))
    }

    let last = dayjs(endTime.value)
    if (!init.isSame(last)) {
        //判断两个日期是否相同
        tempTime = init
    }
    startTime.value = tempTime.format('YYYY-MM').toString()
    //处理选择时间的区间
    while (init.isSameOrBefore(last)) {
        //相同或之前
        let now = init.format('YYYY').toString()
        let nowMonth = init.format('M').toString()
        if (!originTimeList[now]) {
            originTimeList[now] = []
        }
        monthList.push(nowMonth)
        originTimeList[now].push([nowMonth, monthList.length - 1])
        init = init.add(1, 'month')
    }
    //处理当前年的区间
    let year = tempTime.format('YYYY').toString()
    let month = tempTime.format('M').toString()
    yearList = Object.keys(originTimeList)
    timeList.value = [
        { values: yearList, defaultIndex: yearList.indexOf(year) },
        {
            values: monthList,
            defaultIndex: originTimeList[year].find((val: Array<string>) => val[0] == month)[1]
        }
    ]

    let point = 0

    for (let y of yearList) {
        yearToMonth[y] = [point, point + originTimeList[y].length - 1]
        point += originTimeList[y].length
    }
    console.log(timeList.value, 'timeList')

    selectGroup.value[0] = startTime.value
    selectGroup.value[1] = endTime.value

    if (tabIndex.value == 0) {
        nowRecordList.value = await timeFilter()
    } else if (tabIndex.value == 1) {
        buyHistory.value = await taskStore.getBuyHistory(startTime.value, endTime.value)
    }
}

// 改变列表栏的具体函数
const changeTime = (value: Array<string>, currentIndex: number | number[]) => {
    let [year, month] = value
    let [yearIndex, monthIndex] = timePicker.value ? timePicker.value.getIndexes() : [-1, -1]
    if (!currentIndex) {
        let nowMonthList: Array<string> = originTimeList[year]

        // 处理year的改变
        let nowItem = nowMonthList.find((val) => val[0] == month)
        if (!nowItem) {
            if (year == yearList[0]) {
                timePicker.value.setColumnIndex(Number(!currentIndex), 0)
            } else {
                timePicker.value.setColumnIndex(Number(!currentIndex), monthList.length - 1)
            }
        } else {
            timePicker.value.setColumnIndex(Number(!currentIndex), nowItem[1])
        }
        month = monthList[timePicker.value.getColumnIndex(Number(!currentIndex))]

        let index = yearList.indexOf(year)
        if (yearIndex != index) {
            timePicker.value.setColumnIndex(Number(currentIndex), index)
        }
    } else {
        // 处理month的改变
        for (let i = 0; i < yearList.length; i++) {
            if (monthIndex >= yearToMonth[yearList[i]][0] && monthIndex <= yearToMonth[yearList[i]][1]) {
                timePicker.value.setColumnIndex(Number(!currentIndex), i)
                year = yearList[i]
                break
            }
        }
    }

    selectGroup.value[Number(selectSOE.value)] =
        year +
        '-' +
        dayjs(year + '-' + month)
            .format('MM')
            .toString() //滑动事件组件
}

// 转换选择开始时间还是结束时间，可用于重新初始化列表数据
const changeSelect = (bol: boolean) => {
    if (bol == selectSOE.value) {
        let now = selectGroup.value[Number(selectSOE.value)].split('-')
        changeTime(now, 0)
    } else {
        selectSOE.value = !selectSOE.value
        let now = selectGroup.value[Number(selectSOE.value)].split('-')
        now[1] = dayjs(now[0] + '-' + now[1])
            .format('M')
            .toString()
        changeTime(now, 0)
    }
}

const reInit = () => {
    if (cannotClick) {
        selectGroup.value = [startTime.value, endTime.value]
        cannotClick = false
    }
}

const cancel = () => {
    showPicker.value = false
    cannotClick = true
}

const confirm = async () => {
    if (dayjs(selectGroup.value[0]).isAfter(dayjs(selectGroup.value[1]))) {
        ;[selectGroup.value[0], selectGroup.value[1]] = [selectGroup.value[1], selectGroup.value[0]]
    }
    let temp = dayjs(selectGroup.value[0]).add(2, 'month')
    if (temp.isBefore(dayjs(selectGroup.value[1]))) {
        ;[selectGroup.value[0], selectGroup.value[1]] = [selectGroup.value[1], selectGroup.value[0]]
        toast(state.record.三个月的领取记录)
    } else {
        showPicker.value = false
        startTime.value = selectGroup.value[0]
        endTime.value = selectGroup.value[1]
        if (tabIndex.value == 0) {
            nowRecordList.value = await timeFilter()
        } else if (tabIndex.value == 1) {
            buyHistory.value = await taskStore.getBuyHistory(startTime.value, endTime.value)
        }
    }
    console.log(startTime.value, endTime.value, '时间')
}

// 弹出时间框
const showActionSheet = () => {
    showPicker.value = true
}
// 获取记录对应图片
const timeFilter = async () => {
    let res = await taskStore.getRecord(startTime.value, endTime.value) //让后端增加nft数据
    console.log(res, 111111111111)

    let arr = res.records.map((item) => {
        let icon = imgs['work/0.png']
        switch (item.source) {
            case 1: //缴费类
                icon = imgs['work/1.png']
                break
            case 2: //增值类
                icon = imgs['work/2.png']
                break
            case 3: //天天优惠
                icon = imgs['work/3.png']
                break
            case 4: //积分商城
                icon = imgs['work/4.png']
                break
            case 5: //wewalk
                icon = imgs['work/5.png']
                break
            case 6: //每日登录
                icon = imgs['work/6.png']
                break
            case 7: //盲盒抽奖
                icon = imgs['work/7.png']
                break
            case 8: //碳值卡
                icon = imgs['work/8.png']
                break
            case 9: //浇水
                icon = imgs['work/9.png']
                break
            case 10: //NFT
                icon = imgs['work/10.png']
                break
            case 11: //遊戲現金消費
                icon = imgs['work/11.png']
                break
            case 12: //每日遊戲遊玩
                icon = imgs['work/12.png']
                break
            case 13: // 任务栏任务
                icon = imgs[`work/${item.source_icon}.png`]
                break
            case 16: // 广告
                icon = imgs[`work/${item.source_icon}.png`]
                break
            case 17: // 广告
                icon = imgs[`work/${item.source_icon}.png`]
                break
            case 18: // 广告
                icon = imgs[`work/${item.source_icon}.png`]
                break
        }
        if (item.multiple >= 2) {
            icon = imgs['work/16_1.png']
        }
        if (item.source == 7) {
            //优先级更高
            icon = imgs['work/7.png']
        }
        if (item.linked_friend) {
            icon = imgs['work/18_get.png']
            item.source_text = `${state.friend.拾取}[${
                item.linked_friend.name.length <= 20
                    ? item.linked_friend.name
                    : item.linked_friend.name.slice(0, 20) + '……'
            }]${state.friend.減碳值}`
        }
        return {
            time: item.clicked_at,
            title: item.source_text ? item.source_text : '',
            value: item.energy_value,
            icon: icon
        }
    })
    energy_time.value = 0
    for (let i = 0; i < arr.length; i++) {
        energy_time.value += arr[i].value
    }
    let font = arr.findIndex((val) => dayjs(dayjs(val.time).format('YYYY-MM')).isSameOrAfter(startTime.value))
    if (font == -1) return []
    else {
        let last = arr.findIndex((val) => dayjs(dayjs(val.time).format('YYYY-MM')).isAfter(endTime.value))
        if (last == -1) return arr.slice(font)
        else return arr.slice(font, last)
    }
}

// 去减碳商城
const toOffShelf = () => {
    router.push({
        path: '/mall',
        query: {
            hideNavigationBar: 'true'
        }
    })
}

watch(
    () => showPicker.value,
    (val, oldVal) => {
        if (val) {
            selectSOE.value = false
            if (timePicker.value) changeSelect(false) //重新打开的时候要重新初始化数据
        }
    }
)

const seeDetail = (item)=>{
    logEventStatistics('garden_history_detail')
    dialog.get('shoppingDialog').show({
        item: {
            num: item.energy_value,
            name: item.commodity.metadata[lang.value == 'tc' ? 'name' : (lang.value == 'en' ? 'nameEn' : 'nameSc')],
            label: item.commodity.newest,
            description: item.commodity.metadata[lang.value == 'tc' ? 'description' : (lang.value == 'en' ? 'descriptionEn' : 'descriptionSc')],
            rarity: item.commodity.rarity,
            countText: item.commodity.metadata[lang.value == 'tc' ? 'countText' : (lang.value == 'en' ? 'countTextEn' : 'countTextSc')],
            effectiveText: item.commodity.metadata[lang.value == 'tc' ? 'effectiveText' : (lang.value == 'en' ? 'effectiveTextEn' : 'effectiveTextSc')],
            flowText: item.commodity.metadata[lang.value == 'tc' ? 'flowText' : (lang.value == 'en' ? 'flowTextEn' : 'flowTextSc')],
            code: item.commodity.code,
            target:item.commodity.target,
            iconUrl:item.commodity.metadata.iconUrl
        },
        status: item.status == 3 ? item.status : 2,
        id: item.id,
        target_value:item.target_value,
        jump_url:item.commodity.jump_url,
        expired_at:item.expired_at
    })
}

const isLessThanDay = (expired_at)=>{
   let dayTime = 24 * 60 *60 * 1000
   let res = new Date().getTime() + dayTime > new Date(expired_at).getTime()
   return res
}

</script>

<style lang="less" scoped>
@import './record.scss';
</style>
