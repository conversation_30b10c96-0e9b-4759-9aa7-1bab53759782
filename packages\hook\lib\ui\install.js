import { BaseLinkVue } from '../base/baseLikeVue';
import dialog from './dialog/install';
class UI extends BaseLinkVue {
    use(plugin, ...options) {
        dialog.use(plugin, options);
        return this;
    }
    directive(name, directive) {
        dialog.directive(name, directive);
        return this;
    }
    component(name, component) {
        dialog.component(name, component);
        return this;
    }
}
export default new UI();
