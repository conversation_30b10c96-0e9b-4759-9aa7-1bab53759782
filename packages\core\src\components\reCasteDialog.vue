<template>
    <div class="content">
        <h1>{{ state.nft.recastTitle }}</h1>
        <p class="showImg">
            <img src="@/assets/imgs/NTFDetail/nft_change.png" />
        </p>
        <div class="bottom">
            <div class="btn" @click="goCasting">
                {{ state.nft.goCasting }}
            </div>
        </div>

        <p class="back">{{ state.dialog.点击屏幕返回 }}</p>
    </div>
</template>

<script lang="ts" setup>
/** nft重新铸造弹窗 */
import { useLang, useRouter } from 'hook'
const { router, currentRoute } = useRouter()
const { state } = useLang()
let emit = defineEmits(['close'])
/** 前往铸造页 */
const goCasting = () => {
    emit('close')
    router.push({
        path: '/achievement',
        query: {
            hideNavigationBar: 'true'
        }
    })
}
</script>

<style lang="less" scoped>
.content {
    padding: 30px 30px 5px 30px;
    width: 544px;
    background: #ffffff;
    border-radius: 24px 24px 24px 24px;
    text-align: center;

    h1 {
        font-weight: 600;
        font-size: 32px;
        color: #4e5b7e;
    }

    .showImg {
        img {
            width: 480px;
        }
    }

    .bottom {
        display: flex;
        justify-content: center;

        .btn {
            width: 220px;
            height: 56px;
            background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
            border-radius: 48px 48px 48px 48px;
            font-size: 28px;
            color: #ffffff;
            text-align: center;
            line-height: 56px;
        }
    }

    > * {
        margin-bottom: 30px;
    }

    .back {
        position: absolute;
        font-size: 28px;
        font-weight: bold;
        color: #ffffff;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -160px;
    }
}
</style>
