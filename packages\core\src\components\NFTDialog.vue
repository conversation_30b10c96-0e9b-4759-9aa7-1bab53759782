<template>
    <!-- nft铸造结果弹窗 -->
    <div class="nft__results">
        <div class="nft__succeed" v-if="results">
            <!-- <p>{{state.nft.鑄造完成}}</p> -->
            <p>{{ state.nft.你已鑄造成功可獲得以下道具 }}</p>
            <div class="card-box">
                <p>{{ state.dialog.稀有度 }}</p>
                <div class="starList">
                    <img
                        v-for="i in rarity"
                        :key="i"
                        class="star"
                        :src="$imgs['plantSelection/star.png']"
                    />
                </div>
                <img class="flows" :src="$imgs[`props/标题装饰/${code}.png`]" alt="">
                <div class="name">{{ state.prop[code] }}</div>
            </div>
            <span>{{ state.nft.獎勵已放進你的背包快去使用吧 }}</span>
            <div class="btn">
                <button @click="goPackage" class="goPackage">{{state.dialog.前往背包}}</button>
                <button @click="goHome" class="goHome">{{state.nft.去MyHome佈置}}</button>
                <button @click="seeNFT" class="seeNFT">{{state.nft.查看此NFT}}</button>
            </div>
            <span class="nft__continue">{{state.dialog.点击屏幕返回}}</span>
        </div>
        <div class="nft__fail" v-else-if="results === false">
            <p>{{state.nft.鑄造失敗}}</p>
            <img src="../assets/imgs/失败.png" alt="">
            <p class="fs--35">[{{ cause }}]</p>
            <div class="btn">
                <button class="fail" @click="recover">{{state.nft.返回}}</button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useDialog, useLang,useRouter,useEventBus,useToast,useEnvConfig } from 'hook';
import { defineComponent, onMounted, inject, ref, watch, getCurrentInstance } from 'vue'
import updateDialog from '@/components/updateDialog.vue'
import { logEventStatistics,getAppVersion } from "@via/mylink-sdk";
import { imgs } from "@assets/imgs";
const { state,lang } = useLang()
import { useNFTStore } from '@/store'
const { router } = useRouter()
const  eventBus  = useEventBus()
const dialog = useDialog({
    updateDialog
})
const props=defineProps({
    results:{
        type:String || Number ||null
    },
    cause:{
        type:String
    },
    code:{
        type:String,
        required:true
    },
    rarity:{
        type:Number,
        required:true
    }
})
// 返回事件
const recover = () => {
    logEventStatistics('garden_fail_back')
    eventBus.emit('recover')
}

const seeNFT = ()=>{
    console.log(props.results);
    logEventStatistics('garden_success_check_nft')
    if (useEnvConfig().RUN_ENV == 'develop' ||  useEnvConfig().RUN_ENV == 'uat') {
        // location.href = "http://*************/nftweb/#/third-party?origin=1&target=numberExchange&forceHideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>"
        location.href = `openurl-modal://https://*************/nftweb/#/third-party?origin=4&target=nftDetail&groupId=${props.results}&forceHideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>`
    }else{
        location.href = `openurl-modal://https://cdn.mylink.hk.chinamobile.com/blockchain/link/nft/index.html#/third-party?origin=4&target=nftDetail&groupId=${props.results}&forceHideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>`
    }
}

let isPat = useEnvConfig().RUN_ENV == 'production' || useEnvConfig().RUN_ENV == 'beta' ? true : false
let uatLink = {
    home: `openurl-modal://https://*************/activity/myCity/#/myHome?lang=${
        useLang().lang.value
    }&forceHideNavigationBar=true`,
    home2: 'openurl-modal://https://<<cmcchkhsh_host>>/activity/myCity/#/newMyHome?lang=<<cmcchkhsh_cmplang>>&hideNavigationBar=true',
    home3: `openhkhshlogin://cmcchkhsh://metaverse?url=myhome`
}
let patLink = {
    home: `openurl-modal://https://mylink.komect.com/activity/myCity/#/myHome?lang=${
        useLang().lang.value
    }&forceHideNavigationBar=true`,
    home2: 'openurl-modal://https://<<cmcchkhsh_host>>/activity/myCity/#/newMyHome?lang=<<cmcchkhsh_cmplang>>&hideNavigationBar=true',
    home3: `openhkhshlogin://cmcchkhsh://metaverse?url=myhome`
}
const goHome = () => {
    logEventStatistics('garden_decorate_my_home')
    const version = getAppVersion() //获取版本信息
    //   const ver = 960
    const [first, seconds] = version.split('.')
    const ver = Number(version.split('.').join(''))
    if (ver >= 1000) {
        let link = isPat ? patLink : uatLink
        window.location.href = link['home3']
        return
    } else {
        dialog.get('updateDialog').show({}, { maskClose: false })
        return
    }
    // let link = isPat ? patLink : uatLink
    // window.location.href = link['home']
}

const goPackage = ()=>{
    router.replace({
        path:'/package',
        query: {
            hideNavigationBar: 'true',
            changeTitle:'true'
        }
    })
}
</script>

<style lang='less' scoped>
.nft__results{
    display: flex;
    align-items: center;
    height: 80vh;
}
.nft__succeed{
    color: #fff;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    p{
        width: 528px;
        margin-bottom: 66px;
        font-weight: bold;
        font-size: 42px;
        color: #FFFFFF;
        text-align: center;
    }
    span{
        font-weight: bold;
        font-size: 28px;
        color: #FFFFFF;
        line-height: 28px;
        text-align: center;
        margin-top: 24px;
    }
    img{
        margin-top: 150px;
        width: 100%;
    }
    .btn{
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        font-size: 30px;
    }
}
.nft__fail{
    width: 100vw;
    color: #fff;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    .fs--35{
        font-size: 35px;
    }
    img{
        margin-top: 150px;
        width: 50%;
    }
    .btn{
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        font-size: 30px;
    }
}
.nft__continue{
    font-size: 30px;
    color: #fff;
    position: absolute;
    font-size: 28px;
    font-family: PingFang SC-Bold, PingFang SC;
    font-weight: bold;
    left: 50%;
    transform: translate(-50%, 0);
    bottom: 0px;
    white-space: nowrap;
    margin-top: 34px;
}
.goPackage{
    width: 430px;
    height: 84px;
    background: linear-gradient( 180deg, #FDDD3E 0%, #FBB629 100%);
    border-radius: 48px 12px 48px 12px;
    margin-top: 34px;
}
.goHome{
    width: 430px;
    height: 84px;
    background: linear-gradient( 180deg, #56D3EB 0%, #34AADF 100%);
    border-radius: 48px 12px 48px 12px;
    margin-top: 34px;
}
.seeNFT{
    width: 430px;
    height: 84px;
    background: linear-gradient( 180deg, #FFFFFF 0%, #FFFFFF 97%, #FFFFFF 100%);
    border-radius: 48px 12px 48px 12px;
    color: #6A43D1;
    margin-top: 34px;
}
.card-box{
    background: url('@assets/imgs/props/card.png') no-repeat;
    background-size: cover;
    width: 334px;
    height: 488px;
    display: flex;
    align-items: center;
    flex-direction: column;
    p{
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 26px;
        color: #FFFFFF;
        text-align: center;
        margin-top: 10px;
        margin-bottom: 0;
    }
    .starList{
        display: flex;
        height: 42px;
        align-items: center;
        justify-content: center;
        margin-top: 70px;
        img{
            margin: 0;
        }
    }
    .flows{
        width: 100px;
        height: 100px;
        margin-top: 64px;
    }
    .name{
        margin-top: 60px;
        font-family: PingFang TC, PingFang TC;
        font-weight: 600;
        font-size: 32px;
        color: #4E5B7E;
        text-align: center;
    }
}
.fail{
    width: 536px;
    height: 80px;
    background: #6A43D1;
    border-radius: 40px 40px 40px 40px;
    border: none;
    margin-top: 48px;
}
</style>
