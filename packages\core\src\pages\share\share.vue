<template>
  <div class="home">
      <navComponent :returnType="2" :showTransmit="false"/>
      <div class="banner">
          <div class="title">{{state.share_.低碳行为绿色种植}}</div>
          <p class="trip">{{state.share_.快来花园开启绿色种植之旅吧}}</p>
          <!-- 减碳球 -->
          <div class="bubBalls">
              <div v-for="index in 6" :key="index" 
              :class="{
               'bub': true,
               'bub1': index == 1,
               'bub2': index == 2,
               'bub3': index == 3,
               'bub4': index == 4,
               'bub5': index == 5,
               }">
                  <div class="ball">+5g</div>
                  <p class="text">
                      {{ state.shareWork[index - 1].includes(' ')?
                            state.shareWork[index - 1] : (
                                state.shareWork[index - 1].length > 8 ?
                                state.shareWork[index - 1].slice(0, 8) + '\n' + state.shareWork[index - 1].slice(8) :
                                state.shareWork[index - 1]
                            )
                         }}
                  </p>
              </div>
          </div>
          <div class="card">
              <img class="b_title" :src="$imgs['green-bg.png']" alt="">
              <p class="c_title">{{state.share_.成长奖励}}</p>
              <div class="contain">
                  <div class="row">
                      <div class="col-title">{{state.share_.装饰类}}</div>
                      <div class="col-title">{{state.share_.工具类}}</div>
                      <div class="col-title">{{state.share_.生活类}}</div>
                        
                        
                  </div>

                  <div class="row">
                      <div class="ab">
                            <div class="aword">
                                <img :src="$imgs[`props/挂饰/item8.png`]" alt="">
                            </div>
                            <p class="propName"> {{state.propsReward['item8'].propName}}</p>
                      </div>
                      
                        <div class="ab">
                            <div class="aword">
                                <img :src="$imgs[`props/稻草人/daocaoren1.png`]" alt="">
                            </div>  
                            <p class="propName"> {{state.propsReward['daocaoren1'].propName}}</p>
                        </div>
                        <div class="ab">
                            <div class="aword zhuoyi">
                                <img :src="$imgs[`props/桌子/zhuozi5.png`]" alt="">
                                <img :src="$imgs[`props/椅子/yizi5.png`]" alt="">
                            </div>
                            <p class="propName"> {{state.share_.桌椅}}</p>
                        </div>
                  </div>

                  <div class="row">
                        <div class="ab">
                            <div class="aword">
                                <img :src="$imgs[`props/喷水池/penshuichi4.png`]" alt="">
                            </div>
                            <p class="propName"> {{state.propsReward['penshuichi4'].propName}}</p>
                        </div>
                        <div class="ab">
                            <div class="aword">
                                <img :src="$imgs[`props/灯柱/dengzhu2.png`]" alt="">
                            </div>    
                            <p class="propName"> {{state.propsReward['dengzhu2'].propName}}</p>
                        </div>
                        <div class="ab">
                            <div class="aword">
                                <img :src="$imgs[`props/木头车/mutouche1.png`]" alt="">
                            </div>
                            <p class="propName"> {{state.propsReward['mutouche1'].propName}}</p>
                        </div>
                  </div>

              </div>
          </div>
          
      </div>
      <div class="bottom">
          <div class="button" @click="toapp">
              {{state.share_.打开MyLink}}
          </div>
      </div>
  </div>
</template>

<script setup>
import { openApp } from '@via/mylink-sdk'
import { useLang,useEnvConfig } from 'hook'
import navComponent from '@/components/navComponent.vue';
const envConfig = useEnvConfig()

const { state } = useLang()
const toapp = () => {
    const urlObj = new URL(location.href.split('#')[0])
    // const shareUrl = urlObj.toString() + '#/home?hideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>'
    const shareUrl = urlObj.toString() + '#/home?&hideNavigationBar=true'
    // if (envConfig.RUN_ENV == 'develop' || envConfig.RUN_ENV == 'uat') {
    //     location.href = `http://*************/mylink/#/l/?link=${encodeURIComponent(location.href)}`
    // }else{
    //     location.href = `https://mylink.komect.com/mylink/#/l/?link=${encodeURIComponent(location.href)}`
    // }
    openApp(shareUrl)
}
</script>

<style lang="less" scoped>
@import './share.scss';
</style>