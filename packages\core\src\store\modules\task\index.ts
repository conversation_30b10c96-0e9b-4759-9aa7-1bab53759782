import { defineStore } from 'pinia'
import actions from './actions'
import type { taskType } from './type'

export const useTaskStore = defineStore('taskStore', {
    state: (): taskType => ({
        action_upgrade: [],
        acIntStu: false,
        openTask:false,
        limitTask:false,
        item:{
            "code":'',
            "energy_value":0,
            "id":0,
            "metadata":{},
            "name":"",
            "newest":false,
            "rarity":0,
            "status":0,
            "stock_count":0
        },
        // 批次id
        taskId:0,
        upLevelPlantList:[],
        banner:[]
    }),
    
    actions
})
