import * as api from './api'
import { useEnvConfig, useApi, useWindow } from 'hook'
import { inApp, getUserInfo, logEventStatistics, AliLog, getSystem } from '@via/mylink-sdk'
import { useStorage, useLang, useRouter } from 'hook'
import { adawardType } from './type'

export function initAdaward(this:adawardType) {
  api.initAdaward()
  .then(res=>{
    this.unitIdObj = JSON.parse(window.atob(res))
    console.log(this.unitIdObj);
    
  })
  .catch(err=>{
    console.log(err);
  })
}