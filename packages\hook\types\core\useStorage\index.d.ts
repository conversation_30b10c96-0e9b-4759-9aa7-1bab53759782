declare function customSave(key: string, value: any): void;
declare function customLoad<T = any | null>(key: string, defaultValue?: T): T;
declare function customRemove(key: string): void;
declare function save(key: string, value: any, exp?: number): void;
declare function load<T = any | null>(key: string, defaultValue?: T): T;
declare function remove(key: string): void;
export declare function useStorage(): {
    customSave: typeof customSave;
    customLoad: typeof customLoad;
    customRemove: typeof customRemove;
    save: typeof save;
    load: typeof load;
    remove: typeof remove;
};
export {};
