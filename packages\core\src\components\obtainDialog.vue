<template>
  <div class="box">
        <p class="tip" v-html="state.联动奖赏.未获得"></p>
        <div class="cardContainer">
            <div class="cardItem">
                <img class="card" :src="$imgs['task/obtainBg.png']" alt="">
                <div class="title">{{ type }}</div>
                <div class="subtitle">{{ state.task.使用後變成雙倍 }}</div>
                <img class="typeImg" :class="{'typeImgEn':lang=='en'}" :src="$imgs[`${aword == state.home.步数单日双倍卡 ? 'Double_step' :
                                                    aword == state.home.减碳值单日双倍卡 ? 'reduction_card' :
                                                    aword == 'energy20' ? '碳值20' :
                                                    aword == 'energy30' ? '碳值30' :
                                                    aword == 'energy10' ? '碳值10' : ''
                                                    }.png`]" alt="">
            </div>
        </div>
        <div class="toblind">
            <div @click.stop="toBlind" class="know">
                {{state.联动奖赏.去盲盒}}
            </div>
        </div>
        <div>
            <div @click.stop="toTask" class="know">
                {{state.联动奖赏.去做任務}}
            </div>
        </div>

        <p class="fanhui">{{state.dialog.点击屏幕返回}}</p>
  </div>
</template>

<script setup lang='ts'>
import { useTaskStore} from '@/store'
import { useLang, useEnvConfig,useRouter } from 'hook';
let emit = defineEmits(['close'])
import { inject, ref } from 'vue'
const { state,lang } = useLang()
const taskStore = useTaskStore()//api
const {router} = useRouter()
const props = defineProps({
  aword: {
      type: String,
      required: true
  },
  type:{
    type:String
  }
})

let index = ref(0)

let blindUrl = {
    uat: `openurl-modal://http://*************/activity/myCity/#/blindBox?lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true`,
    beta: `openurl-modal://https://mylink.komect.com/activity/myCity/#/blindBox?lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true&debug=true`,
    pat: `openurl-modal://https://mylink.komect.com/activity/myCity/#/blindBox?lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true`
}

// 路由跳转
const toRouter = (url:string, obj:object={}) => {
    router.push({
        path: url,
        query: {
            "hideNavigationBar": 'true',
            ...obj
        }
    })
}

const toBlind = () => {
    window.location.href = useEnvConfig().RUN_ENV == 'production' ? blindUrl.pat : useEnvConfig().RUN_ENV == 'beta' ? blindUrl.beta : blindUrl.uat
}
const toTask = () => {
    useTaskStore().openTask = true
    toRouter('/home',{"hideNavigationBar": 'true'})
    emit('close')
}
</script>

<style lang='less' scoped>
.box{
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: column;

    .tip{
        white-space: pre-line;
        text-align: center;
        font-size: 48px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        margin-bottom: 50px;
    }
    .cardContainer{
        display: flex;
        justify-content: center;
        width: 750px;

        .cardItem{
            width: 334px;
            height: 488px;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            .title{
                font-size: 32px;
                font-family: PingFang TC, PingFang TC;
                font-weight: 600;
                color: #4E5B7E;
                z-index: 2;
                padding: 0 36px;
                margin-top: 72px;
                text-align: center;
            }
            .subtitle{
                font-size: 24px;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                color: #4E5B7E;
                margin-top: 12px;
                padding: 0 36px;
                z-index: 2;
                text-align: center;
            }
            .card{
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
            .typeImg{
                position: absolute;
                height: 160px;
                margin-top: 260px;
            }
            .typeImgEn{
                margin-top: 320px !important;
            }

        }
    }

    .toblind{
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        margin-top: 98px;
        >.know{
            margin-bottom: 40px;
        }
        >.know:nth-child(1){
            background: linear-gradient(180deg, #56D3EB 0%, #0068E0 100%);
            box-shadow: 0px 8px 0px 2px #0D50AC, inset 0px 4px 0px 2px #9FF0FF;
            margin-bottom: 40px;
        }
    }
    .know{
        width: 430px;
        height: 84px;
        background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
        box-shadow: 0px 8px 0px 2px rgba(252,175,40,1), inset 0px 4px 0px 2px rgba(255,242,178,1);
        border-radius: 48px 12px 48px 12px;
        font-size: 36px;
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .fanhui{
        position: absolute;
        font-size: 28px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -160px;
        white-space: nowrap;
    }
}
</style>
