<template>
  <div class="box">
    <div class="contain">
      <p>{{state.dialog.快去种下一颗植物吧}}</p>
      <div class="treeIcon"></div>
    </div>
    <button class="btn" @click="goToPlant"> {{state.dialog.去种植}}</button>
  </div>
  
</template>

<script setup>
import { useDialog, useLang } from 'hook';
import { onMounted, inject, ref, watch, getCurrentInstance } from 'vue'
const uuid = inject('uuid')
const { state } = useLang() 
const goToPlant = () =>{
    useDialog().getInstance(uuid)?.emit('goToPlant')
} 

</script>

<style lang='less' scoped>
.box{
    display: flex;
    flex-direction: column;
    align-items: center;
    .contain{
      width: 570px;
      height: 330px;
      position: relative;
      p{
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        top: 15px;
        font-size: 32px;
        font-family: <PERSON><PERSON>ang SC-Bold, PingFang SC, Arial;
        font-weight: bold;
        color: #FFFFFF;
        white-space: nowrap;
      }
      .treeIcon{
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 40px;
        width: 176px;
        height: 176px;
        background: url('@assets/imgs/treeIcon.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    .btn{
      margin-top: 40px;
      width: 430px;
      height: 84px;
      background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
      border-radius: 48px 12px 48px 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36px;
      font-family: PingFang SC-Bold, PingFang SC;
      font-weight: bold;
      color: #FFFFFF;
      line-height: 32px;
      text-shadow: 0px 0px 12px #FBAC2E;
    }
}
</style>