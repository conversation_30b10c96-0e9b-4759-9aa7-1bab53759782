<template>
  <div class="box">
      <p class="tit">{{ state.friend.温馨提示 }}</p>
      <p class="tip">{{ state.friend.你目前並沒有種植任何植物請先於MyGarden種植後才可進入好友的花園查看或拾取對方的減碳值 }}</p>
      <div class="btn" @click="toPlant">{{ state.friend.前往種植 }}</div>
  </div>
</template>

<script setup>
import { useLang, useRouter } from 'hook'
const { router, currentRoute } = useRouter()
const { state } = useLang()
let emit = defineEmits(['close'])


function toPlant() {
    emit('close')
    router.push({
        path: '/plantSelection',
        query: {
            "hideNavigationBar": 'true'
        }
    })
}
</script>

<style lang='less' scoped>
.box{
    width: 544px;
    padding: 64px 40px;
    background: #FFFFFF;
    border-radius: 24px 24px 24px 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .tit{
        font-size: 32px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #4E5B7E;
        line-height: 44px;
        white-space: nowrap;
        text-align: center;
    }
    .tip{
        margin-top: 16px;
        font-size: 24px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #4E5B7E;
        line-height: 40px;
        text-align: center;
    }
    .btn{
        margin-top: 48px;
        background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
        border-radius: 48px 48px 48px 48px;
        padding: 10px 54px;
        display: flex;
        white-space: nowrap;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        line-height: 32px;
    }
}
</style>