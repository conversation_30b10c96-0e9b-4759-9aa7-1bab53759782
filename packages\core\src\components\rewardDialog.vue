<template>
  <div class="box">
        <p class="tip" v-if="extraInfo">{{ !isfinish ? state.task.額外任務已完成 :state.task.額外任務生效中 }}</p>
        <div class="cardContainer">
            <div class="cardItem" v-if="extraInfo">
                <img class="card awordCard" :src="$imgs[`task/${aword == 'use-ticket-equity'? 'extra1' :
                                                    aword == 'finish-all' ?'extra2':''}.png`]" alt="">
                <div class="cardtitle">{{ aword == 'use-ticket-equity'? state.task.跨境權益 :state.task.全勤任務 }}</div>
            </div>
            <div class="cardItem" v-else>
                <!-- 左右按钮 -->
                <img class="hhaa ll" @click.stop="iknew" v-if="step > 0" :src="$imgs['j1.png']" alt="">
                <img class="hhaa rr" @click.stop="iknow" v-if="step < stepNum-1" :src="$imgs['j1.png']" alt="">
                <!-- 中间卡片 -->
                <div class="preCard" v-if="list[step] == 'interal'">
                    <img class="card card2" :src="$imgs['task/obtainBg.png']" alt="">
                    <div class="title">
                        {{  aword + state.task.g減碳值 }}
                    </div>
                    <div class="subtitle">{{ state.task.上期任務獎勵 }}</div>
                    <img class="typeImg" :src="$imgs[`task/reward.png`]" alt="">
                </div>
                <div class="preCard" v-if="list[step] == 'card'">
                    <img class="card card2" :src="$imgs['task/obtainBg.png']" alt="">
                    <div class="title">
                        {{ state.task.步數減碳值單日雙倍卡 }}
                    </div>
                    <div class="subtitle">{{ state.task.上期任務獎勵 }}</div>
                    <img class="typeImg" :src="$imgs[`Double_step.png`]" alt="">
                </div>
            </div>
        </div>
        <div class="toblind">
            <div class="know"  v-if="extraInfo && isfinish" @click="toTask">
                {{state.task.去完成}}
            </div>
            <div class="know takeit" v-if="!extraInfo" @click="toGet">
                {{state.task.收下}}
            </div>
        </div>
        <p class="info" v-if="extraInfo">{{ allTask? state.task.完成後可獲額外20g減碳值 : state.task.完成所有限定任務可獲額外5g減碳值 }}</p>

        <p class="fanhui">{{state.dialog.点击屏幕返回}}</p>
  </div>
</template>

<script setup lang='ts'>
import { useTaskStore} from '@/store'
import { useLang, useEventBus } from 'hook';
import {inject,onBeforeMount,onBeforeUnmount,ref,watch} from 'vue'
const { state,lang } = useLang()
import { useDialog } from 'hook';
const uuid = inject('uuid')
let eventBus = useEventBus()

const props = defineProps({
  aword: {
      type: String,
      required: false
  },
  aword2: {
      type: String,
      required: false
  },
//   上期奖励或触发任务
  extraInfo:{
    type:Boolean
  },
//   去完成按钮显示
  isfinish:{
    type:Boolean,
    default:false
  },
//   自动打开任务栏
  openTask:{
    type:Boolean,
    default:false
  },
//   打开下一个弹窗
  nextTask:{
    type:Boolean,
    default:false
  },
//   全勤任务
  allTask:{
    type:Boolean,
    default:false,
    required: false
  }
})
const toTask = () => {
    useDialog().getInstance(uuid)?.emit('close')
}

let getOnece = ref(true)
// 点击收下
const toGet = async() => {
    if(getOnece.value){
        getOnece.value = false
        const result = await useTaskStore().getPreviousReward()
        eventBus.emit('upLevelFun',result)
        const mask = document.querySelector('.click-mask')
        mask?.removeEventListener('click',toGet,false)
    }
    useDialog().getInstance(uuid)?.emit('close')
}

//   打开下一个弹窗
let doTask = () => {
    eventBus.emit('nextTaskDia')
    const mask = document.querySelector('.click-mask')
    mask?.removeEventListener('click',doTask,false)
    return
}

//   自动打开任务栏
const openTask = () =>{
    useDialog().getInstance(uuid)?.emit('close')
    const mask = document.querySelector('.click-mask')
    mask?.removeEventListener('click',openTask,false)
    return
}
onBeforeMount(() => {
    const mask = document.querySelector('.click-mask')
    if (props.nextTask) {
        mask?.addEventListener('click',doTask,false)
    }else if(props.openTask){
        mask?.addEventListener('click',openTask,false)
    }else if(!props.extraInfo){
        mask?.addEventListener('click',toGet,false)
    }
    // 判断奖励是否有多个
    if(props.aword && props.aword2){
        stepNum.value = 2
    }
    props.aword ? list.value.push('interal') : ''
    props.aword2 ? list.value.push('card') : ''
})

let step = ref(0)
let stepNum = ref(0)
let list = ref([] as string[])
const iknew = () => {
    if(step.value == 0){
    }else{
        step.value --
    }
}
const iknow = () => {
    if(step.value == stepNum.value-1){
    }else{
        step.value ++
    }
}
watch(step, () => {
    if(step.value == stepNum.value-1){
        useDialog().getInstance(uuid).state.opts.maskClose = true
    }else{
        useDialog().getInstance(uuid).state.opts.maskClose = false
    }
})

</script>

<style lang='less' scoped>
.box{
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: column;

    .tip{
        white-space: pre-line;
        text-align: center;
        font-size: 48px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        margin-bottom: 142px;
    }
    .cardContainer{
        display: flex;
        justify-content: center;
        width: 750px;

        .cardItem{
            // width: 334px;
            height: 420px;
            position: relative;
            display: flex;
            justify-content: center;
            .preCard{
                display: flex;
                flex-direction: column;
                align-items: center;

            }
            .hhaa{
                width: 38px;
                height: 61px;
                position: absolute;
                top: 204px;
                z-index: 2;
                &.ll{
                    left: -100px;
                }
                &.rr{
                    right: -100px;
                    transform: rotate(180deg);
                }
                &.ll1{
                    left: 100px;
                    transform: rotate(180deg);
                }
                &.rr1{
                    right: 100px;
                }
            }
            .title{
                width: 334px;
                font-size: 32px;
                font-family: PingFang TC, PingFang TC;
                font-weight: 600;
                color: #4E5B7E;
                z-index: 2;
                padding: 0 36px;
                margin-top: 72px;
                text-align: center;
            }
            .subtitle{
                font-size: 24px;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                color: #4E5B7E;
                margin-top: 12px;
                padding: 0 36px;
                z-index: 2;
                text-align: center;
            }
            .typeImg{
                position: absolute;
                height: 160px;
                margin-top: 220px;
            }
            .typeImgEn{
                margin-top: 260px;
            }
            .card{
                position: absolute;
                top: 0;
                width: 330px;
                height: 254px;
            }
            .awordCard{
                // left: -80px;
            }
            .card2{
                width: 334px;
                height: 448px;
                margin-bottom: 72px;
            }
            .cardtitle{
                font-size: 36px;
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                color: #FFFFFF;
                display: block;
                z-index: 2;
                margin-top: 196px;
            }

        }
    }

    .toblind{
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

    }
    .know{
        width: 430px;
        height: 84px;
        background: linear-gradient(180deg, #50DD4C 0%, #3CBF48 100%);
        box-shadow: 0px 8 0px 2px #27A33F, inset 0px 4 0px 2px #DDFFD9;
        border-radius: 48px 12px 48px 12px;
        font-size: 36px;
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .takeit{
        background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
        box-shadow: 0px 8 0px 2px #FCAF28, inset 0px 4 0px 2px #FFF2B2;
        margin-top: 72px;
    }
    .info{
        font-size: 28px;
        font-family: PingFang SC, PingFang SC;
        // font-weight: bold;
        color: #FFFFFF;
        margin-top: 42px;
        padding: 0 20px;
        text-align: center;
    }
    .fanhui{
        position: absolute;
        font-size: 28px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -160px;
        white-space: nowrap;
    }
}
</style>
