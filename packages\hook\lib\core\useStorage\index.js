const suffix = ['via', 'system'];
function gen<PERSON>ey(key) {
    return [...suffix, key].join(':');
}
function customSave(key, value) {
    try {
        let saveValue;
        try {
            saveValue = JSON.stringify(value);
        }
        catch (e) {
            saveValue = value;
        }
        window.localStorage.setItem(key, saveValue);
    }
    catch (e) {
        console.log(e);
        console.log('存储数据失败', key, value);
    }
}
function customLoad(key, defaultValue) {
    let value = window.localStorage.getItem(key);
    if (value === null) {
        return defaultValue;
    }
    try {
        value = JSON.parse(value);
    }
    catch (e) {
        console.log(e);
        value = value || defaultValue;
    }
    return value;
}
function customRemove(key) {
    window.localStorage.removeItem(key);
}
function save(key, value, exp) {
    key = genKey(key);
    const data = {
        v: value,
        e: exp || -1,
        t: new Date().getTime() / 1000
    };
    localStorage.setItem(key, JSON.stringify(data));
}
function load(key, defaultValue) {
    key = genKey(key);
    const item = localStorage.getItem(key);
    if (item === null) {
        return defaultValue;
    }
    try {
        let data = JSON.parse(item || '{}');
        if (!data.e || !data.t) {
            localStorage.removeItem(key);
            return defaultValue;
        }
        if (data.e === -1) {
            return data.v;
        }
        //过期
        if (data.t + data.e <= new Date().getTime() / 1000) {
            localStorage.removeItem(key);
            return defaultValue;
        }
        return data.v;
    }
    catch (e) {
        localStorage.removeItem(key);
        return defaultValue;
    }
}
function remove(key) {
    key = genKey(key);
    localStorage.removeItem(key);
}
export function useStorage() {
    return {
        customSave,
        customLoad,
        customRemove,
        save,
        load,
        remove
    };
}
