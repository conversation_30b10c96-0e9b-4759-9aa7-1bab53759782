import fs from 'fs'
import { resolve } from 'path'
import type { ResolvedConfig, PluginOption } from 'vite'
import less from 'less'
import cleanCss from 'clean-css'
import { __injectStyle__ } from './style'
import { minify } from 'uglify-js'

const fileRegex = /\.(css|less)$/

const injectKey = `__INJECT__STYLE__KEY__`

let viteConfig: ResolvedConfig
const css: string[] = []

let renderSync = (code, option = {}) => {
  return (less.render(code, option) as any).then(
    (output) => {
      return output.css
    },
    (error) => {
      throw error
    }
  )
}

export default function libInjectCss(): PluginOption {
  return {
    name: 'lib-inject-css',
    apply: 'build',
    configResolved(resolvedConfig: ResolvedConfig) {
      viteConfig = resolvedConfig
    },
    async transform(code: string, id: string) {
      if (fileRegex.test(id)) {
        const cssCode = await renderSync(code)
        const miniCode = await cleanCss.process(cssCode.toString(), { to: null })
        css.push(miniCode.css)
        return { code: '' }
      }

      if (viteConfig.build.lib) {
        let entry = viteConfig.build.lib.entry
        entry = entry.replace(new RegExp('\\\\', 'g'), '/')
        if (id.includes(entry)) {
          return {
            code: `${code} ${injectKey}`
          }
        }
      }
      return null
    },
    async writeBundle(_: any, bundle: any) {
      const funcStr = minify(__injectStyle__.toString()).code
      const injectStr = `(${funcStr})("${css.join('')}")`
      for (const file of Object.entries(bundle)) {
        const { root } = viteConfig
        const outDir: string = viteConfig.build.outDir || 'dist'
        const fileName: string = file[0]
        const filePath: string = resolve(root, outDir, fileName)
        try {
          let data: string = fs.readFileSync(filePath, { encoding: 'utf8' })
          if (data.includes(injectKey)) {
            data = data.replace(injectKey, injectStr)
          }
          fs.writeFileSync(filePath, data)
        } catch (e) {
          console.error(e)
        }
      }
    }
  }
}
