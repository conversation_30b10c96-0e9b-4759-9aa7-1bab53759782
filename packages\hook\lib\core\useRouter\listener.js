import { useEventBus } from '../useEventBus';
const eventBus = useEventBus();
const key = 'ROUTE_CHANGE';
const instance = {
    isRegister: false
};
let latestRoute;
function emit(to) {
    eventBus.emitToInstance(key, instance, to);
    latestRoute = to;
}
function listen(handler, immediate = true) {
    if (!instance.isRegister) {
        instance.isRegister = true;
        eventBus.register(instance);
    }
    eventBus.on(key, handler, instance);
    if (immediate && latestRoute) {
        handler(latestRoute);
    }
}
function remove() {
    instance.isRegister = false;
    eventBus.unregister(instance);
}
export { emit, listen, remove };
