import { nftType } from './type'
import { useApi } from 'hook'
import { logEventStatistics } from '@via/mylink-sdk';
import { log } from 'console';
const { authRequest, authGet, authPost } = useApi()

// 确认铸造返回结果
async function nftFoundryClick(treeId: number | string) {
    return await authRequest('PATCH','/api/sdk/treetask/nft/build',{
        params: {
            tree_id: treeId
        }
    }).then((res)=>{
        console.log(res);

        return res;
    }).catch((err)=>{
        return err
    })
}

async function nftAchieveRedDot(treeId: number | string) {
    return await authRequest('PATCH','/api/sdk/treetask/nft/notice/mute',{
        params: {
            tree_id: treeId
        }
    }).then((res)=>{
        return res;
    }).catch((err)=>{
        return err
    })
}

async function userNftActivedNoticeMute(treeId: number | string) {
    return await authRequest('PATCH','/api/sdk/treetask/nft/actived/notice/mute',{
        params: {
            tree_id: treeId
        }
    }).then((res)=>{
        return res;
    }).catch((err)=>{
        return err
    })
}

export default { nftFoundryClick , nftAchieveRedDot , userNftActivedNoticeMute }
