import { createApp, getCurrentInstance } from 'vue';
import DialogApp from './DialogApp.vue';
import { state, rawState } from './state';
import registerList, { register } from './register';
import { Dialog } from './dialog';
const dialogApp = createApp(DialogApp);
dialogApp.mount('#app-dialog');
register(dialogApp);
/**
 * 注册弹窗，显示弹窗之前，先要有注册，否则会读取不到弹窗配置
 * @param dialog
 */
function registerDialog(dialog) {
    rawState.dialogMapper.set(dialog.dialogName || dialog.name, dialog);
}
registerList.forEach(registerDialog);
export function useDialog(localDialog) {
    // 先从当前组件获取，看是否有注册局部弹窗，没有再获取全局的
    const parent = getCurrentInstance();
    if (parent) {
        ;
        parent.localDialog = localDialog;
    }
    // console.log(parent)
    return {
        registerDialog,
        app: dialogApp,
        state,
        rawState,
        get: (name, opts) => new Dialog(false, parent, name, opts),
        getSingle: (name, opts) => {
            const singleDialog = rawState.singleDialogMap.get(name);
            return singleDialog || new Dialog(true, parent, name, opts);
        },
        getInstance: (uuid) => rawState.dialogMap.get(uuid),
        closeAll: () => {
            while (state.showList.length) {
                const item = state.showList.pop();
                rawState.dialogMap.get(item.uuid)?.close();
                rawState.singleDialogMap.get(item.name)?.close();
            }
        }
    };
}
