<template>
    <titleDecorator class="contain" @click="toRecord">
        <div>
            <div class="head-box">
                <img class="head" :src="headLogo" alt="" />
                <img class="headlogoFrame" :src="useUserStore().frameUrl" v-if="checkHeadFrame" alt="" />
            </div>
            <p class="text">
                <span>{{ state.home.积累减碳值 }}</span>
                {{ totalEnergy }}
            </p>
        </div>
    </titleDecorator>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useLoading, useLang, useRouter } from 'hook'
import { useUserStore, useFriendStore } from '@/store'
import titleDecorator from './titleDecorator.vue'
import { imgs } from "@/assets/imgs/index";

defineProps({
    /** 减碳的总值 */
    totalEnergy: {
        type: String,
        default: 0
    }
})
const { router } = useRouter()
const loading = useLoading()
const friendStore = useFriendStore()
const { state } = useLang()
/** 默认头像 */
const friendHeadLogo =
    'https://cdn.mylink.hk.chinamobile.com/via/resource/activity/2025-05-15/363c48415801449e8cb1fa0218f8aff0.png'
/** 配置用戶头像 */
const headLogo = computed(() => {
    return friendStore.isOwn ? useUserStore().headLogo : friendHeadLogo
})

/** 跳转到获取减碳值的历史页面 */
const toRecord = async () => {
    loading.loading('open')
    if (friendStore.isOwn) {
        if (useUserStore().inLogin) {
            router.push({
                path: '/record',
                query: {
                    hideNavigationBar: 'true'
                }
            })
        } else {
            await useUserStore().login()
            if (useUserStore().inLogin) {
                router.push({
                    path: '/record',
                    query: {
                        hideNavigationBar: 'true'
                    }
                })
            }
        }
    }
    loading.loading('close')
}

/** 检查标题装饰 */
const checkHeadFrame = computed(() => {
    return friendStore.isOwn && useUserStore().frameUrl
})
</script>

<style lang="less" scoped>
.contain {
    width: 450px;
    height: 124px;
    z-index: 2;
    .head-box{
        position: relative;
        width: 70px;
        height: 70px;
        top: 20px;
        left: 42px;
    }
    .head {
        display: block;
        width: 70px;
        height: 70px;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 50%;
    }
    .headlogoFrame {
        width: 100px;
        height: 100px;
        position: absolute;
        top: -14px;
        left: -14px;
    }
    .text {
        position: absolute;
        top: 52px;
        left: 125px;
        font-size: 32px;
        font-family: Arial-Bold Italic, Arial;
        font-weight: normal;
        font-style: italic;
        color: #ffffff;
        span {
            font-style: normal;
            font-size: 24px;
            font-family: Source Han Sans CN-Bold, Source Han Sans CN;
            font-weight: bold;
            color: #ffffff;
        }
    }
}
</style>
