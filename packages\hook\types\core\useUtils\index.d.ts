import QRCode from './qrcode';
import { md5 } from 'md5js';
declare function copyText(text: string): void;
declare function loadUrlQuery(url?: string): {
    query: Record<string, any>;
    hashQuery: Record<string, any>;
};
declare function isArray(value: any): value is any[];
/**
 * 深度合并对象
 * @param firstObj  被合并的对象
 * @param secondObj 合并进来的对象
 */
declare function deepObjectMerge(firstObj: any, secondObj: any): any;
declare function compareVersion(v1: string, v2: string): 0 | 1 | -1;
declare function isObject(obj: any): obj is object;
export declare function useUtils(): {
    copyText: typeof copyText;
    loadUrlQuery: typeof loadUrlQuery;
    isArray: typeof isArray;
    deepObjectMerge: typeof deepObjectMerge;
    compareVersion: typeof compareVersion;
    QRCode: typeof QRCode;
    md5: typeof md5;
    isObject: typeof isObject;
};
export {};
