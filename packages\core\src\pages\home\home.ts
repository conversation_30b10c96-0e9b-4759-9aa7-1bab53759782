import { useUserStore, useTaskStore } from '@/store'
import { ref } from 'vue'
import { useLang, useDialog, useEventBus } from 'hook'
const { state } = useLang()
export function useHomeConfig() {
    let showBearTalk = ref(false)
    let notified_first_logic1 = ref(true)
    let notified_first_logic4 = ref(true)
    const dialog = useDialog({})
    async function judgePopUpNotification(tree_energy, demand, next?) {
        let info = useUserStore().userInfo
        if (Object.keys(info).length == 0) {
            info = await useTaskStore().getInfo()
        }
        // 没有种树
        if (!info.tree) {
            return useEventBus().emit('ifLotArr')
        }
        if (info.webui_manage_values === null) {
            info.webui_manage_values = {}
        }
        notified_first_logic1.value = info.notified_first_logic1
        notified_first_logic4.value = info.notified_first_logic4
        showBearTalk.value = false
        let firstLoginDia = dialog.get('firstLoginDialog')
        firstLoginDia.beforeClose(() => {
            return new Promise((resolve) => {
                next()
                useEventBus().emit('ifLotArr')
                resolve(false)
            })
        })
        let popUpNotificationArr: Array<Number> = []
        let showRule: Record<string, Record<'show' | 'id', Boolean | number>> = {
            aigc:{
                show: !info.webui_manage_values.aigc,
                id: 34
            },
            temp_nft: {
                show: !info.webui_manage_values.temp_nft,
                id: 33
            },
            advertisement_two: {
                show: !info.webui_manage_values.advertisement_two,
                id: 32
            },
            shop_nft: {
                show: !info.webui_manage_values.shop_nft,
                id: 31
            },
            advertisement: {
                show: !info.webui_manage_values.advertisement,
                id: 30
            },
            new_first_tree: {
                show: !info.webui_manage_values.new_first_tree,
                id: 29
            },
            community_game: {
                show: !info.webui_manage_values.community_game,
                id: 28
            },
            five_fu: {
                show: !info.webui_manage_values.five_fu,
                id: 27
            },
            // up_nft: {
            //     show: !info.webui_manage_values.up_nft,
            //     id: 26
            // },
            yumu_mumian: {
                show: !info.webui_manage_values.yumu_mumian,
                id: 25
            },
            new_tree: {
                show: !info.webui_manage_values.new_tree,
                id: 24
            },
            task_replace: {
                show: !info.webui_manage_values.task_replace,
                id: 23
            },
            shopping_game: {
                show: !info.webui_manage_values.shopping_game,
                id: 22
            },
            first_nft_login_upgrade: {
                show: !info.webui_manage_values.first_nft_login_upgrade && useUserStore().isHK == 0,
                id: 21
            },
            first_task_two: {
                show: !info.webui_manage_values.first_task_two && useUserStore().isHK == 0,
                id: 20
            },
            first_nft_login_one: {
                show: !info.webui_manage_values.first_nft_login_one && useUserStore().isHK == 0,
                id: 19
            },
            first_updateaddress_login: { show: !info.webui_manage_values.first_updateaddress_login, id: 18 },
            first_delay_login: {
                show: !info.webui_manage_values.first_delay_login && useUserStore().isHK == 0,
                id: 17
            },
            first_cardupdate_login: { show: !info.webui_manage_values.first_cardupdate_login, id: 16 },
            first_task_login: { show: !info.webui_manage_values.first_task_login && useUserStore().isHK == 0, id: 15 },
            first_game_login: { show: !info.webui_manage_values.first_game_login, id: 14 },
            first_carbon_login: { show: !info.webui_manage_values.first_carbon_login, id: 13 },
            first_postpone_two_login: { show: !info.webui_manage_values.first_postpone_two_login, id: 12 },
            first_vip_login: { show: !info.webui_manage_values.first_vip_login, id: 10 },
            first_flower_login: {
                show: !info.webui_manage_values.first_flower_login && useUserStore().isHK == 0,
                id: 7
            },
            notified_first_logic4: { show: !notified_first_logic4.value, id: 6 },
            notified_first_logic1: { show: !notified_first_logic1.value && !useUserStore().isSlash, id: 4 }
        }
        for (const key in showRule) {
            if (showRule[key].show) {
                popUpNotificationArr.push(showRule[key].id as number)
            }
        }

        popUpNotificationArr = popUpNotificationArr.filter((value) => {
            let act = state.activityArr.find((v) => v.id === value)
            if (!act.onlineTime && !act.startTime) {
                return true
            } else if (!act.onlineTime && act.startTime) {
                return new Date().getTime() > new Date(act.startTime).getTime()
            } else if (act.onlineTime && !act.startTime) {
                return new Date().getTime() < new Date(act.onlineTime).getTime()
            } else if (act.onlineTime && act.startTime) {
                return (
                    new Date().getTime() < new Date(act.onlineTime).getTime() &&
                    new Date().getTime() > new Date(act.startTime).getTime()
                )
            }
        })

        if (popUpNotificationArr.length != 0) {
            notified_first_logic1.value = notified_first_logic4.value = true
            if (!useUserStore().isSlash) {
                useUserStore().updateFirstLogin1()
                useUserStore().updateFirstLogin2()
            }
            useUserStore().updateFirstLogin4()
            let CustomizeArr = [
                'aigc',
                'temp_nft',
                'advertisement_two',
                'shop_nft',
                'advertisement',
                'new_first_tree',
                'community_game',
                'five_fu',
                // 'up_nft',
                'yumu_mumian',
                'new_tree',
                'task_replace',
                'shopping_game',
                'first_nft_login_upgrade',
                'first_nft_login_one',
                'first_cardupdate_login',
                'first_updateaddress_login',
                'first_delay_login',
                'first_game_login',
                'first_delay_login',
                'first_task_login',
                'first_vip_login',
                'first_flower_login',
                'first_postpone_two_login',
                'first_carbon_login',
                'first_task_two'
            ]
            for (const item in info.webui_manage_values) {
                CustomizeArr = CustomizeArr.filter((element) => {
                    return item !== element
                })
            }
            CustomizeArr.forEach((item) => {
                if (showRule[item].show) {
                    useUserStore().setUpData(item, '1')
                }
            })
            firstLoginDia.show(
                { HpData: { n1: tree_energy, n2: demand }, list: popUpNotificationArr },
                { maskClose: false }
            )
        } else {
            next()
            useEventBus().emit('ifLotArr')
        }
    }

    return {
        judgePopUpNotification,
        showBearTalk
    }
}
