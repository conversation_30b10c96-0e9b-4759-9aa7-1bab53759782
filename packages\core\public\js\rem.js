function changefontsize() {

	var html = document.getElementsByTagName('html')[0];

	var screenWitdh = html.offsetWidth >= 375 ? 375 : html.offsetWidth;

	html.style.fontSize = screenWitdh * 75 / 750 + "px";
}
changefontsize();

window.onresize = changefontsize;
//因使用rem布局，需重置微信字体大小 
(function() {
	if (typeof window.WeixinJSBridge == "object" && typeof window.WeixinJSBridge.invoke == "function") {
		handleFontSize();
	} else {
		document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
	}
	function handleFontSize() {
		// 设置网页字体为默认大小
		window.WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize' : 0 });
		// 重写设置网页字体大小的事件
		window.WeixinJSBridge.on('menu:setfont', function() {
			window.WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize' : 0 });
		});
	}
})();