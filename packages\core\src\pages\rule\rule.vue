<template>
    <div class="home">
        <navComponent :returnType="2" :showTransmit="false" />
        <div class="home-bg"></div>
        <div class="container">
            <img class="rulett" :src="$imgs['rulett.png']" alt="" />
            <p class="ruletk" :class="lang == 'en' ? 'ruletk_en' : ''">{{ state.rule.条款及细则 }}</p>
            <div class="content">
                <img v-if="useUserStore().isHK == 1" :src="$imgs[state.rule.pic_nd]" alt="" />
                <img
                    v-else-if="router.currentRoute.value.query.type == 'integral' && useUserStore().isHK == 0"
                    :src="$imgs[state.rule.pic_int]"
                    alt=""
                />
                <img
                    :src="$imgs[state.rule.pic_task]"
                    v-else-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0"
                    alt=""
                />
                <img v-else :src="$imgs[state.rule.pic]" alt="" />
                <button
                    v-if="
                        router.currentRoute.value.query.type == 'integral' &&
                        useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')
                    "
                    class="integral-btn1"
                    @click="openUrl(1)"
                ></button>
                <button
                    v-if="
                        router.currentRoute.value.query.type == 'integral' &&
                        useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')
                    "
                    class="integral-btn2"
                    @click="openUrl(3)"
                ></button>
                <button
                    v-if="
                        router.currentRoute.value.query.type == 'integral' &&
                        useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')
                    "
                    class="integral-btn3"
                    @click="openUrl(4)"
                ></button>
                <button
                    v-if="
                        router.currentRoute.value.query.type == 'integral' &&
                        useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')
                    "
                    class="integral-btn4"
                    @click="openUrl(3)"
                ></button>
                <button
                    v-if="router.currentRoute.value.query.type == 'integral' && useUserStore().isHK == 0 && lang == 'en'"
                    class="integral-btn6"
                    @click="openUrl(1)"
                ></button>
                <button
                    v-if="router.currentRoute.value.query.type == 'integral' && useUserStore().isHK == 0 && lang == 'en'"
                    class="integral-btn7"
                    @click="openUrl(3)"
                ></button>
                <button
                    v-if="router.currentRoute.value.query.type == 'integral' && useUserStore().isHK == 0 && lang == 'en'"
                    class="integral-btn8"
                    @click="openUrl(4)"
                ></button>
                <button
                    v-if="router.currentRoute.value.query.type == 'integral' && useUserStore().isHK == 0 && lang == 'en'"
                    class="integral-btn9"
                    @click="openUrl(3)"
                ></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn1"
                @click="openUrl(5)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn2"
                @click="openUrl(6)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn3"
                @click="openUrl(7)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn4"
                @click="openUrl(9)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn5"
                @click="openUrl(10)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn6"
                @click="openUrl(11)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn7"
                @click="openUrl(12)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn8"
                @click="openUrl(13)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn9"
                @click="openUrl(14)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn10"
                @click="openUrl(15)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn11"
                @click="openUrl(16)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn12"
                @click="openUrl(17)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn13"
                @click="openUrl(18)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn14"
                @click="openUrl(19)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn15"
                @click="openUrl(3)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn16"
                @click="openUrl(4)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn17"
                @click="openUrl(3)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn18"
                @click="openUrl(19)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        (lang == 'tc' || lang == 'sc')" class="task-btn19"
                @click="openUrl(19)"></button>

                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn20"
                @click="openUrl(5)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn21"
                @click="openUrl(6)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn22"
                @click="openUrl(7)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn23"
                @click="openUrl(9)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn24"
                @click="openUrl(10)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn25"
                @click="openUrl(11)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn26"
                @click="openUrl(12)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn27"
                @click="openUrl(13)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn28"
                @click="openUrl(14)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn29"
                @click="openUrl(15)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn30"
                @click="openUrl(16)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn31"
                @click="openUrl(17)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn32"
                @click="openUrl(18)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn33"
                @click="openUrl(19)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn34"
                @click="openUrl(3)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn35"
                @click="openUrl(4)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn36"
                @click="openUrl(3)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn37"
                @click="openUrl(19)"></button>
                <button v-if="router.currentRoute.value.query.type == 'task' && useUserStore().isHK == 0 &&
                        lang == 'en'" class="task-btn38"
                @click="openUrl(19)"></button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { openApp, logEventStatistics } from '@via/mylink-sdk'
import { useLang, useRouter, useLoading } from 'hook'
import navComponent from '@/components/navComponent.vue'
import { useUserStore } from '@store'
const { currentRoute,router } = useRouter()
const { state, lang } = useLang()
const { loading } = useLoading()
//查看活動規則的人數
logEventStatistics('Mygarden_tnc_page')
loading('close')
onMounted(() => {
    let dialog = document.querySelector('#app-dialog')
    dialog.style.display = 'none'
})
const openUrl = (num) => {
    switch (num) {
        case 1:
            window.location.href = `https://mylink.komect.com/mylink/#/integral/rules?lang=${lang.value}`
            break
        case 2:
            window.location.href = 'https://www.hk.chinamobile.com/tc/mylinkpoints.html'
            break
        case 3:
            window.location.href = 'https://www.hk.chinamobile.com/tc/privacy.html'
            break
        case 4:
            window.location.href = 'https://www.hk.chinamobile.com/tc/pics.html'
            break
        case 5:
        //     window.location.href = `https://cdn.mylinkapp.hk/via/atv/save-energy-forest/index.html#/rule?lang=${lang.value}&hideNavigationBar=true`
                router.push({ path: '/rule', query: { hideNavigationBar: 'true' } })
            break
        case 6:
            window.location.href = 'https://mylink.oss-accelerate.aliyuncs.com/ico/homepage/penshefeihang/pdffile/3e796a4fba7c4023903269b093a3ea90.pdf'
            break
        case 7:
            window.location.href = 'https://mylink.oss-accelerate.aliyuncs.com/ico/homepage/penshefeihang/pdffile/a8b9b99508bb415a891a73d67153f1dd.pdf'
            break
        case 9:
            window.location.href = 'https://mylink.oss-accelerate.aliyuncs.com/ico/homepage/zhonglvbashi/pdffile/b7eb44c615bb43f1b780fc8cda492d57.pdf'
            break
        case 10:
            window.location.href = 'https://mylink.oss-accelerate.aliyuncs.com/ico/homepage/zhonglvbashi/pdffile/01e32427241f47e2be607547b7a26af3.pdf'
            break
        case 11:
            window.location.href = 'https://mylink.oss-accelerate.aliyuncs.com/ico/homepage/zhonglvbashi/pdffile/b372443cf6024fd489e019add4b0dd35.pdf'
            break
        case 12:
            window.location.href = 'openhkhshlogin://https://cdn.mylinkapp.hk/mygame/center/index.html?thirdPartyToken=<<cmcchkhsh_thirdparty_token_channel=bU9JU0HAZG>>&lang=<<cmcchkhsh_cmplang>>&jumpGame=31'
            break
        case 13:
            window.location.href = 'openhkhshlogin://https://cdn.mylinkapp.hk/mygame/center/index.html?thirdPartyToken=<<cmcchkhsh_thirdparty_token_channel=bU9JU0HAZG>>&lang=<<cmcchkhsh_cmplang>>&jumpGame=33'
            break
        case 14:
            window.location.href = 'https://mylink.oss-accelerate.aliyuncs.com/ico/homepage/penshefeihang/pdffile/3e796a4fba7c4023903269b093a3ea90.pdf'
            break
        case 15:
            window.location.href = 'https://mylink.oss-accelerate.aliyuncs.com/ico/homepage/penshefeihang/pdffile/a8b9b99508bb415a891a73d67153f1dd.pdf'
            break
        case 16:
            window.location.href = 'https://mylink.oss-accelerate.aliyuncs.com/ico/homepage/zhonglvbashi/pdffile/b7eb44c615bb43f1b780fc8cda492d57.pdf'
            break
        case 17:
            window.location.href = 'https://mylink.oss-accelerate.aliyuncs.com/ico/homepage/zhonglvbashi/pdffile/01e32427241f47e2be607547b7a26af3.pdf'
            break
        case 18:
            window.location.href = 'https://mylink.oss-accelerate.aliyuncs.com/ico/homepage/zhonglvbashi/pdffile/b372443cf6024fd489e019add4b0dd35.pdf'
            break
        case 19:
                router.push({ path: '/rule', query: { hideNavigationBar: 'true' } })
            break

    }
}
</script>

<style lang="less" scoped>
@import './rule.scss';
</style>
