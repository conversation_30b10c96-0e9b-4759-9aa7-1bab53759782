<template>
    <!-- 成就 -->
    <div
        :class="{
            'achieveBox3': type == 'mini',
            'achieveBox1': type == 'small' || type == 'mini',
            'achieveBox2': type == 'big',
            'big-size': size == 'big'
        }"
    >
        <div class="new" v-if="item && item.new">new</div>
        <div v-if="type == 'small' || type == 'mini'">
            <!-- 树成就状态数据提示 nft(红点) -->
            <!-- TODO NFT -->
            <span
                class="num"
                v-if="
                    (item.code != 'shuishirong' && item.code != 'huangzhongmu') &&
                    item.completed &&
                    item.nft_exists === false &&
                    item.nft_build_notice === true &&
                    useUserStore().isHK == 0
                "
            ></span>
            <img
                class="nft__sign"
                src="../assets/imgs/NFT.png"
                alt=""
                v-if="item.completed && item.nft_exists === true && useUserStore().isHK == 0 && size != 'big'"
            />

            <!--是否显示布置后可获得减碳值-->
            <div
                class="decorate-tips"
                v-if="
                    item.completed &&
                    item.nft_exists === true &&
                    useUserStore().isHK == 0 &&
                    !item.nft_actived &&
                    !item.nft_recycled_before_upgrade &&
                    !item.nft_recycled
                "
            >
                <div>
                    {{ state.nft.getCRAfterDecorate }}
                </div>
            </div>
            <div :class="{ light: true, unlight: !item.completed }" v-if="item.req_vip && size != 'big'">
                {{ state.home.後付限定 }}
            </div>
            
            <div v-if="(isFriend ? !item.completed : !item.owned)" class="lock"></div>
            <img class="achieve_bg" :class="(isFriend ? !item.completed : !item.owned) ? 'dark' : ''" :src="$imgs['achieve_bg.png']" alt="" />
            <div class="ach_tree" :class="(isFriend ? !item.completed : !item.owned) ? 'dark' : ''">
                <img :class="`${item.code}`" :src="$imgs[`achievement/${item.code}.png`]" alt="" />
            </div>
            
            <img
                class="achieve_title"
                :class="(isFriend ? !item.completed : !item.owned) ? 'dark' : ''"
                :src="$imgs['achieve_title2.png']"
                alt=""
            />
            <p :class="(isFriend ? !item.completed : !item.owned) ? 'darktext' : ''">{{ state.plant[item.code]?.plantName }}</p>
            <img :src="$imgs[`${item.completed ? 'plantedicon' : 'notplanted'}.png`]" class="planted-icon" v-if="item.owned" alt="">
        </div>
        <div v-if="type == 'big'">
            <img class="achieve_bg" :src="$imgs['achieve_bg.png']" alt="" />
            <div class="tree">
                <img :class="`${treeCode}`" :src="$imgs[`TT/state/${treeCode}/s9.png`]" alt="" />
            </div>
            <img class="title" :src="$imgs['achieve_title2.png']" alt="" />
            <p>{{ state.plant[treeCode].plantName }}</p>
        </div>
        <div class="tab-list" v-if="size == 'big'">
            <div :class="{ light: true, unlight: !item.completed }" v-if="item.req_vip">{{ state.home.後付限定 }}</div>
            <img
                class="nft__sign"
                src="../assets/imgs/NFT2.png"
                alt=""
                v-if="item.completed && item.nft_exists === true && useUserStore().isHK == 0  && item.nft_recycled == false"
            />
        </div>
        <!-- TODO NFT -->
        <div
            class="give"
            v-if="
                (type == 'small' || type == 'mini') &&
                !isFriend &&
                useUserStore().isHK == 0 &&
                typeof item.flower_count == 'number' &&
                (item.code !== 'huangzhongmu' && item.code !== 'shuishirong')
            "
            @click="giveClick(item.flower_count, item.completed)"
        >
            <img class="give-img" :src="$imgs[`give.png`]" alt="" />
            <span class="give-text">{{ state.flower.贈送 }}({{ item.flower_count }})</span>
        </div>
        <div class="give-diglog" v-if="giveDig && useUserStore().isHK == 0">
            <div class="give-box">
                <div class="give-title">
                    <span class="give-title-text">{{ state.flower.贈送提示 }}</span>
                </div>
                <div class="give-tips">
                    <span class="give-tips1">{{ state.flower.每種成一棵植物後 }}</span>
                    <br />
                    <span class="give-tips2">{{ state.flower.贈送成功後 }}</span>
                    <br />
                </div>
                <div class="give-btn" @click="giveDig = !giveDig">
                    <span class="give-text">{{ state.flower.知道了 }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useDialog, useLang, useRouter, useEventBus, useToast } from 'hook'
import { defineComponent, onMounted, inject, ref, watch, getCurrentInstance, computed } from 'vue'
import { logEventStatistics } from '@via/mylink-sdk'
import { useNFTStore, useUserStore } from '@/store'

const nftStore = useNFTStore()
const { router } = useRouter()
const { emit } = useEventBus()
const giveDig = ref(false)
const isFriend = ref(location.href.includes('friendId'))
const props = defineProps({
    item: {
        type: Object,
        required: false
    },
    type: {
        type: String,
        required: true
    },
    treeCode: {
        type: String,
        required: false,
        default: 'lanhuaying'
    },
    /** 尺寸 */
    size: {
        type: String,
        required: false
    }
})
const { state } = useLang()
// 赠送
const giveClick = (flower_count, lock,tree_id) => {
    console.log(props.item)
    logEventStatistics('garden_gift_send_click')
    if (lock && !flower_count) {
        return useToast().toast(state.flower.暫無可贈送的裝飾)
    }
    if (flower_count) {
        router.push({
            path: '/friendList',
            query: {
                hideNavigationBar: 'true',
                treeCode: props.item.code
            }
        })
    } else {
        giveDig.value = true
    }
}
</script>

<style lang="less" scoped>
.achieveBox3 {
    transform: scale(0.7);
    transform-origin: left center;
    margin-right: -30px;
}

.achieveBox1 {
    width: 164px;
    height: 176px;
    position: relative;
    .nft__sign {
        width: 84px;
        right: -25%;
        bottom: 0%;
    }
    .num {
        position: absolute;
        width: 16px;
        height: 16px;
        background: #fd4232;
        border-radius: 50%;
        right: 38px;
        top: 219px;
        z-index: 111;
    }
    .Rights {
        position: absolute;
        background: #8f8df7;
        color: #fff;
        padding: 8px 12px;
        font-size: 16px;
        border-radius: 10px;
        right: 0px;
        top: -20px;
        z-index: 111;
    }
    .Rights::after {
        content: ' ';
        display: block;
        position: absolute;
        z-index: 111;
        right: 4px;
        bottom: -7px;
        width: 0px;
        height: 0px;
        border-right: 10px solid transparent;
        border-left: 10px solid transparent;
        border-bottom: 15px solid #8f8df7;
        border-radius: 3px;
        transform: rotate(85deg);
    }
    .achieve_bg {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        top: 0;
        width: 148px;
    }
    .achieve_title {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        width: 164px;
        bottom: 15px;
    }
    .planted-icon{
        position: absolute;
        width: 64px;
        height: 64px;
        right: 32px;
        top: 190px;
    }
    .ach_tree {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 0px;
        .jieguojueming {
            width: 135px;
            transform: translate(-5px, 0);
        }
        .xiuqiuhua {
            width: 135px;
            transform: translate(2px, -10px);
        }
        .diaozhonghua {
            width: 135px;
            transform: translate(-5px, 0);
        }
        .yangzijing {
            width: 135px;
            transform: translate(-3px, 0);
        }
        .lanhuaying {
            width: 135px;
            transform: translate(-5px, 0);
        }
        .yumushu{
            width: 140px;
        }
        .mumian{
            width: 140px;
        }
        .shuishirong{
            width: 180px;
        }
        .huangzhongmu{
            width: 170px;
        }
    }
    .dark {
        filter: brightness(50%);
    }
    .darktext {
        filter: brightness(80%);
    }
    .lock {
        z-index: 2;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 49px;
        height: 52px;
        background: url('@assets/TT/Icon/lock.png') no-repeat;
        background-size: 100% auto;
    }
    .light {
        z-index: 2;
        position: absolute;
        left: 0;
        top: -20px;
        width: 84px;
        height: 32px;
        background: linear-gradient(180deg, #c18b6f 0%, #885a42 100%);
        border-radius: 16px 4px 16px 4px;
        // font-size: 22px;
        font-size: 18px;
        font-weight: 400;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .unlight {
        background: #5a3f2e;
    }
    p {
        z-index: 2;
        font-size: 24px;
        color: #ffffff;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 24px;
        white-space: nowrap;
        max-width: 120px;
        white-space: nowrap;
        overflow: hidden; //文本超出隐藏
        text-overflow: ellipsis; //文本超出省略号替代
    }
}

.give {
    position: absolute;
    bottom: -118px;
    width: 270px;
    height: 50px;
    border-radius: 26px 26px 26px 26px;
    border: 2px solid #22992c;
    display: flex;
    justify-content: center;
    align-items: center;
    span {
        font-size: 24px;
        font-family: PingFang TC-Regular, PingFang TC;
        font-weight: 400;
        color: #22992c;
    }
    img {
        width: 18px;
        height: 22px;
        margin-right: 6px;
    }
}
.give-gary {
    width: 270px;
    height: 50px;
    border-radius: 26px 26px 26px 26px;
    border: 2px solid #b8d5ba;
    span {
        color: #b8d5ba;
    }
    .give-img {
        opacity: 0.4;
    }
}
.give-diglog {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.14);
    z-index: 99;
    .give-box {
        width: 544px;
        background: #ffffff;
        border-radius: 24px 24px 24px 24px;
        margin: 594px auto 0;
        padding-bottom: 60px;
        .give-title {
            font-size: 32px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #4e5b7e;
            text-align: center;
            padding-top: 64px;
        }
        .give-tips {
            font-size: 24px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #4e5b7e;
            padding: 0 40px;
            margin-top: 16px;
            line-height: 40px;
        }
        .give-btn {
            width: 220px;
            height: 56px;
            background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
            border-radius: 48px 48px 48px 48px;
            margin: 48px auto 0;
            display: flex;
            justify-content: center;
            align-items: center;
            span {
                font-size: 28px;
                font-family: PingFang SC-Bold, PingFang SC;
                font-weight: bold;
                color: #ffffff;
                line-height: 32px;
                text-shadow: 0px 0px 12px #fbac2e;
            }
        }
    }
}
.achieveBox2 {
    width: 312px;
    height: 312px;
    position: relative;
    .achieve_bg {
        position: absolute;
        left: 50%;
        top: 0px;
        transform: translate(-50%, 0);
        width: 244px;
    }
    .tree {
        position: absolute;
        transform: translate(-50%, 0);
        left: 50%;
        bottom: 80px;
        .diaozhonghua {
            width: 230px;
            transform: translate(-8px, 0);
        }
        .xiuqiuhua {
            width: 210px;
            transform: translate(3px, 0);
        }
        .jieguojueming {
            width: 245px;
        }
        .yangzijing {
            width: 200px;
            transform: translate(-5px, 0);
        }
        .lanhuaying {
            width: 240px;
            transform: translate(-10px, 0);
        }
        .yumushu{
            width: 220px;
        }
        .mumian{
            width: 240px;
        }
        .shuishirong{
            width: 250px;
        }
        .huangzhongmu{
            width: 250px;
        }
    }
    .title {
        width: 312px;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 20px;
    }
    p {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 33px;
        font-size: 32px;
        font-family: PingFang TC-Medium, PingFang TC;
        font-weight: 500;
        color: #ffffff;
        // white-space: nowrap;
        width: 250px;
        height: 80px;
        // background: #FFFFFF;
        display: flex;
        text-align: center;
        align-items: center;
        justify-content: center;
        line-height: 30px;
    }
}

.big-size {
    width: 312px;
    height: 420px;
    background: #fff;
    border-radius: 16px 16px 16px 16px;
    .nft__sign {
        width: 84px;
        right: -15%;
    }
    .achieve_bg {
        width: 200px;
        height: 200px;
        top: 60px;
    }
    .ach_tree {
        bottom: 90px;
        .jieguojueming {
            width: 230px;
        }
        .xiuqiuhua {
            width: 230px;
        }
        .diaozhonghua {
            width: 230px;
        }
        .yangzijing {
            width: 230px;
        }
        .lanhuaying {
            width: 230px;
        }
        .mumian {
            width: 230px;
        }
        .yumushu {
            width: 230px;
        }
        .shuishirong{
            width: 250px;
            transform: translateY(-50px);
        }
        .huangzhongmu{
            width: 240px;
            transform: translateY(-50px);
        }
    }
    .achieve_title {
        bottom: 120px;
        width: 270px;
    }
    p {
        bottom: 145px;
    }
    .give {
        left: 50%;
        transform: translateX(-50%);
        bottom: 20px;
    }
    .tab-list {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: start;
        left: 10px;
        bottom: 80px;
        > * {
            margin-right: 10px;
        }

        .light {
            width: 104px;
            height: 32px;
            background: linear-gradient(90deg, #ffe1bf 0%, #dbaa69 100%);
            border-radius: 16px 4px 16px 4px;
            // font-size: 22px;
            font-size: 18px;
            font-weight: 400;
            color: #965f12;
            display: flex;
            align-items: center;
            justify-content: center;
            position: unset;
        }
    }

    .decorate-tips {
        widows: 100%;
        float: right;
        div {
            padding: 0px 10px;
            height: 28px;
            background: #fffdea;
            border-radius: 4px 16px 4px 16px;

            color: #ab7f3d;
            font-family: PingFang SC, PingFang SC;
            font-size: 20px;
            text-align: center;
        }
    }
}
.new{
    position: absolute;
    z-index: 2;
    width: 70px;
    height: 40px;
    background: linear-gradient( 180deg, #FF8888 0%, #E22121 100%);
    border-radius: 24px 24px 24px 4px;
    font-weight: 500;
    font-size: 22px;
    color: #FFFFFF;
    line-height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
