import { logEventStatistics } from '@via/mylink-sdk'
import { useEnvConfig } from 'hook'
export default function () {
    const seeNFT = (groupId) => {
        logEventStatistics('garden_success_check_nft')
        if (useEnvConfig().RUN_ENV == 'develop' || useEnvConfig().RUN_ENV == 'uat') {
            // location.href = "http://47.57.156.216/nftweb/#/third-party?origin=1&target=numberExchange&forceHideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>"
            location.href = `openurl-modal://https://47.57.156.216/nftweb/#/third-party?origin=4&target=nftDetail&groupId=${groupId}&forceHideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>`
        } else {
            location.href = `openurl-modal://https://cdn.mylink.hk.chinamobile.com/blockchain/link/nft/index.html#/third-party?origin=4&target=nftDetail&groupId=${groupId}&forceHideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>`
        }
    }

    return { seeNFT }
}
