.reductionRecord {
    max-height: 100vh;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;

    .top-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100vw;
        height: 370px;
        background: url('@imgs/reductionRecord/background.png') no-repeat;
        background-size: 100% auto;
    }

    .title {
        // position: absolute;
        width: 100vw;
        height: 86px;
        margin-top: 220px;
        display: flex;
        align-items: center;
        z-index: 100;

        .head {
            width: 86px;
            height: 86px;
            background: #ffffff;
            border-radius: 43px;
            box-shadow: 0px 6px 0px 2px rgba(0, 0, 0, 0.16);
            margin-left: 50px;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            .logo {
                width: 70px;
                height: 70px;
                display: block;
                border-radius: 50%;
            }
            .headlogoFrame{
                width: 82px;
                height: 82px;
                position: absolute;
                top: 1px;
                left: px;
            }
        }

        .total {
            z-index: 100;
            color: #4e5b7e;
            margin-left: 28px;
            margin-top: 16px;
            height: 86px;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .text {
                font-size: 28px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                line-height: 28px;
                z-index: 100;
            }

            .value {
                margin-top: 23px;
                height: 38px;
                font-size: 36px;
                font-style: italic;
                font-weight: bold;
                line-height: 0px;
                z-index: 100;
            }
        }
    }

    .content {
        margin-top: 30px;
        width: 100vw;
        // height: 140px;
        background: linear-gradient(180deg, #d9f4e7 0%, #ffffff 100%);
        box-shadow: inset 0px 6px 0px 2px rgba(255, 255, 255, 0.85);
        border-radius: 48px 48px 0px 0px;
        opacity: 0.98;
        z-index: 10;
        padding-top: 44px;
        padding-left: 50px;
        .tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 48px;
            .tab {
                font-size: 28px;
                color: #A7AEC3;
                margin-right: 56px;
                &.active {
                    color: #196F20;
                    font-weight: bold;
                    position: relative;
                    &::after {
                        content: '';
                        width: 44px;
                        height: 8px;
                        background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
                        border-radius: 6px 6px 6px 6px;
                        position: absolute;
                        bottom: -15px;
                        left: 0;
                        right: 0;
                        margin: auto;
                    }
                }
            }
        }

        .time {
            font-size: 28px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #4e5b7e;
            line-height: 28px;

            .row {
                .wh(20, 12);
                position: relative;
                left: 20px;
                bottom: 3px;
            }
        }

        .cumulative {
            margin-top: 11px;
            font-size: 24px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #a7aec3;
            line-height: 28px;
        }
    }

    .lists {
        // padding-bottom: 35px;
        width: 100vw;
        margin-top: 40px;
        overflow: scroll;
        max-height: calc(100vh - 570px);

        .list-item {
            display: flex;
            justify-content: space-between;
            padding: 24px 90px 24px 50px;

            .left {
                display: flex;
                align-items: flex-start;
                .icon-box{
                    position: relative;
                    .expired{
                        left: 4px;
                        top: 4px;
                        width: 64px;
                        height: 64px;
                        background: rgba(0,0,0,0.6);
                        position: absolute;
                        border-radius: 50%;
                        font-weight: 400;
                        font-size: 18px;
                        color: #FFFFFF;
                        line-height: 28px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        span{
                            transform: scale(0.7) rotate(15deg);
                            white-space: nowrap;
                        }
                    }
                }
                .icon {
                    display: block;
                    width: 72px;
                    height: 72px;
                    margin-right: 20px;
                }

                .mid {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;

                    .item-title {
                        font-size: 28px;
                        font-family: PingFang SC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #4e5b7e;
                        margin-bottom: 12px;
                        line-height: 40px;
                    }

                    .item-time {
                        font-size: 24px;
                        font-family: PingFang SC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #a7aec3;
                        line-height: 28px;
                    }
                    .be-about-to,
                    .expired_at{
                        font-family: PingFang SC, PingFang SC;
                        font-weight: 400;
                        font-size: 24px;
                        color: #4E5B7E;
                        line-height: 28px;
                    }
                    .be-about-to{
                        color: #F25B5B;
                    }
                }
            }
            .item-value {
                font-size: 28px;
                font-family: PingFang SC-Semibold, PingFang SC;
                font-weight: 600;
                color: #4e5b7e;
                line-height: 28px;
                display: flex;
                align-items: center;
            }
            .card-right{
                display: flex;
                flex-direction: column;
                align-items: center;
                .see-detail{
                    margin-top: 78px;
                    width: 168px;
                    height: 58px;
                    background: linear-gradient( 359deg, #FDC23E 0%, #FDED9E 100%);
                    border-radius: 30px;
                    font-weight: 500;
                    font-size: 24px;
                    color: #6A520F;
                    display: flex;                    
                    justify-content: center;
                    align-items: center;
                }
                .see-detail-expired{
                    border: 2px solid #6A520F;
                    background: #fff;
                }
            }

            

            &.obb {
                background: #f7f7f7;
            }
        }
    }

    .empty {
        margin-top: 60px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .empty-img {
            width: 178px;
            height: 144px;
        }

        .empty-text {
            width: 308px;
            font-size: 28px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #4e5b7e;
            line-height: 44px;
            text-align: center;
            margin-top: 60px;
            white-space: pre-line;
        }

        .btn {
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 200px;
            height: 52px;
            background: linear-gradient(360deg, #3cbf48 0%, #3dd13f 100%);
            box-shadow: 0px 4px 0px 2px #38b23c, inset 0px 2px 0px 2px #92e898;
            border-radius: 18px 18px 18px 18px;
            font-size: 24px;
            font-family: PingFang TC-Medium, PingFang TC;
            font-weight: 500;
            color: #ffffff;
            margin-top: 58px;
            text-align: center;
            padding: 0 10px;
            box-sizing: border-box;
        }
    }

    .picker {
        background-color: #ffffff;
        border-radius: 48px 48px 0px 0px;

        :deep(.van-picker-column__item) {
            font-size: 32px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #4e5b7e;
            line-height: 28px;
        }

        :deep(.van-picker__frame) {
            border-top: 1px solid #9ea5ba;
            border-bottom: 1px solid #9ea5ba;
            margin-left: 45px;
            margin-right: 45px;
        }

        .btn-group {
            margin-top: 56px;
            margin-left: 90px;
            margin-right: 90px;
            padding-bottom: 116px;
            display: flex;
            justify-content: space-between;

            .cancel {
                width: 260px;
                height: 56px;
                background: #ffffff;
                border-radius: 48px 12px 48px 12px;
                border: 2px solid #9ea5ba;
                font-size: 28px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #a7aec3;
                line-height: 28px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .confirm {
                width: 260px;
                height: 56px;
                background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
                box-shadow: inset 0px 4px 0px 2px #fff2b2;
                border-radius: 48px 12px 48px 12px;
                font-size: 28px;
                font-family: PingFang SC-Bold, PingFang SC;
                font-weight: bold;
                color: #ffffff;
                line-height: 32px;
                text-shadow: 0px 0px 12px #fbac2e;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }

    .picker-top {
        width: 95%;//优化 750
        height: 140px;
        background: linear-gradient(180deg, #d9f4e7 0%, #ffffff 100%);
        box-shadow: inset 0px 6px 0px 2px rgba(255, 255, 255, 0.85);
        border-radius: 48px 48px 0px 0px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 146px;
        padding-right: 144px;
        margin: 0 auto;//剧中
        .picker-top-text {
            font-size: 32px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #4e5b7e;
            line-height: 28px;

            &.picker-top-select {
                color: #22992c;
            }

            &.underline {
                text-decoration: underline;
            }
        }
    }

    .picker-tip {
        text-align: center;
        font-size: 24px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #a7aec3;
        line-height: 28px;
        position: relative;
        bottom: 13px;
    }
}