<template>
    <div class="friend">
        <!-- Tab标题  start -->
        <div class="mgTitle">
            <div :class="`kk ${friendTabIndex == 1 ? 'active' : ''}`">
                <p>{{ state.friend.最新动态 }}</p>
            </div>
        </div>
        <div class="containerTop"></div>
        <!-- Tab标题   end -->
        <div class="container" v-if="virtualBub">
            <!-- Tab:最新动态  start -->
            <template v-if="friendTabIndex == 1">
                <!-- 有两个状态，如果用户无种植，并且这两天还没有数据，就显示空状态
                        用户有无种植：{}
                        这两天有无数据：{}
                    -->
                <template v-for="(record, key) in friendStore.activityList" :key="key">
                    <template v-if="true">
                        <div class="time">{{ record.day }}</div>
                        <div class="eneryitem">
                            <div :class="`item provide ${depTabIndex[key] == 0 ? 'provide_active' : ''
                                }`" @click="changeTab2(0, key)">
                                <img :src="$imgs['getOrange.png']" alt="" v-show="depTabIndex[key] == 0" />
                                <span class="eneryitem_name">
                                    {{ state.friend.共被收取 }} {{ record.other_energy_value }}g{{ state.friend.被朋友 }}
                                </span>
                            </div>
                            <div :class="`item get ${depTabIndex[key] == 1 ? 'get_active' : ''
                                }`" @click="changeTab2(1, key)">
                                <img :src="$imgs['getGreen.png']" v-show="depTabIndex[key] == 1" />
                                <span>{{ state.friend.收取 }} {{ record.self_energy_value }}g</span>
                            </div>
                        </div>
                        <template v-if="true">
                            <div class="enerylist" v-show="list_cpu(record, depTabIndex[key]).length !== 0">
                                <ul>
                                    <li v-for="(item, k, i) in list_cpu(record, depTabIndex[key])" :key="k">
                                        <eneryItem v-if="k < 3 || showList[key]" :item="item" :type="depTabIndex[key]"
                                            :third_id="item.third_id ? item.third_id.replace(item.create_time.replace(' ', ''), '') : item.third_id"
                                            :day="record.day"></eneryItem>
                                    </li>

                                    <div @click="changeshow(key)" class="listshow"
                                        v-if="list_cpu(record, depTabIndex[key]).length > 3">
                                        <div class="title" v-if="!showList[key]">{{ state.friend.展開 }}</div>
                                        <div class="title" v-else>{{ state.friend.收起 }}</div>
                                        <div class="down" :class="{ up: showList[key] }"></div>
                                    </div>
                                </ul>
                                <!-- <span v-if="Object.keys(list_cpu(record, depTabIndex[key])).length > 3" @click="router.push('/trends?hideNavigationBar=true')">查看更多</span> -->
                            </div>
                        </template>

                        <div class="energy_empty" v-show="list_cpu(record, depTabIndex[key]).length == 0">
                            <img class="empty-img" :src="$imgs['record-none.png']" alt="" />
                            <div class="empty-text">{{ state.friend.无动态 }}</div>
                        </div>
                    </template>
                </template>
            </template>
            <!-- Tab：最新动态  end -->
            <!-- Tab：空状态  start -->
            <!-- 最近两天都没有数据或者时候用户未参与种植时的空状态 -->
            <div class="energy_empty" v-show="friendTabIndex == 1 && friendStore.activityList.length == 0">
                <img class="empty-img" :src="$imgs['record-none.png']" alt="" />
                <div class="empty-text">{{ state.friend.无动态 }}</div>
                <div class="btn" @click="nextFriend">{{ state.friend.去找减碳值 }}</div>
            </div>
            <!-- Tab：空状态  end -->
        </div>
        <div class="container" v-else>
            <template v-for="(item) in 1">
                <template v-if="true">
                    <div class="time">{{ lang == 'en' ? 'today' :'今天' }}</div>
                    <div class="eneryitem">
                        <div :class="['item', 'provide', 'provide_active']">
                            <img :src="$imgs['getOrange.png']" alt="" />
                            <span class="eneryitem_name">
                                {{ state.friend.共被收取 }} {{ 12 }}g{{ state.friend.被朋友 }}
                            </span>
                        </div>
                        <div :class="`item get`">
                            <span>{{ state.friend.收取 }} {{ 12 }}g</span>
                        </div>
                    </div>
                    <div class="list" v-for="(i,index) in friendName">
                        <div>
                            <div class="left">
                                <img :src="$imgs['head.png']" alt="">
                                <p>[{{ i }}]{{ state.friend.共收取你 }}<span style="color:#EF8A28;">{{2*(index+1)}}g</span></p>
                            </div>
                            <div class="right">
                                <p>{{ state.friend.展開 }}</p>
                                <span></span></div>
                        </div>
                    </div>
                </template>
            </template>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive, onMounted, onBeforeMount, onBeforeUnmount, getCurrentInstance, computed } from 'vue'
import { useRouter, useLang, useLoading, useDayjs, useStorage, useEnvConfig, useDialog, useToast, useEventBus } from 'hook'
import { useFriendStore, useTreeStore, useUserStore, useTaskStore } from '@/store'
import eneryItem from '@/components/eneryItem.vue'
import imgavatar from '@/components/imgavatar.vue'
import { inApp, logEventStatistics } from '@via/mylink-sdk'
const envConfig = useEnvConfig()
const storage = useStorage()
let eventBus = useEventBus()
const { router, currentRoute } = useRouter()
const { state, lang } = useLang()
const friendStore = useFriendStore()
const friendTabIndex = ref(0)
const today = ref()
const yesterday = ref()
const showList = ref([false, false]) //false是收起的状态
const myname1 = ref('你')
const myname2 = ref('我')
let own = ref<any>({})
let myRandIndex = ref(-1)
let rankList = ref<any[]>([])
let notSign = ref<any>([])
let treeStore = useTreeStore()
const friendName = ref(['霸氣白桃烏龍茶','好事花生888','豬豬俠007'])
let props = defineProps({
    friendSheetTab: {
        type: Number
    },
    virtualBub: {
        type: Boolean,
        default:false
    }
})

const goSeeMore = () => {
    if (friendTabIndex.value === 0) {
        router.push('/friend?hideNavigationBar=true')
    } else {
        router.push('/trends?hideNavigationBar=true')
    }
}

const addFriend = (status) => {
    if (status === 'see') {
        logEventStatistics('garden_friends_invite_view_click')
        location.href = useEnvConfig().ADD_URL
    } else if (status === 'add') {
        logEventStatistics('garden_friends_invite_click')
        location.href = useEnvConfig().FRIENDS_URL
    }
}

let depTabIndex = ref([0, 0])
function list_cpu(item, index) {
    let result: any = []
    switch (index) {
        case 0:
            result = item.os
            break
        case 1:
            result = item.as
            break
    }

    result = Object.values(result).sort((a, b) => {
        return new Date(b.create_time).getTime() - new Date(a.create_time).getTime()
    })
    return Object.values(result)
}
function nextFriend() {
    eventBus.emit('closeFriendSheet')
    eventBus.emit('nextFriend')
}


function changeTab(index) {
    if (index == 0) {
        logEventStatistics('garden_new_activity_click')
    } else if (index == 1) {
        logEventStatistics('garden_myfriends_button_click')
    }
    friendTabIndex.value = index
}
//最新动态里面的tab
function changeTab2(num, key) {
    if (num == 0) {
        logEventStatistics('garden_total_collect_byfrd_click')
    } else if (num == 1) {
        logEventStatistics('garden_total_collect_click')
    }
    if (depTabIndex.value[key] == num)
        return
    depTabIndex.value[key] = num
}
//生成今天和昨天的函数
function generateDates() {
    var today = new Date()
    var yesterday = new Date(today)
    yesterday.setDate(today.getDate() - 1)
    var todayStr = ''
    var yesterdayStr = ''

    if (today.getFullYear() !== yesterday.getFullYear()) {
        todayStr = today.getFullYear() + '-01-01'
    } else {
        todayStr = '今天'
    }
    if (yesterday.getMonth() + 1 == 1 && yesterday.getDate() == 1) {
        yesterdayStr = today.getFullYear() + '-01-01'
    } else {
        const options = { month: '2-digit', day: '2-digit' }
        yesterdayStr = yesterday.toLocaleDateString('en-US', options).replace(/\//g, '-')
    }
    return { today: todayStr, yesterday: yesterdayStr }
}
// 控制【展开更多/收起】的方法
function changeshow(key) {
    showList.value[key] = !showList.value[key]
}
// 点击初始化动态界面
async function init() {
    if (!useUserStore().inLogin) {
        useUserStore().login()
    }
    rankList.value = friendStore.friendList.filter((item) => item.energy_total != 0)
    notSign.value = friendStore.friendList.filter((item) => item.energy_total == 0)
    //把去邀请放到最后
    notSign.value.sort((a, b) => {
        if (b.current_tree) {
            return 1
        }
        return -1
    })
    let info = await useTaskStore().getInfo()
    if (info) {
        own.value = {
            isOwn: true,
            head_logo: useUserStore().headLogo,
            name: info.user.name,
            energy_total: useTreeStore().energyTotal,
            current_tree: {
                code: useTreeStore().treeCode,
                level: useTreeStore().lv
            },
            third_id: info.user.third_id
        }
    }
    if (own.value.energy_total > 0) {
        rankList.value.push(own.value)
    } else {
        notSign.value.unshift(own.value)
    }
    rankList.value.sort((a, b) => b.energy_total - a.energy_total)
    if (own.value.energy_total > 0) {
        myRandIndex.value = rankList.value.findIndex(item => item.isOwn)
    }
    //在主页筛选成五或六人
    // if (router.currentRoute.value.name === 'home') {
    //     rankList.value = rankList.value.slice(0,6)
    //     if (!rankList.value.filter((item)=>item.isOwn)) {
    //         rankList.value = rankList.value.slice(0,5)
    //     }
    //     notSign.value = []
    // }
}
onMounted(() => {
    if (props.friendSheetTab) {
        friendTabIndex.value = props.friendSheetTab
    }
    const time = generateDates()
    today.value = time.today
    yesterday.value = time.yesterday
    init()
})

onBeforeMount(() => {
    eventBus.on('seeMore', goSeeMore)
    eventBus.on('clickson', () => eventBus.emit('clickgrandfather'))
})

watch(() => treeStore.energyTotal, async () => {
    await init()
})

onBeforeUnmount(() => {
    eventBus.off('seeMore', goSeeMore)
})
</script>
<style lang="less" scoped>
.friend {
    width: 724px;
    position: relative;
    background-color: #fff;
    border-radius: 48px 48px 48px 48px;
    padding-top: 50px;
}

.mgTitle {
    width: 392px;
    height: 84px;
    background: url('@assets/imgs/friendTitle2.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    line-height: 28px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    z-index: 2;

    span {
        font-size: 36px;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FFFFFF;
        padding-top: 16px;
    }

    .kk {
        flex: 1;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: #22992c;
    }

    .k1 {
        // border-right: 1px solid #ffffff;
    }

    .active {
        font-size: 36px;
        color: #ffffff;
    }
}

.containerTop {
    width: 100%;
    height: 160px;
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(180deg, #D9F4E7 0%, #FFFFFF 100%);
    border-radius: 48px 48px 0px 0px;
    opacity: 0.85;
}

.container {
    padding: 22px;
    width: 100%;
    height: 100%;
    position: relative;

    /* top:76px; */
    overflow-y: scroll;

    // overflow: hidden;
    .addfriend {
        // width: 706px;
        height: 128px;
        background: url('@imgs/intFrdCard.png') no-repeat;
        background-size: 100% 100%;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;

        img {
            width: 58px;
            height: 58px;
            margin: 0 24px 0 32px;
        }

        .add_title {
            font-size: 28px;
            color: #196f20;
            flex: 1;
        }

        .add_button {
            width: 158px;
            height: 52px;
            line-height: 52px;
            background-color: #ffffff;
            font-size: 24px;
            text-align: center;
            border-radius: 18px;
            box-shadow: inset 0px 2px 0px 2px #ebffed;
            color: #22992c;
            margin-right: 24px;
            position: relative;

            .num {
                display: block;
                position: absolute;
                right: -3px;
                top: -5px;
                width: 20px;
                height: 20px;
                background: #F05548;
                border-radius: 50%;
            }
        }
    }

    .friendlist {
        font-size: 28px;
        margin-top: 32px;

        li {
            display: flex;
            height: 128px;
            // justify-content: space-around;
            align-items: center;
            padding: 28px 42px 26px 24px;
            box-sizing: border-box;
            margin-bottom: 16px;
            transition: height 1s ease;

            &.firstLi {
                background-color: #f7f7f7;
                border-radius: 32px 8px 32px 8px;
            }

            .imglevel {
                width: 22px;
                height: 44px;
                margin-right: 40px;
            }

            .imglevel2 {
                width: 22px;
                height: 44px;
                font-size: 40px;
                font-family: Arial-Bold Italic, Arial;
                font-weight: bold;
                color: #4e5b7e;
                line-height: 28px;
                font-style: italic;
            }

            .imgavatar {
                width: 66px;
                height: 66px;
                margin-right: 24px;
            }

            .imgavatar1 {
                margin-left: 62px;
            }

            .friendinfo {
                flex: 1;

                .name {
                    color: #4e5b7e;
                    display: block;
                    width: 340px;
                    white-space: nowrap;
                    /* 防止文本换行 */
                    overflow: hidden;
                    /* 超出容器部分隐藏 */
                    text-overflow: ellipsis;
                    /* 超出部分以省略号表示 */
                }

                .level {
                    color: #a7aec3;
                    font-size: 24px;
                }
            }

            .tanValue {
                display: flex;
                align-items: center;

                img {
                    width: 20px;
                    height: 20px;
                }

                .toInvite {
                    font-size: 20px;
                    color: #FFFFFF;
                    white-space: nowrap;
                    align-items: center;
                    padding: 4px 24px;
                    background: #22992C;
                    border-radius: 22px 22px 22px 22px;
                }

                .value {
                    margin-left: 8px;
                    font-size: 32px;
                    color: #22992c;
                    font-weight: bold;
                }
            }
        }
    }

    .time {
        font-size: 32px;
        color: #4e5b7e;
        font-weight: 600;
    }

    .eneryitem {
        display: flex;
        font-size: 28px;
        margin-top: 24px;
        position: relative;
        justify-content: center;

        .item {
            width: 324px;
            height: 58px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            padding-left: 78px;
            padding-right: 10px;
            position: relative;
            font-size: 28px;

            img {
                width: 32px;
                height: 32px;
                // position: absolute;
                // top: 50%;
                // left: 16px;
                // transform: translateY(-50%);
            }
        }

        .provide {
            background-color: #fffaed;
            color: #fde9c9;
            margin-right: 22px;
        }

        .get {
            background-color: #e4f7e5;
            color: #c4e7c7;
        }

        .provide_active {
            color: #ef8a00;
            background-image: linear-gradient(to right, #ffebc4 100%, #ffe2a2 100%);
        }

        .get_active {
            color: #22992c;
            background-image: linear-gradient(to right, #bef8c0 100%, #94eb98 100%);
        }
    }

    .enerylist {
        font-size: 24px;
        margin-top: 32px;
        padding: 0 0 28px 0px;
        width: 100%;
        // height: 450px;
        box-sizing: border-box;
        overflow-y: scroll;
        overflow-x: hidden;
        pointer-events: none;
        top: 60px;
    }

    .energy_empty {
        margin-top: 60px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .empty-img {
            width: 178px;
            height: 144px;
        }

        .empty-text {
            width: 308px;
            font-size: 28px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #4e5b7e;
            line-height: 44px;
            text-align: center;
            margin-top: 60px;
            white-space: pre-line;
        }

        .btn {
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 200px;
            height: 52px;
            background: linear-gradient(360deg, #3cbf48 0%, #3dd13f 100%);
            box-shadow: 0px 4px 0px 2px #38b23c, inset 0px 2px 0px 2px #92e898;
            border-radius: 18px 18px 18px 18px;
            font-size: 24px;
            font-family: PingFang TC-Medium, PingFang TC;
            font-weight: 500;
            color: #ffffff;
            margin-top: 58px;
            text-align: center;
            padding: 0 10px;
            box-sizing: border-box;
        }
    }
}

.listshow {
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: all;

    .title {
        font-size: 24px;
        color: #a7aec3;
        margin-right: 8px;
    }

    .down {
        width: 16px;
        height: 16px;
        border-right: 2px solid #a7aec3;
        border-bottom: 2px solid #a7aec3;
        /* 先旋转到-135deg的位置 */
        transform: rotate(45deg);
    }

    .up {
        transform: rotate(-135deg);
    }
}

.provide {
    width: 324px;
    background: linear-gradient(152deg, #FFEBC4 0%, #FFE2A2 100%, 0.15);
    box-shadow: 0px 2px 0px 2px rgba(242, 192, 99, 0.15);
    border-radius: 12px 12px 12px 12px;
    text-align: center;
    color: rgba(239, 138, 0, 0.15);

    img {}
}

.container .eneryitem .provide_active {
    width: 324px;
    color: #EF8A00;
    background: linear-gradient(152deg, #FFEBC4 0%, #FFE2A2 100%);
    box-shadow: 0px 2px 0px 2px rgba(242, 192, 99);
}

.container .eneryitem .item {
    width: 324px;
    padding-left: 0;
    justify-content: center;

    img {
        margin-right: 30px;
    }
}

.get {
    width: 324px;
    background: rgba(78, 202, 84, 0.15);
    border-radius: 12px 12px 12px 12px;
}

.container .eneryitem .get_active {
    width: 324px;
    background: linear-gradient(153deg, #BEF8C0 0%, #94EB98 100%);
    box-shadow: 0px 2px 0px 2px rgba(68, 201, 80, 1);
    border-radius: 12px 12px 12px 12px;
    opacity: 1;
}

// .container .eneryitem::before{
//     clear: both;
//     display: block;
//     content:'';
//     background: url('@assets/imgs/trends_blue.png') no-repeat;
//     background-size: cover;
//     margin-right: 20px;
//     width: 60px;
//     height: 60px;
// }
.more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 48px;

    span {
        font-size: 24px;
        font-family: PingFang TC-Regular, PingFang TC;
        font-weight: 400;
        color: #A7AEC3;
        padding-right: 8px;
    }

    img {
        width: 12px;
        height: 20px;
    }
}
.list{
    margin-top: 20px;
    &>div{
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .left{
        display: flex;
        align-items: center;
        img{
            width: 66px;
            height: 66px;
            margin-right: 10px;
        }
        p{
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 22px;
            color: #4E5B7E;
        }
    }
    .right{
        width: 100px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 22px;
        border: 1px solid #A7AEC3;
        display: flex;
        justify-content: center;
        p{
            font-family: PingFang TC, PingFang TC;
            font-weight: 400;
            font-size: 22px;
            color: #A7AEC3;
        }
        span{
            width: 0;
            height: 0;
            margin-left: 8px;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-bottom: 8px solid #A7AEC3;
            margin-top: 13px;
        }
    }
}
</style>
