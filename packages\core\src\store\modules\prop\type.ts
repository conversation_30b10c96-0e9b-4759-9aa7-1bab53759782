import { usePropStore } from '.'
export interface propType {
    propsConfig: {
        挂饰: Array<any>, 
        铲子: null | string , 
        围栏: null | string,
        灯柱: null | string,
        稻草人: null | string,
        木头车: null | string,
        喷水池: null | string,
        桌子: null | string,
        椅子: null | string,
        GIF: null | string
    },
    toCard1:<PERSON><PERSON><PERSON>,
    toCard2:<PERSON><PERSON>an,
    toCard:<PERSON>olean,
}
export type PropStoreType = ReturnType<typeof usePropStore>
