<template>
    <div>
        <!-- 浇水过程不让操作 -->
        <div class="no-operate" v-if="waterShow"></div>
        <img class="water" :src="$imgs[waterImg]" alt="" v-show="waterShow">
        <div class="navv grow" @click="openWater" v-if="friendStore.isOwn && useTreeStore().hasPlanted && isShowOpenWater">
            <!-- <p>
                {{ state.water.淋花 }}
            </p> -->
            <span class="new" v-if="first_water_home != 1">New</span>
            <!-- 小红点 -->
            <span class="num" v-else></span>
            <div class="content" v-if="Tseconds || useUserStore().waterNum == useUserStore().day_limit">
                <span v-if="useUserStore().waterNum < useUserStore().day_limit - 1">{{ state.water.三秒後再來 }}</span>
                <span v-else>{{ state.water.明天再來 }}</span>
            </div>
        </div>
        <div class="navv ad" @click="initWaterAd" v-else-if="friendStore.isOwn && useTreeStore().hasPlanted && !isShowOpenWater && !alreadyAdaward">
            <!-- 小红点 -->
            <span class="num"></span>
            <div class="content">
                <span>{{ state.advertisement.看廣告領減碳值 }}</span>
            </div>
        </div>
    </div>
</template>

<script setup lang='ts'>
import { useLang, useToast, useEventBus,useDialog,useLoading } from 'hook';
import { ref,onMounted, computed,nextTick } from 'vue';
import { useTreeStore, useUserStore, useFriendStore,useTaskStore,useAppStore, useAdawardStore } from '@/store';
import { logEventStatistics,getAppVersion } from '@via/mylink-sdk';
import { watch } from 'vue';
import wateringDiglog from "@/components/wateringDiglog.vue";
import adDialog from "@/components/adDialog.vue";
const loading =  useLoading()
const friendStore = useFriendStore()//api
const { state, lang } = useLang()
const { toast } = useToast()
const eventBus = useEventBus()
const dialog = useDialog({
    wateringDiglog,
    adDialog
})
let wateringDig = dialog.get('wateringDiglog')
// const adShow = computed(() => {
//     return useAppStore().params.resultCode == '0'
// })


let alreadyAdaward = ref(true)
// 调用该组件
onMounted(async ()=>{
    if (useUserStore().inLogin) {
        let info = await useTaskStore().getInfo()
        alreadyAdaward.value = info.already_adaward
        first_water_home.value = info.webui_manage_values ? info.webui_manage_values.first_water_home : ''
        if (info.watering_info.day_count < info.watering_info.day_limit) {
            isShowOpenWater.value = true
        }
    }

    
})

watch(()=>useUserStore().inLogin,async ()=>{
    if (useUserStore().inLogin) {
        let info = await useTaskStore().getInfo()
        alreadyAdaward.value = info.already_adaward
        first_water_home.value = info.webui_manage_values ? info.webui_manage_values.first_water_home : ''
        console.log(first_water_home.value,'first_water_homefirst_water_homefirst_water_home');
        if (info.watering_info.day_count < info.watering_info.day_limit) {
            isShowOpenWater.value = true
        }
    }
})

const props = defineProps({
    cardType: {//卡片类型
        type: Number,
        required: true,
        default: 0
    },
    tree_energy: {//对树减碳值
        type: Number
    },
    user_energy: {//用户持有的减碳值
        type: Number
    }
})
let emit = defineEmits(['handle', 'init', 'addBuffer'])

let first_water_home = ref<string | number>('')
let tree_energy = props.tree_energy
let user_energy = props.user_energy

let Tseconds = ref<boolean>(false)  //三秒后浇水
let waterImg = ref<string>('')
let waterShow = ref<boolean>(false) //水龙头



let waterTimer:NodeJS.Timeout | null = null   // 三秒浇水间隔的定时器
const openWater = async () => {   // 浇水函数
    if (waterTimer || useUserStore().waterNum == useUserStore().day_limit) return
    logEventStatistics('garden_watering_click')
    Tseconds.value = true
    if (first_water_home.value != '1') {
        useUserStore().setUpData('first_water_home', '1')
        first_water_home.value = '1'
    }
    waterShow.value = true
    // 判断是否有使用二倍功能卡
    if (props.cardType === 1) {
        waterImg.value = 'water2.gif'
    } else {
        waterImg.value = 'water.gif'
    }
    await useUserStore().wartering()
        .then(async res => {
            await emit('init')
            waterTimer = setTimeout(async () => {
                waterTimer = null
                waterImg.value = ''
                waterShow.value = false
                if (res) {
                    let energyadd = res.user_actions.filter((item) => item.action_key == 'energy_add')
                    let upgrade = res.user_actions.filter((item) => item.action_key == 'tree_upgrade').sort((a, b) => {
                        return a.tree_value.level - b.tree_value.level
                    })
                    let complete = res.user_actions.filter((item) => item.action_key == 'tree_complete')
                    let action = [...upgrade, ...complete]
                    if (action.length == 0) {
                        energyadd.forEach((item) => {
                            if (item.action_key == 'energy_add') {
                                emit('handle')
                            }
                        })
                    } else {
                        action.forEach((item) => {
                            if (item.action_key == 'tree_upgrade' || item.action_key == 'tree_complete') {
                                let reward_items = item.tree_value.reward_items
                                let integral_value = item.integral_value || 0
                                // 升级后 赋值风控状态
                                let integralStatus = item.integral_status
                                emit('addBuffer', item.tree_value.level - 1, item.tree_value.level, reward_items, integral_value, integralStatus)
                            }
                        })
                    }
                }

                // 关锁
                wateringLocks.value = false;

                // 浇水次数完了
                if (useUserStore().waterNum >= useUserStore().day_limit) {
                    eventBus.once('initWaterAd',initWaterAd)
                    wateringDig.on('close',()=>{
                        wateringDig.close()
                    }).show({},{maskClose:false})
                } else {
                    Tseconds.value = false
                }
            }, 3000);
        })
        .catch(err => {
            Tseconds.value = false
            toast(state.task.發生異常)
        })

}



// 浇水上限，3s后关闭浇水按钮相关逻辑
const useShowWater = () => {
    // 是否显示浇水按钮
    let isShowOpenWater = ref(false)

    // 三秒后 浇水按钮消失
    let timer: number | null = null;
    const wateringLocks = ref(true);    // 能浇水时，锁关闭
    // 判断以何种方式 关闭浇水按钮
    const closeOpenWater = () => {
        // 不存在浇水情况(每日浇水上限)，直接关闭按钮
        if (wateringLocks.value) {
            isShowOpenWater.value = false
            return;
        }
        // 存在浇水情况，关锁。延时关闭按钮
        clearTimeout(Number(timer));
        timer = window.setTimeout(() => {
            isShowOpenWater.value = false
        })
    }

    return {
        wateringLocks,
        isShowOpenWater,
        closeOpenWater,
    }
}
const {
    wateringLocks,
    isShowOpenWater,
    closeOpenWater,
} = useShowWater();

// 暴露方法
defineExpose({
    closeOpenWater,
    Tseconds
})

const openAd = ()=>{
    useAppStore().startGoogleAd()
    .then(async (notiParams) => {
        console.log('广告完成，结果:', notiParams)
        // 等待页面变为可见状态后再执行奖励发放
        const executeReward = async () => {
            let callBackRes = await useAppStore().callBackAdaward(useAppStore().unitId,JSON.stringify(notiParams))
            if (callBackRes) {
                toast(state.advertisement.已成功領取XXg減碳值)
                // bus.emit('upWater', callBackRes.data)
                alreadyAdaward.value = true
                const res = callBackRes.data
                 if (res) {
                    let energyadd = res.user_actions.filter((item) => item.action_key == 'energy_add')
                    let upgrade = res.user_actions.filter((item) => item.action_key == 'tree_upgrade').sort((a, b) => {
                        return a.tree_value.level - b.tree_value.level
                    })
                    let complete = res.user_actions.filter((item) => item.action_key == 'tree_complete')
                    let action = [...upgrade, ...complete]
                    if (action.length == 0) {
                        energyadd.forEach((item) => {
                            if (item.action_key == 'energy_add') {
                                emit('handle')
                            }
                        })
                    } else {
                        action.forEach((item) => {
                            if (item.action_key == 'tree_upgrade' || item.action_key == 'tree_complete') {
                                let reward_items = item.tree_value.reward_items
                                let integral_value = item.integral_value || 0
                                // 升级后 赋值风控状态
                                let integralStatus = item.integral_status
                                emit('addBuffer', item.tree_value.level - 1, item.tree_value.level, reward_items, integral_value, integralStatus)
                            }
                        })
                    }
                }
            }
        }
        if (document.visibilityState === 'visible') {
            console.log('已在页面，直接弹窗')
            await executeReward()
        } else {
            const handleVisibilityChange = async () => {
                if (document.visibilityState === 'visible') {
                    // 移除监听
                    console.log('检测到已经关闭了广告并返回页面，开始执行发放奖励')
                    document.removeEventListener('visibilitychange', handleVisibilityChange)
                    await executeReward()
                }
            }
            document.addEventListener('visibilitychange', handleVisibilityChange)
        }
    })
    .catch((error) => {
        console.log('广告流程失败:', error)
        let adDia = dialog.get('adDialog')
        if (error.resultCode == '-2' || error.resultCode == '-1') {
                adDia
                .on('close',()=>{adDia.close()})
                .show({
                    title:state.advertisement.暫無Google廣告來源請稍後再來,
                    desc:state.advertisement.詳情請參閲條款及細則
                },{maskClose:false})
            } else if (error.resultCode == '-3') {
                adDia
                .on('close',()=>{adDia.close()})
                .show({
                    title:state.advertisement.您當前的設備不支持Google廣告播放,
                    desc:state.advertisement.詳情請參閲條款及細則
                },{maskClose:false})
            } else if (error.resultCode == '-4') {
                adDia
                .on('close',()=>{adDia.close()})
                .show({
                    title:state.advertisement.您當前設備或MyLinkApp版本過低不支持Google廣告播放,
                    desc:state.advertisement.詳情請參閲條款及細則
                },{maskClose:false})
            }
        // toast(state.advertisement.網絡異常)
    })
}

const initWaterAd = async ()=>{
    let adDia = dialog.get('adDialog')
    const version = getAppVersion()
    const ver = Number(version.split('.').join(''))
    if (ver <= 1130) {
        adDia
        .on('close',()=>{adDia.close()})
        .show({
            title:state.advertisement.您當前設備或MyLinkApp版本過低不支持Google廣告播放,
            desc:state.advertisement.詳情請參閲條款及細則
        },{maskClose:false})
        return
    }
    let timerOut = false
    loading.loading.open()
    let timer = setTimeout(()=>{
        loading.loading.close()
        timerOut = true
        adDia
        .on('close',()=>{adDia.close()})
        .show({
            title:state.advertisement.暫無Google廣告來源請稍後再來,
            desc:state.advertisement.詳情請參閲條款及細則
        },{maskClose:false})
    },10000)
    let unitIdObj = useAdawardStore().unitIdObj
    if (Object.keys(unitIdObj).length != 0) {
        await useAppStore().initAd('0', unitIdObj)
        if(timerOut)return
        openAd()
        clearTimeout(timer)
        loading.loading.close()
    }
}

// eventBus.once('initWaterAd',initWaterAd)
// wateringDig.on('close',()=>{
//     wateringDig.close()
// }).show({},{maskClose:false})
// adDia
// .on('close',()=>{adDia.close()})
// .show({
//     title:'您當前設備或MyLink App版本過低，不支持Google廣告播放',
//     desc:'詳情詳見<span style="color:#EF8A28;">《條款細則》</span>'
// },{maskClose:false})
</script>

<style lang='less' scoped>
.no-operate {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 32;
    right: 0;
}

.water {
    position: absolute;
    z-index: 32;
    top: -300px;
    left: calc(120px - 40vw);
    width: 240px;
    height: 320px;
}

.navv {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: url('@assets/TT/myhome.png') no-repeat;
    background-size: 100% 100%;
    box-shadow: 0px 0px 24px 2px rgba(0, 0, 0, 0.08);

    .num {
        position: absolute;
        width: 16px;
        height: 16px;
        background: #FD4232;
        border-radius: 50%;
        right: 4px;
        top: 4px;
        z-index: 10;
    }

    .content {
        position: absolute;
        // width: 150px;
        background: #FFFFFF;
        border-radius: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 7px 12px;
        top: 117px;
        left: calc(50%);
        transform: translateX(-50%);
        white-space: nowrap;
        span {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
            font-weight: 500;
            color: #4E5B7E;
        }
    }

    .content::after {
        width: 18px;
        height: 12px;
        content: '';
        clear: both;
        display: block;
        background: url('@assets/imgs/all-jt.png') no-repeat;
        background-size: cover;
        position: absolute;
        top: -12px;
        transform: rotate(-180deg);
    }

    .new {
        position: absolute;
        background: #E03131;
        right: 4px;
        top: 4px;
        font-size: 8px;
        font-family: PingFang HK-Semibold, PingFang HK;
        font-weight: 600;
        color: #FFFFFF;
        border-radius: 65px;
        padding: 0 5px;
    }
}

.grow {
    background: url('@assets/TT/water.png') no-repeat;
    background-size: 100% 100%;
}
.ad{
    background: url('@assets/TT/ad.png') no-repeat;
    background-size: 100% 100%;
}
</style>
