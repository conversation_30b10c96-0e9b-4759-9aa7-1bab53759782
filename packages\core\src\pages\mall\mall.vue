<template>
    <div class="container">
        <!-- 减碳商城 -->
        <navComponent :returnType="2" :showTransmit="false" />
        <div class="img shopping-img">
            <img :src="$imgs['shopping.png']" alt="">
            <div class="shopping-info">
                <div class="left-info">
                    <div class="numerical">
                        <span>{{ useUserStore().inLogin ? myEnergy_total : '--' }}g</span>
                    </div>
                    <div class="info-tips">
                        <div class="icon"></div>
                        <div class="content">
                            <span>{{ state.shopping.當前減碳值 }}</span>
                        </div>
                    </div>
                </div>
                <div class="right-info">
                    <div class="more" @click="router.push({
                        path: 'upStrategy',
                        query: {
                            hideNavigationBar: 'true'
                        }
                    })">
                        <span>{{ state.shopping.獲取更多減碳值 }}</span>
                        <div class="icon"></div>
                    </div>
                    <div class="record" @click="toBuyHistory">
                        <span>{{ state.shopping.購買紀錄 }}</span>
                        <div class="icon"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tabs">
            <div class="title" :class="{'active':tabs === 0}"  @click="tabs = 0">
                MyGarden
                <img  v-if="tabs === 0" :src="$imgs['Tengman.png']" alt="">
            </div>
            <div class="title" :class="{'active':tabs === 1}" @click="tabs = 1">
                MyGame
                <img :src="$imgs['Tengman.png']" v-if="tabs === 1" alt="">
            </div>
        </div>
        <!-- 减碳商城道具包 -->
        <div class="shopping-main" v-if="tabs === 0">
            <div class="title">
                <span>{{ state.shopping.裝飾道具包 }}</span>
            </div>
            <div class="shopping-list" ref="shoppingList">
                <div class="shopping-item" v-for="item in PropPackList" @click="shoppingBtnEvent(item)">
                    <div class="new-label" v-if="item.newest">
                        <span>{{ state.shopping.新品 }}</span>
                    </div>
                    <div class="not-shopping" v-if="item.status != 1 && item.status != -2 && useUserStore().inLogin">
                        <img :src="$imgs[`shopping/${lang}/notShopping${item.status}.png`]" alt="">
                    </div>
                    <div class="name">
                        <span>{{ item.metadata[lang == 'tc' ? 'name' : (lang == 'en' ? 'nameEn' : 'nameSc')] }}</span>
                    </div>
                    <img class="daojubao" :src="$imgs[`package/daojubao/${item.code}.png`]" alt="">
                    <div class="star">
                        <img v-for="i in item.rarity" :src="$imgs['plantSelection/star.png']" alt="">
                    </div>
                    <div class="shopping-btn" :class="{ 'shopping-btn-gary': item.status != 1 || !useUserStore().inLogin }">
                        <div class="icon"></div>
                        <span>{{ item.energy_value }}g</span>
                    </div>
                </div>
                <div class="footer">
                    <span>{{ state.shopping.到底啦更多實惠商品敬請期待 }}</span>
                </div>
            </div>
        </div>
        <!-- mygame -->
        <div class="shopping-main" v-if="tabs === 1">
            <div class="title">
                <span>{{ state.shopping.遊戲道具 }}</span>
            </div>
            <div class="shopping-list" ref="shoppingList">
                <div class="shopping-item" v-for="item in myGameList" @click="shoppingBtnEvent(item)">
                    <div class="new-label" v-if="item.newest">
                        <span>{{ state.shopping.新品 }}</span>
                    </div>
                    <div class="not-shopping" v-if="item.status != 1 && item.status != -2 && useUserStore().inLogin">
                        <img :src="$imgs[`shopping/${lang}/notShopping${item.status == 3 ? 2 : item.status}.png`]" alt="">
                    </div>
                    <div class="name">
                        <span>{{ item.metadata[lang == 'tc' ? 'name' : (lang == 'en' ? 'nameEn' : 'nameSc')] }}</span>
                    </div>
                    <img class="daojubao" :src="item.metadata.iconUrl" alt="">
                    <div class="star">
                        <img v-for="i in item.rarity" :src="$imgs['plantSelection/star.png']" alt="">
                    </div>
                    <div class="shopping-btn" :class="{ 'shopping-btn-gary': item.status != 1 || !useUserStore().inLogin }">
                        <div class="icon"></div>
                        <span>{{ item.energy_value }}g</span>
                    </div>
                </div>
                <div class="footer">
                    <span>{{ state.shopping.到底啦更多實惠商品敬請期待 }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang='ts'>
import { useLang, useRouter, useEventBus, useDialog, useToast } from 'hook';
import { onMounted, ref, onBeforeMount } from 'vue'
import { usePropStore, useTaskStore, useTreeStore, useUserStore } from '@/store'
import navComponent from '@/components/navComponent.vue';
const { router, currentRoute } = useRouter()
import { logEventStatistics } from '@via/mylink-sdk'
const { getPropList, getInfo, getShareList } = useTaskStore()
import shoppingDialog from '@/components/shoppingDialog.vue';
import purchaseSuccessfulDialog from '@/components/purchaseSuccessfulDialog.vue'
const tabs = ref(0)
let myEnergy_total = ref(0)
let PropPackList = ref()
let shoppingNew = ref()
const { state, lang } = useLang()
const eventBus = useEventBus()
const dialog = useDialog({
    shoppingDialog,
    purchaseSuccessfulDialog
})


let purchaseSuccessfulDia = dialog.get('purchaseSuccessfulDialog')


// 去购买记录
const toBuyHistory = async () => {
    if (!useUserStore().inLogin) {
        await useUserStore().login()
        if (useUserStore().inLogin) {
            router.push({
                path: 'record',
                query: {
                    tab: '1',
                    hideNavigationBar: 'true'
                }
            })
            logEventStatistics('garden_cr_mall_buy')
        }
    } else {
        router.push({
            path: 'record',
            query: {
                tab: '1',
                hideNavigationBar: 'true'
            }
        })
        logEventStatistics('garden_cr_mall_buy')
    }
}
onBeforeMount(() => {
    if (useUserStore().inLogin) {
        init()
    }
    eventBus.on('goUse', () => {
        router.replace({
            path:'/package',
            query: {
                "hideNavigationBar": 'true'
            }
        })
    })
    eventBus.on('reloadState', async () => {
        let treeDate = await getInfo()
        myEnergy_total.value = treeDate.user.energy_total
        await usePropStore().commodityList(0)
        .then(res => {
            console.log(res);
            PropPackList.value = res.commodities
        })
        await usePropStore().commodityList(1)
        .then(res => {
            myGameList.value = res.commodities
        })
    })
})
onMounted(async () => {
    if (useUserStore().inLogin) {
        await usePropStore().commodityList(tabs.value)
            .then(res => {
                PropPackList.value = res.commodities
            })
        await getMyGameList()
        await useUserStore().setUpData('shopping_new', '1')
    } else {
        await usePropStore().commodityListNoLogin(tabs.value)
        .then(res => {
            PropPackList.value = res.commodities
        })
        await getMyGameList()
    }
    shoppingNew.value = false
})
const init = async () => {
    let treeDate = await getInfo()
    myEnergy_total.value = treeDate.user.energy_total
    await useUserStore().setUpData('commodity_newest_value',treeDate.commodity_newest_value.toString())
}
let mallItem = ref();
const shoppingBtnEvent = (item) => {
    mallItem.value = item
    if (item.status == -2) useToast().toast(state.shopping.減碳值不足)
    dialog.get('shoppingDialog').show({
        item: {
            num: item.energy_value,
            name: item.metadata[lang.value == 'tc' ? 'name' : (lang.value == 'en' ? 'nameEn' : 'nameSc')],
            label: item.newest,
            description: item.metadata[lang.value == 'tc' ? 'description' : (lang.value == 'en' ? 'descriptionEn' : 'descriptionSc')],
            rarity: item.rarity,
            countText: item.metadata[lang.value == 'tc' ? 'countText' : (lang.value == 'en' ? 'countTextEn' : 'countTextSc')],
            effectiveText: item.metadata[lang.value == 'tc' ? 'effectiveText' : (lang.value == 'en' ? 'effectiveTextEn' : 'effectiveTextSc')],
            flowText: item.metadata[lang.value == 'tc' ? 'flowText' : (lang.value == 'en' ? 'flowTextEn' : 'flowTextSc')],
            code: item.code,
            target:item.target,
            iconUrl:item.metadata.iconUrl
        },
        status: item.status,
        id: item.id,
        target_value:item.exists_target_value,
        jump_url:item.jump_url,
        expired_at:item.expired_at,
        init: mallInit
    })
    switch (item.code) {
        case "fulonghesui":
            logEventStatistics('garden_cr_mall_dragon')
            break;
        case "senlinmijing":
            logEventStatistics('garden_cr_mall_forest')
            break;
        case "bingxuetiandi":
            logEventStatistics('garden_cr_mall_snow')
            break;
        default:
            logEventStatistics(item.code + '_buy')
            break;
    }
    // router.push('/shopping')
}

// 减碳商城弹窗初始化
const mallInit = async () => {
    init();
    const mallItemList = await usePropStore().commodityList(tabs.value)
    mallItem.value = mallItemList.commodities.filter(element => {
        return element.code == mallItem.value.code
    })
    console.log(mallItem.value[0]);
    shoppingBtnEvent(mallItem.value[0])
}

const myGameList = ref<Record<string,any>>([])
const getMyGameList = async ()=>{
    if (useUserStore().inLogin) {
        await usePropStore().commodityList(1)
            .then(res => {
                myGameList.value = res.commodities
            })
    } else {
        await usePropStore().commodityListNoLogin(1)
            .then(res => {
                myGameList.value = res.commodities
            })
    }
}
</script>

<style lang='less' scoped>
.container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .shopping-img {
        height: 732px;
        width: 100%;

        img {
            width: 100%;
            height: 100%;
        }

        .shopping-info {
            top: 206px;
            left: calc(50% - 335px);
            width: 670px;
            height: 178px;
            background: #FFFFFF;
            box-shadow: 0px 6 12px 2px rgba(0, 0, 0, 0.1), inset 0px 6 0px 2px rgba(222, 245, 220, 0.4);
            border-radius: 48px 24px 48px 24px;
            position: absolute;
            display: flex;
            justify-content: space-between;
            padding: 36px 32px;

            .left-info {
                .numerical {
                    font-size: 56px;
                    font-family: Arial, Arial;
                    font-weight: bold;
                    color: #2EB206;
                }

                .info-tips {
                    display: flex;
                    margin-right: 8px;
                    margin-top: 10px;
                    align-items: center;

                    .icon {
                        background: url('@assets/imgs/leaf.png') no-repeat;
                        background-size: cover;
                        width: 28px;
                        height: 28px;
                        margin-right: 8px;
                    }

                    .content {
                        font-size: 24px;
                        font-family: PingFang SC, PingFang SC;
                        font-weight: bold;
                        color: #196F20;
                    }
                }
            }

            .right-info {
                .more {
                    width: 240px;
                    height: 56px;
                    background: linear-gradient(131deg, #22B728 0%, #2E8D30 100%);
                    border-radius: 12px 12px 12px 12px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    span {
                        font-size: 24px;
                        font-family: PingFang TC, PingFang TC;
                        font-weight: 600;
                        color: #FFFFFF;
                        margin-right: 14px;
                    }

                    .icon {
                        background: url('@assets/imgs/info-right.png') no-repeat;
                        background-size: cover;
                        width: 10px;
                        height: 14px;
                    }
                }

                .record {
                    font-size: 24px;
                    font-family: PingFang TC, PingFang TC;
                    font-weight: 400;
                    color: #4E5B7E;
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    margin-top: 16px;

                    .icon {
                        background: url('@assets/imgs/shopping-right.png') no-repeat;
                        background-size: cover;
                        width: 12px;
                        height: 20px;
                        margin-left: 8px;
                    }
                }
            }
        }
    }
    .tabs{
        display: flex;
        justify-content: space-around;
        height: 52px;
        background: rgba(62,205,66,0.3);
        box-shadow: inset 0px 0 24px 2px rgba(25,75,31,0.13);
        border-radius: 48px 48px 48px 48px;
        transform: translateY(-304px);
        .title{
            width: 156px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #196F20;
            line-height: 28px;
        }
        .active{
            color: #fff;
            width: 168px;
            height: 52px;
            background: linear-gradient( 1deg, #33B43F 0%, #47D048 100%);
            box-shadow: inset 0px 4px 0px 2px #64DE69;
            border-radius: 48px;
            position: relative;
            img{
                width: 116px;
                position: absolute;
                top: -16px;
                right: -8px;
            }
        }
    }

    .img {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        width: 100vw;
        height: 750px;
        position: relative;

        .sceneProps {
            bottom: 130px;
        }

        img {
            width: 100%;
        }
    }

    .shopping-main {
        display: flex;
        flex-direction: column;
        background-color: #fff;
        border-radius: 48px 48px 0px 0px;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        top: 524px;
        .title {
            width: 316px;
            height: 100px;
            background: url('@assets/imgs/shopping-title.png') no-repeat;
            background-size: 100%;
            margin: 0 auto;
            margin-top: -12px;
            font-size: 28px;
            font-family: PingFang SC, PingFang SC;
            font-weight: bold;
            color: #FFFFFF;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 52px;
        }

        .shopping-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            padding: 0 40px;
            overflow-y: scroll;
            height: 100%;
            .shopping-item {
                width: 316px;
                height: 444px;
                background: url('@assets/imgs/shopping_box.png') no-repeat;
                background-size: cover;
                margin-top: 22px;
                position: relative;

                .new-label {
                    position: absolute;
                    width: 92px;
                    height: 40px;
                    background: linear-gradient(180deg, #FF8888 0%, #E22121 100%);
                    border-radius: 24px 24px 24px 4px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 6;

                    span {
                        font-size: 22px;
                        font-family: PingFang SC, PingFang SC;
                        font-weight: 500;
                        color: #FFFFFF;
                        line-height: 36px;
                    }
                }

                .purchase-rate {
                    width: 162px;
                    height: 40px;
                    background: linear-gradient(180deg, #FFB865 0%, #FF6739 100%);
                    border-radius: 24px 24px 24px 4px;
                }

                .not-shopping {
                    position: absolute;
                    top: 12px;
                    left: 12px;
                    width: 292px;
                    height: 332px;
                    background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.2) 100%);
                    border-radius: 80px 16px 16px 16px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    img {
                        width: 162px;
                        height: 162px;
                    }
                }

                .name {
                    font-size: 15px;
                    font-family: PingFang TC, PingFang TC;
                    font-weight: 600;
                    color: #283760;
                    width: 165px;
                    margin: 27px auto 0;
                    height: 66px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }

                .daojubao {
                    display: block;
                    width: 160px;
                    height: 160px;
                    margin: 16px auto 0;
                }

                .star {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-top: 14px;

                    img {
                        width: 22px;
                        height: 20px;
                        margin-right: 4px;
                    }
                }

                .shopping-btn {
                    width: 276px;
                    height: 56px;
                    background: linear-gradient(0deg, #FDC23E 0%, #FDED9E 100%);
                    border-radius: 28px 28px 28px 28px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 63px auto 0;

                    .icon {
                        width: 28px;
                        height: 28px;
                        background: url('@assets/imgs/leaf.png') no-repeat;
                        background-size: cover;
                        margin-right: 8px;
                    }

                    span {
                        font-size: 24px;
                        font-family: PingFang TC, PingFang TC;
                        font-weight: 600;
                        color: #6A5201;
                    }
                }

                .shopping-btn-gary {
                    background: linear-gradient(180deg, #E5E5E5 0%, #ACB4AF 100%);

                    span {
                        color: rgba(67, 67, 67, 0.4);
                    }

                    .icon {
                        background: url('@assets/imgs/leaf_gary.png') no-repeat;
                        background-size: cover;
                    }
                }
            }

            .footer {
                font-size: 24px;
                font-family: PingFang TC, PingFang TC;
                font-weight: 400;
                color: #A7AEC3;
                text-align: center;
                padding: 42px 0;
                width: 100%;
            }
        }

        .footer {
            font-size: 24px;
            font-family: PingFang TC, PingFang TC;
            font-weight: 400;
            color: #A7AEC3;
            text-align: center;
            padding-top: 42px;
        }
    }
}
</style>
