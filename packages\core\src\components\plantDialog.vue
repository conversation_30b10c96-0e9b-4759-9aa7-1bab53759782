<template>
    <div class="box">
        <img :src="$imgs['plantbg.png']" alt="" />
        <template v-if="tabIndex === 0">
            <p class="toptext">{{ state.home.x成就1.replace('{x}', state.plant[treeCode]?.plantName) }}</p>
        </template>
        <template v-else-if="tabIndex === 1">
            <p class="toptext">{{ state.plant[treeCode]?.plantName + ' NFT' }}</p>
        </template>

        <!--植物与ntf详情图切换-->
        <detailTab :treeCode="treeCode" @changetab="changeTab" :defaultTabIndex="tabIndex"></detailTab>

        <!--针对树的tab-->
        <template v-if="tabIndex === 0">
            <!--当前状态的图标-->
            <img :src="stateImgTab1" class="stateImg" />
            <!-- 不同权限对应不同的按钮 -->
            <!-- TODO NFT -->
            <template v-if="useUserStore().isHK == 0 && treeCode !== 'huangzhongmu' && treeCode !== 'shuishirong'">
                <!--未种成或者正在种的状态-->
                <template v-if="planted === false">
                    <div class="plant_tip click_btn disactive">{{ state.home.完成种植即成就 }} ></div>

                    <div class="plant_detail btn3" @click="toInfo">{{ state.home.查看植物詳情 }} ></div>
                </template>

                <!--种植完成的状态-->
                <template v-else-if="planted === true">
                    <div class="goHome click_btn active" @click="toHome">
                        <template v-if="props.nft_active">{{ state.nft.goMyHome }} ></template>
                        <template v-else>{{ state.nft.去MyHome佈置 }} ></template>
                        <div class="limit__content" v-if="!props.nft_recycled && !props.nft_recycled_before_upgrade">
                            <i class="limit">{{ state.nft.everyDayTips }}</i>
                        </div>
                    </div>

                    <div class="btn-box" style="display: flex; justify-content: space-around">
                        <p class="btn__center" @click="toInfo">
                            {{ state.home.查看植物詳情.replace('{x}', state.plant[treeCode]?.plantName) }} >
                        </p>
                        <p @click="preview" class="btn2">{{ state.nft.layoutPreview }} ></p>
                    </div>
                </template>
            </template>
            <template v-else>
                <div class="goHome click_btn active" @click="toInfo">
                    {{ state.home.查看植物詳情 }}
                </div>
                <p class="unLock">{{ state.toast.即將推出更多精彩玩法 }}</p>
            </template>
        </template>
        <!--针对ntf的tab-->
        <template v-else-if="tabIndex === 1">
            <!--当前状态的图标-->
            <img :src="stateImgTab2" class="stateImg" />
            <!-- 不同权限对应不同的按钮 -->
            <template v-if="useUserStore().isHK == 0">
                <!--未种成未铸造的状态-->
                <template v-if="!planted">
                    <div class="nft_tip click_btn disactive">
                        {{ state.nft.種成後可鑄造NFT }}
                    </div>

                    <div class="nft_equity_tip btn3" @click="showNFTEquity">
                        {{ state.nft.showNFTEquity }}
                    </div>
                </template>

                <!-- 已种成已铸造已回收的状态 -->
                <template v-else-if="nft_recycled_before_upgrade || nft_recycled">
                    <div class="recycled">{{ state.nft.已回收 }}</div>
                    <div class="nft_equity_tip btn3" @click="showNFTEquity">{{ state.nft.showNFTEquity }} ></div>
                </template>

                <!--已种成但未铸造的状态-->
                <template v-else-if="planted && !foundryed && !nft_upgrade_notice">
                    <div class="nft_tip click_btn active" @click="toNFT">{{ state.nft.鑄造NFT }}></div>
                    <div class="nft_equity_tip btn3" @click="showNFTEquity">
                        {{ state.nft.showNFTEquity }}
                    </div>
                </template>

                <!--已种成已铸造v2未回收的状态-->
                <template v-else-if="planted && foundryed && !nft_recycled && !nft_upgrade_notice">
                    <div class="goHome click_btn active" @click="seeNFT">
                        <div>{{ state.nft.查看此NFT }}></div>
                    </div>
                    <div class="btn-box" style="display: flex; justify-content: space-around">
                        <p class="btn__center" @click="showNFTEquity">{{ state.nft.showNFTEquity }}></p>
                        <p @click="toRecovery" class="btn2">{{ state.nft.前往回收 }}></p>
                    </div>
                </template>

                <!--已种成已铸造v1未回收的状态-->
                <template v-else-if="planted && !foundryed && !nft_recycled_before_upgrade && nft_upgrade_notice">
                    <div class="goHome click_btn active" @click="toNFT">
                        <div>{{ state.nft.鑄造升級版NFT }}></div>
                    </div>
                    <div class="btn-box" style="display: flex; justify-content: space-around">
                        <p class="btn__center" @click="showNFTEquity">{{ state.nft.showNFTEquity }}></p>
                        <p @click="toRecovery" class="btn2">{{ state.nft.前往舊版回收NFT }}></p>
                    </div>
                </template>
            </template>
        </template>

        <button class="btn1" @click="emit('close')"></button>

        <!--nft权益弹窗-->
        <nftEquity
            :showSheet="isShowNftEquity"
            @close="isShowNftEquity = false"
            @toclause="emit('close')"
            :treeCode="treeCode"
        ></nftEquity>
        <!--布置预览弹窗-->
        <nftPreview :showSheet="isShowNftPreview" @close="isShowNftPreview = false" :treeCode="treeCode"></nftPreview>
    </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref } from 'vue'
import { useLang, useRouter, useEnvConfig, useDialog } from 'hook'
import updateDialog from '@/components/updateDialog.vue'
import detailTab from './plantDiglog/detailTab.vue'
import nftEquity from './plantDiglog/nftEquity.vue'
import nftPreview from './plantDiglog/nftPreview.vue'
import recoveryDialog from '@/components/recoveryDialog.vue'
import { getAppVersion, logEventStatistics } from '@via/mylink-sdk'
import { useTaskStore, useUserStore } from '@store/index'
import lockImg from '@/assets/imgs/NTFDetail/lock.png'
import plantingImg from '@/assets/imgs/NTFDetail/planting.png'
import successImg from '@/assets/imgs/NTFDetail/success.png'
import castedImg from '@/assets/imgs/NTFDetail/casted.png'
import recycledImg from '@/assets/imgs/NTFDetail/recycled.png'
import { log } from 'console'

const envConfig = useEnvConfig()
const { state } = useLang()
const dialog = useDialog({
    updateDialog,
    recoveryDialog
})
const props = defineProps({
    treeCode: {
        type: String,
        required: false
    },
    tree_id: {
        type: Number,
        required: false
    },
    planted: {
        type: Boolean,
        required: false,
        defualt: false
    },
    foundryed: {
        type: Boolean,
        required: false,
        defualt: false
    },
    nft_group_id: {
        type: String,
        required: false
    },
    owned: {
        type: Boolean,
        required: false,
        defualt: false
    },
    nft_active: {
        type: Boolean,
        required: false,
        defualt: false
    },
    nft_recycled: {
        type: Boolean,
        required: false,
        defualt: false
    },
    nft_upgrade_notice: {
        type: Boolean,
        required: false,
        defualt: false
    },
    nft_recycled_before_upgrade: {
        type: Boolean,
        required: false,
        defualt: false
    }
})

/** tab1状态对应的图标 */
const stateImgTab1 = ref('')
/** tab2状态对应的图标 */
const stateImgTab2 = ref('')
/** 是否显示nft权益的弹窗 */
const isShowNftEquity = ref(false)
/** 是否显示myhome摆设预览 */
const isShowNftPreview = ref(false)
/** 当前tab页 */
const tabIndex = ref(0)
let emit = defineEmits(['close'])
onBeforeMount(async () => {
    // 设定当前状态图标
    // 树木状态
    if (!props.owned) {
        // 未种
        stateImgTab1.value = lockImg
    } else if (!props.planted) {
        // 已种但未完成
        stateImgTab1.value = plantingImg
    } else {
        stateImgTab1.value = successImg
    }

    // nft状态
    if (!props.foundryed && !props.nft_upgrade_notice) {
        stateImgTab2.value = lockImg
        return
    } else if (props.nft_recycled_before_upgrade && props.nft_upgrade_notice) {
        stateImgTab2.value = recycledImg
        return
    } else if (!props.nft_recycled_before_upgrade && props.nft_upgrade_notice) {
        stateImgTab2.value = castedImg
        return
    } else if (!props.nft_recycled && !props.nft_upgrade_notice) {
        stateImgTab2.value = castedImg
        return
    } else if (props.nft_recycled && !props.nft_upgrade_notice) {
        stateImgTab2.value = recycledImg
        return
    }

    // 在种植完成但是未铸造的情况下显示到nft的tab
    if (props.planted && !props.foundryed) {
        tabIndex.value = 1
    }
})

const { router, currentRoute } = useRouter()
const toInfo = () => {
    logEventStatistics('garden_check_plants')
    emit('close')
    router.push({
        path: '/plantDetail/' + props.tree_id,
        query: {
            hideNavigationBar: 'true'
        }
    })
}

const toNFT = () => {
    logEventStatistics('garden_mint_nft')
    emit('close')
    router.push({
        path: '/treeNFT/' + props.tree_id,
        query: {
            hideNavigationBar: 'true',
            treeCode: props.treeCode,
            treeId: props.tree_id,
            path: 'activity'
        }
    })
}

const seeNFT = () => {
    logEventStatistics('garden_check_nft')
    if (useEnvConfig().RUN_ENV == 'develop' || useEnvConfig().RUN_ENV == 'uat') {
        // location.href = "http://*************/nftweb/#/third-party?origin=1&target=numberExchange&forceHideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>"
        location.href = `openurl-modal://https://*************/nftweb/#/third-party?origin=4&target=nftDetail&groupId=${props.nft_group_id}&forceHideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>`
    } else {
        location.href = `openurl-modal://https://cdn.mylink.hk.chinamobile.com/blockchain/link/nft/index.html#/third-party?origin=4&target=nftDetail&groupId=${props.nft_group_id}&forceHideNavigationBar=true&lang=<<cmcchkhsh_cmplang>>`
    }
}

let isPat = envConfig.RUN_ENV == 'production' || envConfig.RUN_ENV == 'beta' ? true : false
let uatLink = {
    home: `openurl-modal://https://*************/activity/myCity/#/myHome?lang=${
        useLang().lang.value
    }&forceHideNavigationBar=true`,
    home2: 'openurl-modal://https://<<cmcchkhsh_host>>/activity/myCity/#/newMyHome?lang=<<cmcchkhsh_cmplang>>&hideNavigationBar=true',
    home3: `openhkhshlogin://cmcchkhsh://metaverse?url=myhome`
}
let patLink = {
    home: `openurl-modal://https://mylink.komect.com/activity/myCity/#/myHome?lang=${
        useLang().lang.value
    }&forceHideNavigationBar=true`,
    home2: 'openurl-modal://https://<<cmcchkhsh_host>>/activity/myCity/#/newMyHome?lang=<<cmcchkhsh_cmplang>>&hideNavigationBar=true',
    home3: `openhkhshlogin://cmcchkhsh://metaverse?url=myhome`
}
const toHome = () => {
    logEventStatistics('garden_decorate_my_home')
    const version = getAppVersion() //获取版本信息
    const ver = Number(version.split('.').join(''))
    if (ver >= 1000) {
        let link = isPat ? patLink : uatLink
        window.location.href = link['home3']
        return
    } else {
        dialog.get('updateDialog').show({}, { maskClose: false })
        return
    }
}

/** 查看nft权益 */
const showNFTEquity = () => {
    logEventStatistics('garden_nft_benefit')
    isShowNftEquity.value = true
}

/** 布置预览 */
const preview = () => {
    logEventStatistics('garden_review_plants')
    isShowNftPreview.value = true
}

/** 切换tab调用的事件函数 */
const changeTab = (index: number) => {
    tabIndex.value = index
}

/** 回收nft */
const toRecovery = () => {
    logEventStatistics('garden_return_nft')
    emit('close')
    dialog
        .get('recoveryDialog')
        .show({ nft_group_id: props.nft_group_id, nft_upgrade_notice: props.nft_upgrade_notice }, { maskClose: true })
}
</script>

<style lang="less" scoped>
.van-popup {
    overflow-y: unset;
}
.box {
    position: relative;
    width: 570px;
    height: 720px;
    display: flex;
    flex-direction: column;
    > .btn1 {
        position: absolute;
        width: 56px;
        height: 56px;
        background: url('@assets/imgs/closeIcon.png') no-repeat;
        background-size: 100% 100%;
        border: none;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -180px;
    }
    > img {
        position: absolute;
        width: 570px;
        height: 840px;
        top: 0px;
    }

    .stateImg {
        position: absolute;
        width: 84px;
        height: 84px;
        top: 370px;
        left: 380px;
    }
    .ach {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 230px;
    }
    .toptext {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        top: 85px;
        height: 94px;
        width: 80%;
        text-align: center;
        font-size: 36px;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #4e5b7e;
    }
    .tip {
        position: absolute;
        width: 90%;
        left: 50%;
        transform: translate(-50%, 0);
        top: 480px;
        height: 110px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        // background-color: pink;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #4e5b7e;
        white-space: pre-line;
    }
    .goNFT {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 50px;
        width: 362px;
        height: 60px;
        background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
        box-shadow: 0px 2px 0px 2px rgba(252, 175, 40, 1), inset 0px 4px 0px 2px rgba(255, 242, 178, 1);
        border-radius: 48px 12px 48px 12px;
        padding: 5px 20px;
        text-align: center;
        box-sizing: content-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 35px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #fff;
        top: 530px;
    }
    .recycled {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 50px;
        width: 362px;
        height: 60px;
        background: linear-gradient(180deg, #d3d3d3 0%, #848484 100%);
        box-shadow: inset 0px 4px 0px 2px #eaeaea;
        border-radius: 48px 12px 48px 12px;
        padding: 5px 20px;
        text-align: center;
        box-sizing: content-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        top: 530px;
    }

    .plant_detail {
        position: absolute;
        left: 50%;
        top: 650px;
    }

    .nft_tip {
        position: absolute;
        left: 50%;
        top: 530px;
    }

    .nft_equity_tip {
        position: absolute;
        left: 50%;
        top: 650px;
    }

    .goHome {
        position: absolute;
        left: 50%;
        top: 545px;
    }

    .goNFT__finish {
        background: linear-gradient(180deg, #898988 0%, #aea99f 100%);
        box-shadow: 0px 0px 0px 2px #c9c9c9, inset 0px 4px 0px 2px #bfbdbd;
        border-radius: 48px 12px 48px 12px;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 50px;
        min-width: 400px;
        height: 60px;
        padding: 5px 20px;
        text-align: center;
        box-sizing: content-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #fff;
    }

    .plant_tip {
        position: absolute;
        left: 50%;
        top: 530px;
    }

    /** 弹窗按钮 */
    .click_btn {
        border-radius: 48px 12px 48px 12px;
        transform: translate(-50%, 0);
        bottom: 50px;
        width: 356px;
        height: 60px;
        padding: 5px 20px;
        text-align: center;
        box-sizing: content-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #fff;
    }

    /** 激活的按钮 */
    .disactive {
        background: linear-gradient(180deg, #898988 0%, #aea99f 100%);
        box-shadow: 0px 0px 0px 2px #c9c9c9, inset 0px 4px 0px 2px #bfbdbd;
    }

    /** 未激活的按钮 */
    .active {
        background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
        box-shadow: 0px 2px 0px 2px rgba(252, 175, 40, 1), inset 0px 4px 0px 2px rgba(255, 242, 178, 1);
    }

    .limit__content {
        position: absolute;
        right: -14px;
        top: -30px;
        width: 136px;
        height: 68px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: url('@assets/imgs/redBubble.png') no-repeat;
        background-size: cover;
        z-index: 2;
        .limit {
            width: 115px;
            height: 40px;
            text-align: center;
            font-style: normal;
            font-size: 12px;
            line-height: 12px;
            position: absolute;
            color: #fff;
            top: 20px;
            left: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    .btn-box {
        position: absolute;
        bottom: 15px;
        display: flex;
        width: 100%;
        padding: 0 50px;
    }
    .btn {
        height: 40px;
        background: #ffffff;
        text-align: center;
        border-bottom: 1px solid #4eca54;
        box-sizing: content-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 22px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #4eca54;
    }
    .btn__center {
        height: 40px;
        background: #ffffff;
        text-align: center;
        border-bottom: 1px solid #4eca54;
        box-sizing: content-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 22px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #4eca54;
    }
    .btn2 {
        height: 40px;
        background: #ffffff;
        border-bottom: 1px solid #4eca54;
        text-align: center;
        box-sizing: content-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 22px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #4eca54;
    }
    .btn3 {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 50px;
        min-width: 354px;
        height: 60px;
        background: #ffffff;
        border-radius: 48px 12px 48px 12px;
        border: 2px solid #4eca54;
        padding: 5px 20px;
        text-align: center;
        box-sizing: content-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #4eca54;
    }
}

.unLock {
    font-weight: 400;
    font-size: 24px;
    color: #4e5b7e;
    line-height: 28px;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}
</style>
