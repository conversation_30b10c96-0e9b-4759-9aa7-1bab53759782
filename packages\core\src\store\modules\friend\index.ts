import { defineStore } from 'pinia'
import actions from './actions'
import type { friendType } from './type'
export const useFriendStore = defineStore('friendStore', {
    state: () : friendType => ({
       //数据
       isOwn: true,
       friendList: [],
       activityList: [],
       marqueeList: [],
       noticeCount: 0,
       propsConfig: {
            挂饰: [null, null, null], 
            铲子: null, 
            围栏: null,
            灯柱: null,
            稻草人: null,
            木头车: null,
            喷水池: null,
            桌子: null,
            椅子: null,
            GIF: null
        },
        // 主页好友列表五人、六人
        homeFriendList:[],
        flowerFriendList:[]
    }),
    actions
})
