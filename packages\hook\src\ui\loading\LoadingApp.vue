<template>
  <div v-if="state.show" class="lx-load-mark">
      <img src="../../../../core/src/assets/imgs/loading.gif" alt="">
      <div class="reload" v-if="reloadShow" @click="reload"><img src="../../../../core/src/assets/imgs/刷新按钮.jpg" alt="">{{ reloadLang[lang] }}</div>
  </div>
</template>

<script lang="ts" setup>
import { state } from './state'
import { ref,watch } from "vue";
import { useEnvConfig } from '../../core/useEnvConfig'
import { useLang } from '../../core/useLang'
let {lang} = useLang()
let reloadShow = ref(false)
const { BASE } = useEnvConfig()
const img = `${BASE}/loading.gif`
function reload() {
  location.reload()
}
//
let reloadLang = {
  tc:'刷新',
  sc:'刷新',
  en:'Refresh'
}
let timer:any = null
watch(()=>state.show,(n,v)=>{
  if (state.show) {
    timer = setTimeout(() => {
      if (state.show) {
        reloadShow.value = true
      }else{
        clearTimeout(timer)
      }
    }, 10000);
  }else{
    if (timer) {
      clearTimeout(timer)
    }
    reloadShow.value = false
  }
})
</script>

<style lang="less">
#app-loading {
  position: fixed;
  z-index: 5001;
}

.reload{
  display: flex;
  position: fixed;
  bottom: 2rem;
  right: 1rem;
  background-color: #fff;
  padding: 0.25rem 0.5rem;
  border-radius: 0.1rem;
  font-size: 0.4rem;
  color: #6a43d1;
  align-items: center;
  justify-content: space-around;
  img{
    width: 40%;
  }
}
</style>

<style lang="less" scoped>
.lx-load-mark {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999999999;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
  > .loading-img {
    width: 2rem;
  }
}
</style>
