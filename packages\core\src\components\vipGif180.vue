<template>
    <div class="image" @click="runAni">
        <div id="VipGif" :class="`${code} ${isAni ? 'animation' : ''}`" :style="`background-image: url(${$imgs[`props/vip/${code}/sprite.png`]})`"></div>
    </div>
</template>

<script lang="ts" setup>
import { useDialog, useLang } from 'hook';
import { onMounted, ref,onBeforeUnmount } from 'vue'
const { state } = useLang() 
const props = defineProps({
    code: {
        type: String,
        required: false
    }
})

let isAni = ref(false)
let runAniTimer:any = null

onMounted(() => {
    var element = document.getElementById('VipGif')
    element.addEventListener('animationend',function(e){
        isAni.value = false
    })
	runAniTimer = setInterval(()=>{
		runAni()
	},10000)
})

onBeforeUnmount(()=>{
	clearInterval(runAniTimer)
})

const runAni = () => {
    if(isAni.value)
        return
    isAni.value = true
}
</script>

<style lang="less" scoped>
.image{
    width: 240px;
    height: 240px;
    position: relative;
    overflow: hidden;
    transform: translate(-30px, -30px) scale(0.75);
    transform-origin: center center;

	#VipGif{
		.no-app-select();
		width: 100%;
		height: 100%;
		background-repeat: no-repeat;
		background-position: 0px 0px;

		&.hiker{
			background-size: 4800px 1440px;
			&.animation{
				animation: hikerAni 4.80s steps(1) forwards;
			}
		}
		&.arborist{
			background-size: 4800px 1440px;
			&.animation{
				animation: arboristAni 4.76s steps(1) forwards;
			}
		}
		&.magician{
			background-size: 4800px 1440px;
			&.animation{
				animation: magicianAni 4.40s steps(1) forwards;
			}
		}
		&.astronaut{
			background-size: 4800px 1440px;
			&.animation{
				animation: astronautAni 4.68s steps(1) forwards;
			}
		}
		&.musician{
			background-size: 4800px 1680px;
			&.animation{
				animation: musicianAni 5.00s steps(1) forwards;
			}
		}
.sculptor{
    background-size: 4800px 1440px;
   &.animation{
		animation: sculptorAni 4.60s steps(1) forwards;
	}
}
.fairy{
    background-size: 4800px 1440px;
   &.animation{
		animation: fairyAni 4.32s steps(1) forwards;
	}
}
.master{
    background-size: 4800px 1440px;
   &.animation{
		animation: masterAni 4.20s steps(1) forwards;
	}
}
.candymaster{
    background-size: 4800px 1680px;
   	&.animation{
    	animation: candymasterAni 5s steps(1) forwards;
	}
}
.nauticalexplorer{
    background-size: 4800px 1680px;
   	&.animation{
    	animation: nauticalexplorerAni 5s steps(1) forwards;
	}
}
.windchaser {
    background-size: 4800px 1680px;
   	&.animation{
    	animation: windchaserAni 5s steps(1) forwards;
	}
}
.springwalkers {
    background-size: 4800px 1680px;
   	&.animation{
    	animation: springwalkersAni 5s steps(1) forwards;
	}
}

@keyframes hikerAni {
	0% { background-position: 0px 0px; }
	0.84% { background-position: -240px 0px; }
	1.68% { background-position: -480px 0px; }
	2.52% { background-position: -720px 0px; }
	3.36% { background-position: -960px 0px; }
	4.20% { background-position: -1200px 0px; }
	5.04% { background-position: -1440px 0px; }
	5.88% { background-position: -1680px 0px; }
	6.72% { background-position: -1920px 0px; }
	7.56% { background-position: -2160px 0px; }
	8.40% { background-position: -2400px 0px; }
	9.24% { background-position: -2640px 0px; }
	10.08% { background-position: -2880px 0px; }
	10.92% { background-position: -3120px 0px; }
	11.76% { background-position: -3360px 0px; }
	12.61% { background-position: -3600px 0px; }
	13.45% { background-position: -3840px 0px; }
	14.29% { background-position: -4080px 0px; }
	15.13% { background-position: -4320px 0px; }
	15.97% { background-position: -4560px 0px; }
	16.81% { background-position: 0px -240px; }
	17.65% { background-position: -240px -240px; }
	18.49% { background-position: -480px -240px; }
	19.33% { background-position: -720px -240px; }
	20.17% { background-position: -960px -240px; }
	21.01% { background-position: -1200px -240px; }
	21.85% { background-position: -1440px -240px; }
	22.69% { background-position: -1680px -240px; }
	23.53% { background-position: -1920px -240px; }
	24.37% { background-position: -2160px -240px; }
	25.21% { background-position: -2400px -240px; }
	26.05% { background-position: -2640px -240px; }
	26.89% { background-position: -2880px -240px; }
	27.73% { background-position: -3120px -240px; }
	28.57% { background-position: -3360px -240px; }
	29.41% { background-position: -3600px -240px; }
	30.25% { background-position: -3840px -240px; }
	31.09% { background-position: -4080px -240px; }
	31.93% { background-position: -4320px -240px; }
	32.77% { background-position: -4560px -240px; }
	33.61% { background-position: 0px -480px; }
	34.45% { background-position: -240px -480px; }
	35.29% { background-position: -480px -480px; }
	36.13% { background-position: -720px -480px; }
	36.97% { background-position: -960px -480px; }
	37.82% { background-position: -1200px -480px; }
	38.66% { background-position: -1440px -480px; }
	39.50% { background-position: -1680px -480px; }
	40.34% { background-position: -1920px -480px; }
	41.18% { background-position: -2160px -480px; }
	42.02% { background-position: -2400px -480px; }
	42.86% { background-position: -2640px -480px; }
	43.70% { background-position: -2880px -480px; }
	44.54% { background-position: -3120px -480px; }
	45.38% { background-position: -3360px -480px; }
	46.22% { background-position: -3600px -480px; }
	47.06% { background-position: -3840px -480px; }
	47.90% { background-position: -4080px -480px; }
	48.74% { background-position: -4320px -480px; }
	49.58% { background-position: -4560px -480px; }
	50.42% { background-position: 0px -720px; }
	51.26% { background-position: -240px -720px; }
	52.10% { background-position: -480px -720px; }
	52.94% { background-position: -720px -720px; }
	53.78% { background-position: -960px -720px; }
	54.62% { background-position: -1200px -720px; }
	55.46% { background-position: -1440px -720px; }
	56.30% { background-position: -1680px -720px; }
	57.14% { background-position: -1920px -720px; }
	57.98% { background-position: -2160px -720px; }
	58.82% { background-position: -2400px -720px; }
	59.66% { background-position: -2640px -720px; }
	60.50% { background-position: -2880px -720px; }
	61.34% { background-position: -3120px -720px; }
	62.18% { background-position: -3360px -720px; }
	63.03% { background-position: -3600px -720px; }
	63.87% { background-position: -3840px -720px; }
	64.71% { background-position: -4080px -720px; }
	65.55% { background-position: -4320px -720px; }
	66.39% { background-position: -4560px -720px; }
	67.23% { background-position: 0px 0px; }
	68.07% { background-position: -240px -960px; }
	68.91% { background-position: -480px -960px; }
	69.75% { background-position: -720px -960px; }
	70.59% { background-position: -960px -960px; }
	71.43% { background-position: -1200px -960px; }
	72.27% { background-position: -1440px -960px; }
	73.11% { background-position: -1680px -960px; }
	73.95% { background-position: -1920px -960px; }
	74.79% { background-position: -2160px -960px; }
	75.63% { background-position: -2400px -960px; }
	76.47% { background-position: -2640px -960px; }
	77.31% { background-position: -2880px -960px; }
	78.15% { background-position: -3120px -960px; }
	78.99% { background-position: -3360px -960px; }
	79.83% { background-position: -3600px -960px; }
	80.67% { background-position: -3840px -960px; }
	81.51% { background-position: -4080px -960px; }
	82.35% { background-position: -4320px -960px; }
	83.19% { background-position: -4560px -960px; }
	84.03% { background-position: 0px -1200px; }
	84.87% { background-position: -240px -1200px; }
	85.71% { background-position: -480px -1200px; }
	86.55% { background-position: -720px -1200px; }
	87.39% { background-position: -960px -1200px; }
	88.24% { background-position: -1200px -1200px; }
	89.08% { background-position: -1440px -1200px; }
	89.92% { background-position: -1680px -1200px; }
	90.76% { background-position: -1920px -1200px; }
	91.60% { background-position: -2160px -1200px; }
	92.44% { background-position: -2400px -1200px; }
	93.28% { background-position: -2640px -1200px; }
	94.12% { background-position: -2880px -1200px; }
	94.96% { background-position: -3120px -1200px; }
	95.80% { background-position: -3360px -1200px; }
	96.64% { background-position: -3600px -1200px; }
	97.48% { background-position: -3840px -1200px; }
	98.32% { background-position: -4080px -1200px; }
	99.16% { background-position: -4320px -1200px; }
	100.00% { background-position: -4560px -1200px; }
}
@keyframes arboristAni {
    0% { background-position: 0px 0px; }
	0.85% { background-position: -240px 0px; }
	1.69% { background-position: -480px 0px; }
	2.54% { background-position: -720px 0px; }
	3.39% { background-position: -960px 0px; }
	4.24% { background-position: -1200px 0px; }
	5.08% { background-position: -1440px 0px; }
	5.93% { background-position: -1680px 0px; }
	6.78% { background-position: -1920px 0px; }
	7.63% { background-position: -2160px 0px; }
	8.47% { background-position: -2400px 0px; }
	9.32% { background-position: -2640px 0px; }
	10.17% { background-position: -2880px 0px; }
	11.02% { background-position: -3120px 0px; }
	11.86% { background-position: -3360px 0px; }
	12.71% { background-position: -3600px 0px; }
	13.56% { background-position: -3840px 0px; }
	14.41% { background-position: -4080px 0px; }
	15.25% { background-position: -4320px 0px; }
	16.10% { background-position: -4560px 0px; }
	16.95% { background-position: 0px -240px; }
	17.80% { background-position: -240px -240px; }
	18.64% { background-position: -480px -240px; }
	19.49% { background-position: -720px -240px; }
	20.34% { background-position: -960px -240px; }
	21.19% { background-position: -1200px -240px; }
	22.03% { background-position: -1440px -240px; }
	22.88% { background-position: -1680px -240px; }
	23.73% { background-position: -1920px -240px; }
	24.58% { background-position: -2160px -240px; }
	25.42% { background-position: -2400px -240px; }
	26.27% { background-position: -2640px -240px; }
	27.12% { background-position: -2880px -240px; }
	27.97% { background-position: -3120px -240px; }
	28.81% { background-position: -3360px -240px; }
	29.66% { background-position: -3600px -240px; }
	30.51% { background-position: -3840px -240px; }
	31.36% { background-position: -4080px -240px; }
	32.20% { background-position: -4320px -240px; }
	33.05% { background-position: -4560px -240px; }
	33.90% { background-position: 0px -480px; }
	34.75% { background-position: -240px -480px; }
	35.59% { background-position: -480px -480px; }
	36.44% { background-position: -720px -480px; }
	37.29% { background-position: -960px -480px; }
	38.14% { background-position: -1200px -480px; }
	38.98% { background-position: -1440px -480px; }
	39.83% { background-position: -1680px -480px; }
	40.68% { background-position: -1920px -480px; }
	41.53% { background-position: -2160px -480px; }
	42.37% { background-position: -2400px -480px; }
	43.22% { background-position: -2640px -480px; }
	44.07% { background-position: -2880px -480px; }
	44.92% { background-position: -3120px -480px; }
	45.76% { background-position: -3360px -480px; }
	46.61% { background-position: -3600px -480px; }
	47.46% { background-position: -3840px -480px; }
	48.31% { background-position: -4080px -480px; }
	49.15% { background-position: -4320px -480px; }
	50.00% { background-position: -4560px -480px; }
	50.85% { background-position: 0px -720px; }
	51.69% { background-position: -240px -720px; }
	52.54% { background-position: -480px -720px; }
	53.39% { background-position: -720px -720px; }
	54.24% { background-position: -960px -720px; }
	55.08% { background-position: -1200px -720px; }
	55.93% { background-position: -1440px -720px; }
	56.78% { background-position: -1680px -720px; }
	57.63% { background-position: -1920px -720px; }
	58.47% { background-position: -2160px -720px; }
	59.32% { background-position: -2400px -720px; }
	60.17% { background-position: -2640px -720px; }
	61.02% { background-position: -2880px -720px; }
	61.86% { background-position: -3120px -720px; }
	62.71% { background-position: -3360px -720px; }
	63.56% { background-position: -3600px -720px; }
	64.41% { background-position: -3840px -720px; }
	65.25% { background-position: -4080px -720px; }
	66.10% { background-position: -4320px -720px; }
	66.95% { background-position: -4560px -720px; }
	67.80% { background-position: 0px -960px; }
	68.64% { background-position: -240px -960px; }
	69.49% { background-position: -480px -960px; }
	70.34% { background-position: -720px -960px; }
	71.19% { background-position: -960px -960px; }
	72.03% { background-position: -1200px -960px; }
	72.88% { background-position: -1440px -960px; }
	73.73% { background-position: -1680px -960px; }
	74.58% { background-position: -1920px -960px; }
	75.42% { background-position: -2160px -960px; }
	76.27% { background-position: -2400px -960px; }
	77.12% { background-position: -2640px -960px; }
	77.97% { background-position: -2880px -960px; }
	78.81% { background-position: -3120px -960px; }
	79.66% { background-position: -3360px -960px; }
	80.51% { background-position: -3600px -960px; }
	81.36% { background-position: -3840px -960px; }
	82.20% { background-position: -4080px -960px; }
	83.05% { background-position: -4320px -960px; }
	83.90% { background-position: -4560px -960px; }
	84.75% { background-position: 0px -1200px; }
	85.59% { background-position: -240px -1200px; }
	86.44% { background-position: -480px -1200px; }
	87.29% { background-position: -720px -1200px; }
	88.14% { background-position: -960px -1200px; }
	88.98% { background-position: -1200px -1200px; }
	89.83% { background-position: -1440px -1200px; }
	90.68% { background-position: -1680px -1200px; }
	91.53% { background-position: -1920px -1200px; }
	92.37% { background-position: -2160px -1200px; }
	93.22% { background-position: -2400px -1200px; }
	94.07% { background-position: -2640px -1200px; }
	94.92% { background-position: -2880px -1200px; }
	95.76% { background-position: -3120px -1200px; }
	96.61% { background-position: -3360px -1200px; }
	97.46% { background-position: -3600px -1200px; }
	98.31% { background-position: -3840px -1200px; }
	99.15% { background-position: -4080px -1200px; }
	100.00% { background-position: -4320px -1200px; }
}
@keyframes magicianAni {
    0% { background-position: 0px 0px; }
	0.91% { background-position: -240px 0px; }
	1.82% { background-position: -480px 0px; }
	2.73% { background-position: -720px 0px; }
	3.64% { background-position: -960px 0px; }
	4.55% { background-position: -1200px 0px; }
	5.45% { background-position: -1440px 0px; }
	6.36% { background-position: -1680px 0px; }
	7.27% { background-position: -1920px 0px; }
	8.18% { background-position: -2160px 0px; }
	9.09% { background-position: -2400px 0px; }
	10.00% { background-position: -2640px 0px; }
	10.91% { background-position: -2880px 0px; }
	11.82% { background-position: -3120px 0px; }
	12.73% { background-position: -3360px 0px; }
	13.64% { background-position: -3600px 0px; }
	14.55% { background-position: -3840px 0px; }
	15.45% { background-position: -4080px 0px; }
	16.36% { background-position: -4320px 0px; }
	17.27% { background-position: -4560px 0px; }
	18.18% { background-position: 0px -240px; }
	19.09% { background-position: -240px -240px; }
	20.00% { background-position: -480px -240px; }
	20.91% { background-position: -720px -240px; }
	21.82% { background-position: -960px -240px; }
	22.73% { background-position: -1200px -240px; }
	23.64% { background-position: -1440px -240px; }
	24.55% { background-position: -1680px -240px; }
	25.45% { background-position: -1920px -240px; }
	26.36% { background-position: -2160px -240px; }
	27.27% { background-position: -2400px -240px; }
	28.18% { background-position: -2640px -240px; }
	29.09% { background-position: -2880px -240px; }
	30.00% { background-position: -3120px -240px; }
	30.91% { background-position: -3360px -240px; }
	31.82% { background-position: -3600px -240px; }
	32.73% { background-position: -3840px -240px; }
	33.64% { background-position: -4080px -240px; }
	34.55% { background-position: -4320px -240px; }
	35.45% { background-position: -4560px -240px; }
	36.36% { background-position: 0px -480px; }
	37.27% { background-position: -240px -480px; }
	38.18% { background-position: -480px -480px; }
	39.09% { background-position: -720px -480px; }
	40.00% { background-position: -960px -480px; }
	40.91% { background-position: -1200px -480px; }
	41.82% { background-position: -1440px -480px; }
	42.73% { background-position: -1680px -480px; }
	43.64% { background-position: -1920px -480px; }
	44.55% { background-position: -2160px -480px; }
	45.45% { background-position: -2400px -480px; }
	46.36% { background-position: -2640px -480px; }
	47.27% { background-position: -2880px -480px; }
	48.18% { background-position: -3120px -480px; }
	49.09% { background-position: -3360px -480px; }
	50.00% { background-position: -3600px -480px; }
	50.91% { background-position: -3840px -480px; }
	51.82% { background-position: -4080px -480px; }
	52.73% { background-position: -4320px -480px; }
	53.64% { background-position: -4560px -480px; }
	54.55% { background-position: 0px -720px; }
	55.45% { background-position: -240px -720px; }
	56.36% { background-position: -480px -720px; }
	57.27% { background-position: -720px -720px; }
	58.18% { background-position: -960px -720px; }
	59.09% { background-position: -1200px -720px; }
	60.00% { background-position: -1440px -720px; }
	60.91% { background-position: -1680px -720px; }
	61.82% { background-position: -1920px -720px; }
	62.73% { background-position: -2160px -720px; }
	63.64% { background-position: -2400px -720px; }
	64.55% { background-position: -2640px -720px; }
	65.45% { background-position: -2880px -720px; }
	66.36% { background-position: -3120px -720px; }
	67.27% { background-position: -3360px -720px; }
	68.18% { background-position: -3600px -720px; }
	69.09% { background-position: -3840px -720px; }
	70.00% { background-position: -4080px -720px; }
	70.91% { background-position: -4320px -720px; }
	71.82% { background-position: -4560px -720px; }
	72.73% { background-position: 0px -960px; }
	73.64% { background-position: -240px -960px; }
	74.55% { background-position: -480px -960px; }
	75.45% { background-position: -720px -960px; }
	76.36% { background-position: -960px -960px; }
	77.27% { background-position: -1200px -960px; }
	78.18% { background-position: -1440px -960px; }
	79.09% { background-position: -1680px -960px; }
	80.00% { background-position: -1920px -960px; }
	80.91% { background-position: -2160px -960px; }
	81.82% { background-position: -2400px -960px; }
	82.73% { background-position: -2640px -960px; }
	83.64% { background-position: -2880px -960px; }
	84.55% { background-position: -3120px -960px; }
	85.45% { background-position: -3360px -960px; }
	86.36% { background-position: -3600px -960px; }
	87.27% { background-position: -3840px -960px; }
	88.18% { background-position: -4080px -960px; }
	89.09% { background-position: -4320px -960px; }
	90.00% { background-position: -4560px -960px; }
	90.91% { background-position: 0px -1200px; }
	91.82% { background-position: -240px -1200px; }
	92.73% { background-position: -480px -1200px; }
	93.64% { background-position: -720px -1200px; }
	94.55% { background-position: -960px -1200px; }
	95.45% { background-position: -1200px -1200px; }
	96.36% { background-position: -1440px -1200px; }
	97.27% { background-position: -1680px -1200px; }
	98.18% { background-position: -1920px -1200px; }
	99.09% { background-position: -2160px -1200px; }
	100.00% { background-position: -2400px -1200px; }
}
@keyframes astronautAni {
    0% { background-position: 0px 0px; }
	0.86% { background-position: -240px 0px; }
	1.72% { background-position: -480px 0px; }
	2.59% { background-position: -720px 0px; }
	3.45% { background-position: -960px 0px; }
	4.31% { background-position: -1200px 0px; }
	5.17% { background-position: -1440px 0px; }
	6.03% { background-position: -1680px 0px; }
	6.90% { background-position: -1920px 0px; }
	7.76% { background-position: -2160px 0px; }
	8.62% { background-position: -2400px 0px; }
	9.48% { background-position: -2640px 0px; }
	10.34% { background-position: -2880px 0px; }
	11.21% { background-position: -3120px 0px; }
	12.07% { background-position: -3360px 0px; }
	12.93% { background-position: -3600px 0px; }
	13.79% { background-position: -3840px 0px; }
	14.66% { background-position: -4080px 0px; }
	15.52% { background-position: -4320px 0px; }
	16.38% { background-position: -4560px 0px; }
	17.24% { background-position: 0px -240px; }
	18.10% { background-position: -240px -240px; }
	18.97% { background-position: -480px -240px; }
	19.83% { background-position: -720px -240px; }
	20.69% { background-position: -960px -240px; }
	21.55% { background-position: -1200px -240px; }
	22.41% { background-position: -1440px -240px; }
	23.28% { background-position: -1680px -240px; }
	24.14% { background-position: -1920px -240px; }
	25.00% { background-position: -2160px -240px; }
	25.86% { background-position: -2400px -240px; }
	26.72% { background-position: -2640px -240px; }
	27.59% { background-position: -2880px -240px; }
	28.45% { background-position: -3120px -240px; }
	29.31% { background-position: -3360px -240px; }
	30.17% { background-position: -3600px -240px; }
	31.03% { background-position: -3840px -240px; }
	31.90% { background-position: -4080px -240px; }
	32.76% { background-position: -4320px -240px; }
	33.62% { background-position: -4560px -240px; }
	34.48% { background-position: 0px -480px; }
	35.34% { background-position: -240px -480px; }
	36.21% { background-position: -480px -480px; }
	37.07% { background-position: -720px -480px; }
	37.93% { background-position: -960px -480px; }
	38.79% { background-position: -1200px -480px; }
	39.66% { background-position: -1440px -480px; }
	40.52% { background-position: -1680px -480px; }
	41.38% { background-position: -1920px -480px; }
	42.24% { background-position: -2160px -480px; }
	43.10% { background-position: -2400px -480px; }
	43.97% { background-position: -2640px -480px; }
	44.83% { background-position: -2880px -480px; }
	45.69% { background-position: -3120px -480px; }
	46.55% { background-position: -3360px -480px; }
	47.41% { background-position: -3600px -480px; }
	48.28% { background-position: -3840px -480px; }
	49.14% { background-position: -4080px -480px; }
	50.00% { background-position: -4320px -480px; }
	50.86% { background-position: -4560px -480px; }
	51.72% { background-position: 0px -720px; }
	52.59% { background-position: -240px -720px; }
	53.45% { background-position: -480px -720px; }
	54.31% { background-position: -720px -720px; }
	55.17% { background-position: -960px -720px; }
	56.03% { background-position: -1200px -720px; }
	56.90% { background-position: -1440px -720px; }
	57.76% { background-position: -1680px -720px; }
	58.62% { background-position: -1920px -720px; }
	59.48% { background-position: -2160px -720px; }
	60.34% { background-position: -2400px -720px; }
	61.21% { background-position: -2640px -720px; }
	62.07% { background-position: -2880px -720px; }
	62.93% { background-position: -3120px -720px; }
	63.79% { background-position: -3360px -720px; }
	64.66% { background-position: -3600px -720px; }
	65.52% { background-position: -3840px -720px; }
	66.38% { background-position: -4080px -720px; }
	67.24% { background-position: -4320px -720px; }
	68.10% { background-position: -4560px -720px; }
	68.97% { background-position: 0px -960px; }
	69.83% { background-position: -240px -960px; }
	70.69% { background-position: -480px -960px; }
	71.55% { background-position: -720px -960px; }
	72.41% { background-position: -960px -960px; }
	73.28% { background-position: -1200px -960px; }
	74.14% { background-position: -1440px -960px; }
	75.00% { background-position: -1680px -960px; }
	75.86% { background-position: -1920px -960px; }
	76.72% { background-position: -2160px -960px; }
	77.59% { background-position: -2400px -960px; }
	78.45% { background-position: -2640px -960px; }
	79.31% { background-position: -2880px -960px; }
	80.17% { background-position: -3120px -960px; }
	81.03% { background-position: -3360px -960px; }
	81.90% { background-position: -3600px -960px; }
	82.76% { background-position: -3840px -960px; }
	83.62% { background-position: -4080px -960px; }
	84.48% { background-position: -4320px -960px; }
	85.34% { background-position: -4560px -960px; }
	86.21% { background-position: 0px -1200px; }
	87.07% { background-position: -240px -1200px; }
	87.93% { background-position: -480px -1200px; }
	88.79% { background-position: -720px -1200px; }
	89.66% { background-position: -960px -1200px; }
	90.52% { background-position: -1200px -1200px; }
	91.38% { background-position: -1440px -1200px; }
	92.24% { background-position: -1680px -1200px; }
	93.10% { background-position: -1920px -1200px; }
	93.97% { background-position: -2160px -1200px; }
	94.83% { background-position: -2400px -1200px; }
	95.69% { background-position: -2640px -1200px; }
	96.55% { background-position: -2880px -1200px; }
	97.41% { background-position: -3120px -1200px; }
	98.28% { background-position: -3360px -1200px; }
	99.14% { background-position: -3600px -1200px; }
	100.00% { background-position: -3840px -1200px; }
}
@keyframes musicianAni {
    0% { background-position: 0px 0px; }
	0.81% { background-position: -240px 0px; }
	1.61% { background-position: -480px 0px; }
	2.42% { background-position: -720px 0px; }
	3.23% { background-position: -960px 0px; }
	4.03% { background-position: -1200px 0px; }
	4.84% { background-position: -1440px 0px; }
	5.65% { background-position: -1680px 0px; }
	6.45% { background-position: -1920px 0px; }
	7.26% { background-position: -2160px 0px; }
	8.06% { background-position: -2400px 0px; }
	8.87% { background-position: -2640px 0px; }
	9.68% { background-position: -2880px 0px; }
	10.48% { background-position: -3120px 0px; }
	11.29% { background-position: -3360px 0px; }
	12.10% { background-position: -3600px 0px; }
	12.90% { background-position: -3840px 0px; }
	13.71% { background-position: -4080px 0px; }
	14.52% { background-position: -4320px 0px; }
	15.32% { background-position: -4560px 0px; }
	16.13% { background-position: 0px -240px; }
	16.94% { background-position: -240px -240px; }
	17.74% { background-position: -480px -240px; }
	18.55% { background-position: -720px -240px; }
	19.35% { background-position: -960px -240px; }
	20.16% { background-position: -1200px -240px; }
	20.97% { background-position: -1440px -240px; }
	21.77% { background-position: -1680px -240px; }
	22.58% { background-position: -1920px -240px; }
	23.39% { background-position: -2160px -240px; }
	24.19% { background-position: -2400px -240px; }
	25.00% { background-position: -2640px -240px; }
	25.81% { background-position: -2880px -240px; }
	26.61% { background-position: -3120px -240px; }
	27.42% { background-position: -3360px -240px; }
	28.23% { background-position: -3600px -240px; }
	29.03% { background-position: -3840px -240px; }
	29.84% { background-position: -4080px -240px; }
	30.65% { background-position: -4320px -240px; }
	31.45% { background-position: -4560px -240px; }
	32.26% { background-position: 0px -480px; }
	33.06% { background-position: -240px -480px; }
	33.87% { background-position: -480px -480px; }
	34.68% { background-position: -720px -480px; }
	35.48% { background-position: -960px -480px; }
	36.29% { background-position: -1200px -480px; }
	37.10% { background-position: -1440px -480px; }
	37.90% { background-position: -1680px -480px; }
	38.71% { background-position: -1920px -480px; }
	39.52% { background-position: -2160px -480px; }
	40.32% { background-position: -2400px -480px; }
	41.13% { background-position: -2640px -480px; }
	41.94% { background-position: -2880px -480px; }
	42.74% { background-position: -3120px -480px; }
	43.55% { background-position: -3360px -480px; }
	44.35% { background-position: -3600px -480px; }
	45.16% { background-position: -3840px -480px; }
	45.97% { background-position: -4080px -480px; }
	46.77% { background-position: -4320px -480px; }
	47.58% { background-position: -4560px -480px; }
	48.39% { background-position: 0px -720px; }
	49.19% { background-position: -240px -720px; }
	50.00% { background-position: -480px -720px; }
	50.81% { background-position: -720px -720px; }
	51.61% { background-position: -960px -720px; }
	52.42% { background-position: -1200px -720px; }
	53.23% { background-position: -1440px -720px; }
	54.03% { background-position: -1680px -720px; }
	54.84% { background-position: -1920px -720px; }
	55.65% { background-position: -2160px -720px; }
	56.45% { background-position: -2400px -720px; }
	57.26% { background-position: -2640px -720px; }
	58.06% { background-position: -2880px -720px; }
	58.87% { background-position: -3120px -720px; }
	59.68% { background-position: -3360px -720px; }
	60.48% { background-position: -3600px -720px; }
	61.29% { background-position: -3840px -720px; }
	62.10% { background-position: -4080px -720px; }
	62.90% { background-position: -4320px -720px; }
	63.71% { background-position: -4560px -720px; }
	64.52% { background-position: 0px -960px; }
	65.32% { background-position: -240px -960px; }
	66.13% { background-position: -480px -960px; }
	66.94% { background-position: -720px -960px; }
	67.74% { background-position: -960px -960px; }
	68.55% { background-position: -1200px -960px; }
	69.35% { background-position: -1440px -960px; }
	70.16% { background-position: -1680px -960px; }
	70.97% { background-position: -1920px -960px; }
	71.77% { background-position: -2160px -960px; }
	72.58% { background-position: -2400px -960px; }
	73.39% { background-position: -2640px -960px; }
	74.19% { background-position: -2880px -960px; }
	75.00% { background-position: -3120px -960px; }
	75.81% { background-position: -3360px -960px; }
	76.61% { background-position: -3600px -960px; }
	77.42% { background-position: -3840px -960px; }
	78.23% { background-position: -4080px -960px; }
	79.03% { background-position: -4320px -960px; }
	79.84% { background-position: -4560px -960px; }
	80.65% { background-position: 0px -1200px; }
	81.45% { background-position: -240px -1200px; }
	82.26% { background-position: -480px -1200px; }
	83.06% { background-position: -720px -1200px; }
	83.87% { background-position: -960px -1200px; }
	84.68% { background-position: -1200px -1200px; }
	85.48% { background-position: -1440px -1200px; }
	86.29% { background-position: -1680px -1200px; }
	87.10% { background-position: -1920px -1200px; }
	87.90% { background-position: -2160px -1200px; }
	88.71% { background-position: -2400px -1200px; }
	89.52% { background-position: -2640px -1200px; }
	90.32% { background-position: -2880px -1200px; }
	91.13% { background-position: -3120px -1200px; }
	91.94% { background-position: -3360px -1200px; }
	92.74% { background-position: -3600px -1200px; }
	93.55% { background-position: -3840px -1200px; }
	94.35% { background-position: -4080px -1200px; }
	95.16% { background-position: -4320px -1200px; }
	95.97% { background-position: -4560px -1200px; }
	96.77% { background-position: 0px -1440px; }
	97.58% { background-position: -240px -1440px; }
	98.39% { background-position: -480px -1440px; }
	99.19% { background-position: -720px -1440px; }
	100.00% { background-position: -960px -1440px; }
}
@keyframes sculptorAni {
	0% { background-position: 0px 0px; }
	0.88% { background-position: -240px 0px; }
	1.75% { background-position: -480px 0px; }
	2.63% { background-position: -720px 0px; }
	3.51% { background-position: -960px 0px; }
	4.39% { background-position: -1200px 0px; }
	5.26% { background-position: -1440px 0px; }
	6.14% { background-position: -1680px 0px; }
	7.02% { background-position: -1920px 0px; }
	7.89% { background-position: -2160px 0px; }
	8.77% { background-position: -2400px 0px; }
	9.65% { background-position: -2640px 0px; }
	10.53% { background-position: -2880px 0px; }
	11.40% { background-position: -3120px 0px; }
	12.28% { background-position: -3360px 0px; }
	13.16% { background-position: -3600px 0px; }
	14.04% { background-position: -3840px 0px; }
	14.91% { background-position: -4080px 0px; }
	15.79% { background-position: -4320px 0px; }
	16.67% { background-position: -4560px 0px; }
	17.54% { background-position: 0px -240px; }
	18.42% { background-position: -240px -240px; }
	19.30% { background-position: -480px -240px; }
	20.18% { background-position: -720px -240px; }
	21.05% { background-position: -960px -240px; }
	21.93% { background-position: -1200px -240px; }
	22.81% { background-position: -1440px -240px; }
	23.68% { background-position: -1680px -240px; }
	24.56% { background-position: -1920px -240px; }
	25.44% { background-position: -2160px -240px; }
	26.32% { background-position: -2400px -240px; }
	27.19% { background-position: -2640px -240px; }
	28.07% { background-position: -2880px -240px; }
	28.95% { background-position: -3120px -240px; }
	29.82% { background-position: -3360px -240px; }
	30.70% { background-position: -3600px -240px; }
	31.58% { background-position: -3840px -240px; }
	32.46% { background-position: -4080px -240px; }
	33.33% { background-position: -4320px -240px; }
	34.21% { background-position: -4560px -240px; }
	35.09% { background-position: 0px -480px; }
	35.96% { background-position: -240px -480px; }
	36.84% { background-position: -480px -480px; }
	37.72% { background-position: -720px -480px; }
	38.60% { background-position: -960px -480px; }
	39.47% { background-position: -1200px -480px; }
	40.35% { background-position: -1440px -480px; }
	41.23% { background-position: -1680px -480px; }
	42.11% { background-position: -1920px -480px; }
	42.98% { background-position: -2160px -480px; }
	43.86% { background-position: -2400px -480px; }
	44.74% { background-position: -2640px -480px; }
	45.61% { background-position: -2880px -480px; }
	46.49% { background-position: -3120px -480px; }
	47.37% { background-position: -3360px -480px; }
	48.25% { background-position: -3600px -480px; }
	49.12% { background-position: -3840px -480px; }
	50.00% { background-position: -4080px -480px; }
	50.88% { background-position: -4320px -480px; }
	51.75% { background-position: -4560px -480px; }
	52.63% { background-position: 0px -720px; }
	53.51% { background-position: -240px -720px; }
	54.39% { background-position: -480px -720px; }
	55.26% { background-position: -720px -720px; }
	56.14% { background-position: -960px -720px; }
	57.02% { background-position: -1200px -720px; }
	57.89% { background-position: -1440px -720px; }
	58.77% { background-position: -1680px -720px; }
	59.65% { background-position: -1920px -720px; }
	60.53% { background-position: -2160px -720px; }
	61.40% { background-position: -2400px -720px; }
	62.28% { background-position: -2640px -720px; }
	63.16% { background-position: -2880px -720px; }
	64.04% { background-position: -3120px -720px; }
	64.91% { background-position: -3360px -720px; }
	65.79% { background-position: -3600px -720px; }
	66.67% { background-position: -3840px -720px; }
	67.54% { background-position: -4080px -720px; }
	68.42% { background-position: -4320px -720px; }
	69.30% { background-position: -4560px -720px; }
	70.18% { background-position: 0px -960px; }
	71.05% { background-position: -240px -960px; }
	71.93% { background-position: -480px -960px; }
	72.81% { background-position: -720px -960px; }
	73.68% { background-position: -960px -960px; }
	74.56% { background-position: -1200px -960px; }
	75.44% { background-position: -1440px -960px; }
	76.32% { background-position: -1680px -960px; }
	77.19% { background-position: -1920px -960px; }
	78.07% { background-position: -2160px -960px; }
	78.95% { background-position: -2400px -960px; }
	79.82% { background-position: -2640px -960px; }
	80.70% { background-position: -2880px -960px; }
	81.58% { background-position: -3120px -960px; }
	82.46% { background-position: -3360px -960px; }
	83.33% { background-position: -3600px -960px; }
	84.21% { background-position: -3840px -960px; }
	85.09% { background-position: -4080px -960px; }
	85.96% { background-position: -4320px -960px; }
	86.84% { background-position: -4560px -960px; }
	87.72% { background-position: 0px -1200px; }
	88.60% { background-position: -240px -1200px; }
	89.47% { background-position: -480px -1200px; }
	90.35% { background-position: -720px -1200px; }
	91.23% { background-position: -960px -1200px; }
	92.11% { background-position: -1200px -1200px; }
	92.98% { background-position: -1440px -1200px; }
	93.86% { background-position: -1680px -1200px; }
	94.74% { background-position: -1920px -1200px; }
	95.61% { background-position: -2160px -1200px; }
	96.49% { background-position: -2400px -1200px; }
	97.37% { background-position: -2640px -1200px; }
	98.25% { background-position: -2880px -1200px; }
	99.12% { background-position: -3120px -1200px; }
	100.00% { background-position: -3360px -1200px; }
}
@keyframes fairyAni {
	0% { background-position: 0 0px; };
	0.93% { background-position: -240px 0px; }
	1.87% { background-position: -480px 0px; }
	2.80% { background-position: -720px 0px; }
	3.74% { background-position: -960px 0px; }
	4.67% { background-position: -1200px 0px; }
	5.61% { background-position: -1440px 0px; }
	6.54% { background-position: -1680px 0px; }
	7.48% { background-position: -1920px 0px; }
	8.41% { background-position: -2160px 0px; }
	9.35% { background-position: -2400px 0px; }
	10.28% { background-position: -2640px 0px; }
	11.21% { background-position: -2880px 0px; }
	12.15% { background-position: -3120px 0px; }
	13.08% { background-position: -3360px 0px; }
	14.02% { background-position: -3600px 0px; }
	14.95% { background-position: -3840px 0px; }
	15.89% { background-position: -4080px 0px; }
	16.82% { background-position: -4320px 0px; }
	17.76% { background-position: -4560px 0px; }
	18.69% { background-position: 0px -240px; }
	19.63% { background-position: -240px -240px; }
	20.56% { background-position: -480px -240px; }
	21.50% { background-position: -720px -240px; }
	22.43% { background-position: -960px -240px; }
	23.36% { background-position: -1200px -240px; }
	24.30% { background-position: -1440px -240px; }
	25.23% { background-position: -1680px -240px; }
	26.17% { background-position: -1920px -240px; }
	27.10% { background-position: -2160px -240px; }
	28.04% { background-position: -2400px -240px; }
	28.97% { background-position: -2640px -240px; }
	29.91% { background-position: -2880px -240px; }
	30.84% { background-position: -3120px -240px; }
	31.78% { background-position: -3360px -240px; }
	32.71% { background-position: -3600px -240px; }
	33.64% { background-position: -3840px -240px; }
	34.58% { background-position: -4080px -240px; }
	35.51% { background-position: -4320px -240px; }
	36.45% { background-position: -4560px -240px; }
	37.38% { background-position: 0px -480px; }
	38.32% { background-position: -240px -480px; }
	39.25% { background-position: -480px -480px; }
	40.19% { background-position: -720px -480px; }
	41.12% { background-position: -960px -480px; }
	42.06% { background-position: -1200px -480px; }
	42.99% { background-position: -1440px -480px; }
	43.93% { background-position: -1680px -480px; }
	44.86% { background-position: -1920px -480px; }
	45.79% { background-position: -2160px -480px; }
	46.73% { background-position: -2400px -480px; }
	47.66% { background-position: -2640px -480px; }
	48.60% { background-position: -2880px -480px; }
	49.53% { background-position: -3120px -480px; }
	50.47% { background-position: -3360px -480px; }
	51.40% { background-position: -3600px -480px; }
	52.34% { background-position: -3840px -480px; }
	53.27% { background-position: -4080px -480px; }
	54.21% { background-position: -4320px -480px; }
	55.14% { background-position: -4560px -480px; }
	56.07% { background-position: 0px -720px; }
	57.01% { background-position: -240px -720px; }
	57.94% { background-position: -480px -720px; }
	58.88% { background-position: -720px -720px; }
	59.81% { background-position: -960px -720px; }
	60.75% { background-position: -1200px -720px; }
	61.68% { background-position: -1440px -720px; }
	62.62% { background-position: -1680px -720px; }
	63.55% { background-position: -1920px -720px; }
	64.49% { background-position: -2160px -720px; }
	65.42% { background-position: -2400px -720px; }
	66.36% { background-position: -2640px -720px; }
	67.29% { background-position: -2880px -720px; }
	68.22% { background-position: -3120px -720px; }
	69.16% { background-position: -3360px -720px; }
	70.09% { background-position: -3600px -720px; }
	71.03% { background-position: -3840px -720px; }
	71.96% { background-position: -4080px -720px; }
	72.90% { background-position: -4320px -720px; }
	73.83% { background-position: -4560px -720px; }
	74.77% { background-position: 0px -960px; }
	75.70% { background-position: -240px -960px; }
	76.64% { background-position: -480px -960px; }
	77.57% { background-position: -720px -960px; }
	78.50% { background-position: -960px -960px; }
	79.44% { background-position: -1200px -960px; }
	80.37% { background-position: -1440px -960px; }
	81.31% { background-position: -1680px -960px; }
	82.24% { background-position: -1920px -960px; }
	83.18% { background-position: -2160px -960px; }
	84.11% { background-position: -2400px -960px; }
	85.05% { background-position: -2640px -960px; }
	85.98% { background-position: -2880px -960px; }
	86.92% { background-position: -3120px -960px; }
	87.85% { background-position: -3360px -960px; }
	88.79% { background-position: -3600px -960px; }
	89.72% { background-position: -3840px -960px; }
	90.65% { background-position: -4080px -960px; }
	91.59% { background-position: -4320px -960px; }
	92.52% { background-position: -4560px -960px; }
	93.46% { background-position: 0px -1200px; }
	94.39% { background-position: -240px -1200px; }
	95.33% { background-position: -480px -1200px; }
	96.26% { background-position: -720px -1200px; }
	97.20% { background-position: -960px -1200px; }
	98.13% { background-position: -1200px -1200px; }
	99.07% { background-position: -1440px -1200px; }
	100.00% { background-position: -1680px -1200px; }
}
@keyframes masterAni {
	0% { background-position: 0px 0px; };
	0.96% { background-position: -240px 0px; }
	1.92% { background-position: -480px 0px; }
	2.88% { background-position: -720px 0px; }
	3.85% { background-position: -960px 0px; }
	4.81% { background-position: -1200px 0px; }
	5.77% { background-position: -1440px 0px; }
	6.73% { background-position: -1680px 0px; }
	7.69% { background-position: -1920px 0px; }
	8.65% { background-position: -2160px 0px; }
	9.62% { background-position: -2400px 0px; }
	10.58% { background-position: -2640px 0px; }
	11.54% { background-position: -2880px 0px; }
	12.50% { background-position: -3120px 0px; }
	13.46% { background-position: -3360px 0px; }
	14.42% { background-position: -3600px 0px; }
	15.38% { background-position: -3840px 0px; }
	16.35% { background-position: -4080px 0px; }
	17.31% { background-position: -4320px 0px; }
	18.27% { background-position: -4560px 0px; }
	19.23% { background-position: 0px -240px; }
	20.19% { background-position: -240px -240px; }
	21.15% { background-position: -480px -240px; }
	22.12% { background-position: -720px -240px; }
	23.08% { background-position: -960px -240px; }
	24.04% { background-position: -1200px -240px; }
	25.00% { background-position: -1440px -240px; }
	25.96% { background-position: -1680px -240px; }
	26.92% { background-position: -1920px -240px; }
	27.88% { background-position: -2160px -240px; }
	28.85% { background-position: -2400px -240px; }
	29.81% { background-position: -2640px -240px; }
	30.77% { background-position: -2880px -240px; }
	31.73% { background-position: -3120px -240px; }
	32.69% { background-position: -3360px -240px; }
	33.65% { background-position: -3600px -240px; }
	34.62% { background-position: -3840px -240px; }
	35.58% { background-position: -4080px -240px; }
	36.54% { background-position: -4320px -240px; }
	37.50% { background-position: -4560px -240px; }
	38.46% { background-position: 0px -480px; }
	39.42% { background-position: -240px -480px; }
	40.38% { background-position: -480px -480px; }
	41.35% { background-position: -720px -480px; }
	42.31% { background-position: -960px -480px; }
	43.27% { background-position: -1200px -480px; }
	44.23% { background-position: -1440px -480px; }
	45.19% { background-position: -1680px -480px; }
	46.15% { background-position: -1920px -480px; }
	47.12% { background-position: -2160px -480px; }
	48.08% { background-position: -2400px -480px; }
	49.04% { background-position: -2640px -480px; }
	50.00% { background-position: -2880px -480px; }
	50.96% { background-position: -3120px -480px; }
	51.92% { background-position: -3360px -480px; }
	52.88% { background-position: -3600px -480px; }
	53.85% { background-position: -3840px -480px; }
	54.81% { background-position: -4080px -480px; }
	55.77% { background-position: -4320px -480px; }
	56.73% { background-position: -4560px -480px; }
	57.69% { background-position: 0px -720px; }
	58.65% { background-position: -240px -720px; }
	59.62% { background-position: -480px -720px; }
	60.58% { background-position: -720px -720px; }
	61.54% { background-position: -960px -720px; }
	62.50% { background-position: -1200px -720px; }
	63.46% { background-position: -1440px -720px; }
	64.42% { background-position: -1680px -720px; }
	65.38% { background-position: -1920px -720px; }
	66.35% { background-position: -2160px -720px; }
	67.31% { background-position: -2400px -720px; }
	68.27% { background-position: -2640px -720px; }
	69.23% { background-position: -2880px -720px; }
	70.19% { background-position: -3120px -720px; }
	71.15% { background-position: -3360px -720px; }
	72.12% { background-position: -3600px -720px; }
	73.08% { background-position: -3840px -720px; }
	74.04% { background-position: -4080px -720px; }
	75.00% { background-position: -4320px -720px; }
	75.96% { background-position: -4560px -720px; }
	76.92% { background-position: 0px -960px; }
	77.88% { background-position: -240px -960px; }
	78.85% { background-position: -480px -960px; }
	79.81% { background-position: -720px -960px; }
	80.77% { background-position: -960px -960px; }
	81.73% { background-position: -1200px -960px; }
	82.69% { background-position: -1440px -960px; }
	83.65% { background-position: -1680px -960px; }
	84.62% { background-position: -1920px -960px; }
	85.58% { background-position: -2160px -960px; }
	86.54% { background-position: -2400px -960px; }
	87.50% { background-position: -2640px -960px; }
	88.46% { background-position: -2880px -960px; }
	89.42% { background-position: -3120px -960px; }
	90.38% { background-position: -3360px -960px; }
	91.35% { background-position: -3600px -960px; }
	92.31% { background-position: -3840px -960px; }
	93.27% { background-position: -4080px -960px; }
	94.23% { background-position: -4320px -960px; }
	95.19% { background-position: -4560px -960px; }
	96.15% { background-position: 0px -1200px; }
	97.12% { background-position: -240px -1200px; }
	98.08% { background-position: -480px -1200px; }
	99.04% { background-position: -720px -1200px; }
	100.00% { background-position: -960px -1200px; }
}
@keyframes candymasterAni {
	0% { background-position: 0px 0px; }
	0.81% { background-position: -240px 0px; }
	1.61% { background-position: -480px 0px; }
	2.42% { background-position: -720px 0px; }
	3.23% { background-position: -960px 0px; }
	4.03% { background-position: -1200px 0px; }
	4.84% { background-position: -1440px 0px; }
	5.65% { background-position: -1680px 0px; }
	6.45% { background-position: -1920px 0px; }
	7.26% { background-position: -2160px 0px; }
	8.06% { background-position: -2400px 0px; }
	8.87% { background-position: -2640px 0px; }
	9.68% { background-position: -2880px 0px; }
	10.48% { background-position: -3120px 0px; }
	11.29% { background-position: -3360px 0px; }
	12.10% { background-position: -3600px 0px; }
	12.90% { background-position: -3840px 0px; }
	13.71% { background-position: -4080px 0px; }
	14.52% { background-position: -4320px 0px; }
	15.32% { background-position: -4560px 0px; }
	16.13% { background-position: 0px -240px; }
	16.94% { background-position: -240px -240px; }
	17.74% { background-position: -480px -240px; }
	18.55% { background-position: -720px -240px; }
	19.35% { background-position: -960px -240px; }
	20.16% { background-position: -1200px -240px; }
	20.97% { background-position: -1440px -240px; }
	21.77% { background-position: -1680px -240px; }
	22.58% { background-position: -1920px -240px; }
	23.39% { background-position: -2160px -240px; }
	24.19% { background-position: -2400px -240px; }
	25.00% { background-position: -2640px -240px; }
	25.81% { background-position: -2880px -240px; }
	26.61% { background-position: -3120px -240px; }
	27.42% { background-position: -3360px -240px; }
	28.23% { background-position: -3600px -240px; }
	29.03% { background-position: -3840px -240px; }
	29.84% { background-position: -4080px -240px; }
	30.65% { background-position: -4320px -240px; }
	31.45% { background-position: -4560px -240px; }
	32.26% { background-position: 0px -480px; }
	33.06% { background-position: -240px -480px; }
	33.87% { background-position: -480px -480px; }
	34.68% { background-position: -720px -480px; }
	35.48% { background-position: -960px -480px; }
	36.29% { background-position: -1200px -480px; }
	37.10% { background-position: -1440px -480px; }
	37.90% { background-position: -1680px -480px; }
	38.71% { background-position: -1920px -480px; }
	39.52% { background-position: -2160px -480px; }
	40.32% { background-position: -2400px -480px; }
	41.13% { background-position: -2640px -480px; }
	41.94% { background-position: -2880px -480px; }
	42.74% { background-position: -3120px -480px; }
	43.55% { background-position: -3360px -480px; }
	44.35% { background-position: -3600px -480px; }
	45.16% { background-position: -3840px -480px; }
	45.97% { background-position: -4080px -480px; }
	46.77% { background-position: -4320px -480px; }
	47.58% { background-position: -4560px -480px; }
	48.39% { background-position: 0px -720px; }
	49.19% { background-position: -240px -720px; }
	50.00% { background-position: -480px -720px; }
	50.81% { background-position: -720px -720px; }
	51.61% { background-position: -960px -720px; }
	52.42% { background-position: -1200px -720px; }
	53.23% { background-position: -1440px -720px; }
	54.03% { background-position: -1680px -720px; }
	54.84% { background-position: -1920px -720px; }
	55.65% { background-position: -2160px -720px; }
	56.45% { background-position: -2400px -720px; }
	57.26% { background-position: -2640px -720px; }
	58.06% { background-position: -2880px -720px; }
	58.87% { background-position: -3120px -720px; }
	59.68% { background-position: -3360px -720px; }
	60.48% { background-position: -3600px -720px; }
	61.29% { background-position: -3840px -720px; }
	62.10% { background-position: -4080px -720px; }
	62.90% { background-position: -4320px -720px; }
	63.71% { background-position: -4560px -720px; }
	64.52% { background-position: 0px -960px; }
	65.32% { background-position: -240px -960px; }
	66.13% { background-position: -480px -960px; }
	66.94% { background-position: -720px -960px; }
	67.74% { background-position: -960px -960px; }
	68.55% { background-position: -1200px -960px; }
	69.35% { background-position: -1440px -960px; }
	70.16% { background-position: -1680px -960px; }
	70.97% { background-position: -1920px -960px; }
	71.77% { background-position: -2160px -960px; }
	72.58% { background-position: -2400px -960px; }
	73.39% { background-position: -2640px -960px; }
	74.19% { background-position: -2880px -960px; }
	75.00% { background-position: -3120px -960px; }
	75.81% { background-position: -3360px -960px; }
	76.61% { background-position: -3600px -960px; }
	77.42% { background-position: -3840px -960px; }
	78.23% { background-position: -4080px -960px; }
	79.03% { background-position: -4320px -960px; }
	79.84% { background-position: -4560px -960px; }
	80.65% { background-position: 0px -1200px; }
	81.45% { background-position: -240px -1200px; }
	82.26% { background-position: -480px -1200px; }
	83.06% { background-position: -720px -1200px; }
	83.87% { background-position: -960px -1200px; }
	84.68% { background-position: -1200px -1200px; }
	85.48% { background-position: -1440px -1200px; }
	86.29% { background-position: -1680px -1200px; }
	87.10% { background-position: -1920px -1200px; }
	87.90% { background-position: -2160px -1200px; }
	88.71% { background-position: -2400px -1200px; }
	89.52% { background-position: -2640px -1200px; }
	90.32% { background-position: -2880px -1200px; }
	91.13% { background-position: -3120px -1200px; }
	91.94% { background-position: -3360px -1200px; }
	92.74% { background-position: -3600px -1200px; }
	93.55% { background-position: -3840px -1200px; }
	94.35% { background-position: -4080px -1200px; }
	95.16% { background-position: -4320px -1200px; }
	95.97% { background-position: -4560px -1200px; }
	96.77% { background-position: 0px -1440px; }
	97.58% { background-position: -240px -1440px; }
	98.39% { background-position: -480px -1440px; }
	99.19% { background-position: -720px -1440px; }
	100.00% { background-position: -960px -1440px; }
}
@keyframes nauticalexplorerAni {
	0% { background-position: 0px 0px; }
	0.81% { background-position: -240px 0px; }
	1.61% { background-position: -480px 0px; }
	2.42% { background-position: -720px 0px; }
	3.23% { background-position: -960px 0px; }
	4.03% { background-position: -1200px 0px; }
	4.84% { background-position: -1440px 0px; }
	5.65% { background-position: -1680px 0px; }
	6.45% { background-position: -1920px 0px; }
	7.26% { background-position: -2160px 0px; }
	8.06% { background-position: -2400px 0px; }
	8.87% { background-position: -2640px 0px; }
	9.68% { background-position: -2880px 0px; }
	10.48% { background-position: -3120px 0px; }
	11.29% { background-position: -3360px 0px; }
	12.10% { background-position: -3600px 0px; }
	12.90% { background-position: -3840px 0px; }
	13.71% { background-position: -4080px 0px; }
	14.52% { background-position: -4320px 0px; }
	15.32% { background-position: -4560px 0px; }
	16.13% { background-position: 0px -240px; }
	16.94% { background-position: -240px -240px; }
	17.74% { background-position: -480px -240px; }
	18.55% { background-position: -720px -240px; }
	19.35% { background-position: -960px -240px; }
	20.16% { background-position: -1200px -240px; }
	20.97% { background-position: -1440px -240px; }
	21.77% { background-position: -1680px -240px; }
	22.58% { background-position: -1920px -240px; }
	23.39% { background-position: -2160px -240px; }
	24.19% { background-position: -2400px -240px; }
	25.00% { background-position: -2640px -240px; }
	25.81% { background-position: -2880px -240px; }
	26.61% { background-position: -3120px -240px; }
	27.42% { background-position: -3360px -240px; }
	28.23% { background-position: -3600px -240px; }
	29.03% { background-position: -3840px -240px; }
	29.84% { background-position: -4080px -240px; }
	30.65% { background-position: -4320px -240px; }
	31.45% { background-position: -4560px -240px; }
	32.26% { background-position: 0px -480px; }
	33.06% { background-position: -240px -480px; }
	33.87% { background-position: -480px -480px; }
	34.68% { background-position: -720px -480px; }
	35.48% { background-position: -960px -480px; }
	36.29% { background-position: -1200px -480px; }
	37.10% { background-position: -1440px -480px; }
	37.90% { background-position: -1680px -480px; }
	38.71% { background-position: -1920px -480px; }
	39.52% { background-position: -2160px -480px; }
	40.32% { background-position: -2400px -480px; }
	41.13% { background-position: -2640px -480px; }
	41.94% { background-position: -2880px -480px; }
	42.74% { background-position: -3120px -480px; }
	43.55% { background-position: -3360px -480px; }
	44.35% { background-position: -3600px -480px; }
	45.16% { background-position: -3840px -480px; }
	45.97% { background-position: -4080px -480px; }
	46.77% { background-position: -4320px -480px; }
	47.58% { background-position: -4560px -480px; }
	48.39% { background-position: 0px -720px; }
	49.19% { background-position: -240px -720px; }
	50.00% { background-position: -480px -720px; }
	50.81% { background-position: -720px -720px; }
	51.61% { background-position: -960px -720px; }
	52.42% { background-position: -1200px -720px; }
	53.23% { background-position: -1440px -720px; }
	54.03% { background-position: -1680px -720px; }
	54.84% { background-position: -1920px -720px; }
	55.65% { background-position: -2160px -720px; }
	56.45% { background-position: -2400px -720px; }
	57.26% { background-position: -2640px -720px; }
	58.06% { background-position: -2880px -720px; }
	58.87% { background-position: -3120px -720px; }
	59.68% { background-position: -3360px -720px; }
	60.48% { background-position: -3600px -720px; }
	61.29% { background-position: -3840px -720px; }
	62.10% { background-position: -4080px -720px; }
	62.90% { background-position: -4320px -720px; }
	63.71% { background-position: -4560px -720px; }
	64.52% { background-position: 0px -960px; }
	65.32% { background-position: -240px -960px; }
	66.13% { background-position: -480px -960px; }
	66.94% { background-position: -720px -960px; }
	67.74% { background-position: -960px -960px; }
	68.55% { background-position: -1200px -960px; }
	69.35% { background-position: -1440px -960px; }
	70.16% { background-position: -1680px -960px; }
	70.97% { background-position: -1920px -960px; }
	71.77% { background-position: -2160px -960px; }
	72.58% { background-position: -2400px -960px; }
	73.39% { background-position: -2640px -960px; }
	74.19% { background-position: -2880px -960px; }
	75.00% { background-position: -3120px -960px; }
	75.81% { background-position: -3360px -960px; }
	76.61% { background-position: -3600px -960px; }
	77.42% { background-position: -3840px -960px; }
	78.23% { background-position: -4080px -960px; }
	79.03% { background-position: -4320px -960px; }
	79.84% { background-position: -4560px -960px; }
	80.65% { background-position: 0px -1200px; }
	81.45% { background-position: -240px -1200px; }
	82.26% { background-position: -480px -1200px; }
	83.06% { background-position: -720px -1200px; }
	83.87% { background-position: -960px -1200px; }
	84.68% { background-position: -1200px -1200px; }
	85.48% { background-position: -1440px -1200px; }
	86.29% { background-position: -1680px -1200px; }
	87.10% { background-position: -1920px -1200px; }
	87.90% { background-position: -2160px -1200px; }
	88.71% { background-position: -2400px -1200px; }
	89.52% { background-position: -2640px -1200px; }
	90.32% { background-position: -2880px -1200px; }
	91.13% { background-position: -3120px -1200px; }
	91.94% { background-position: -3360px -1200px; }
	92.74% { background-position: -3600px -1200px; }
	93.55% { background-position: -3840px -1200px; }
	94.35% { background-position: -4080px -1200px; }
	95.16% { background-position: -4320px -1200px; }
	95.97% { background-position: -4560px -1200px; }
	96.77% { background-position: 0px -1440px; }
	97.58% { background-position: -240px -1440px; }
	98.39% { background-position: -480px -1440px; }
	99.19% { background-position: -720px -1440px; }
	100.00% { background-position: -960px -1440px; }
}

@keyframes windchaserAni {
	0% { background-position: 0px 0px; }
	0.81% { background-position: -240px 0px; }
	1.61% { background-position: -480px 0px; }
	2.42% { background-position: -720px 0px; }
	3.23% { background-position: -960px 0px; }
	4.03% { background-position: -1200px 0px; }
	4.84% { background-position: -1440px 0px; }
	5.65% { background-position: -1680px 0px; }
	6.45% { background-position: -1920px 0px; }
	7.26% { background-position: -2160px 0px; }
	8.06% { background-position: -2400px 0px; }
	8.87% { background-position: -2640px 0px; }
	9.68% { background-position: -2880px 0px; }
	10.48% { background-position: -3120px 0px; }
	11.29% { background-position: -3360px 0px; }
	12.10% { background-position: -3600px 0px; }
	12.90% { background-position: -3840px 0px; }
	13.71% { background-position: -4080px 0px; }
	14.52% { background-position: -4320px 0px; }
	15.32% { background-position: -4560px 0px; }
	16.13% { background-position: 0px -240px; }
	16.94% { background-position: -240px -240px; }
	17.74% { background-position: -480px -240px; }
	18.55% { background-position: -720px -240px; }
	19.35% { background-position: -960px -240px; }
	20.16% { background-position: -1200px -240px; }
	20.97% { background-position: -1440px -240px; }
	21.77% { background-position: -1680px -240px; }
	22.58% { background-position: -1920px -240px; }
	23.39% { background-position: -2160px -240px; }
	24.19% { background-position: -2400px -240px; }
	25.00% { background-position: -2640px -240px; }
	25.81% { background-position: -2880px -240px; }
	26.61% { background-position: -3120px -240px; }
	27.42% { background-position: -3360px -240px; }
	28.23% { background-position: -3600px -240px; }
	29.03% { background-position: -3840px -240px; }
	29.84% { background-position: -4080px -240px; }
	30.65% { background-position: -4320px -240px; }
	31.45% { background-position: -4560px -240px; }
	32.26% { background-position: 0px -480px; }
	33.06% { background-position: -240px -480px; }
	33.87% { background-position: -480px -480px; }
	34.68% { background-position: -720px -480px; }
	35.48% { background-position: -960px -480px; }
	36.29% { background-position: -1200px -480px; }
	37.10% { background-position: -1440px -480px; }
	37.90% { background-position: -1680px -480px; }
	38.71% { background-position: -1920px -480px; }
	39.52% { background-position: -2160px -480px; }
	40.32% { background-position: -2400px -480px; }
	41.13% { background-position: -2640px -480px; }
	41.94% { background-position: -2880px -480px; }
	42.74% { background-position: -3120px -480px; }
	43.55% { background-position: -3360px -480px; }
	44.35% { background-position: -3600px -480px; }
	45.16% { background-position: -3840px -480px; }
	45.97% { background-position: -4080px -480px; }
	46.77% { background-position: -4320px -480px; }
	47.58% { background-position: -4560px -480px; }
	48.39% { background-position: 0px -720px; }
	49.19% { background-position: -240px -720px; }
	50.00% { background-position: -480px -720px; }
	50.81% { background-position: -720px -720px; }
	51.61% { background-position: -960px -720px; }
	52.42% { background-position: -1200px -720px; }
	53.23% { background-position: -1440px -720px; }
	54.03% { background-position: -1680px -720px; }
	54.84% { background-position: -1920px -720px; }
	55.65% { background-position: -2160px -720px; }
	56.45% { background-position: -2400px -720px; }
	57.26% { background-position: -2640px -720px; }
	58.06% { background-position: -2880px -720px; }
	58.87% { background-position: -3120px -720px; }
	59.68% { background-position: -3360px -720px; }
	60.48% { background-position: -3600px -720px; }
	61.29% { background-position: -3840px -720px; }
	62.10% { background-position: -4080px -720px; }
	62.90% { background-position: -4320px -720px; }
	63.71% { background-position: -4560px -720px; }
	64.52% { background-position: 0px -960px; }
	65.32% { background-position: -240px -960px; }
	66.13% { background-position: -480px -960px; }
	66.94% { background-position: -720px -960px; }
	67.74% { background-position: -960px -960px; }
	68.55% { background-position: -1200px -960px; }
	69.35% { background-position: -1440px -960px; }
	70.16% { background-position: -1680px -960px; }
	70.97% { background-position: -1920px -960px; }
	71.77% { background-position: -2160px -960px; }
	72.58% { background-position: -2400px -960px; }
	73.39% { background-position: -2640px -960px; }
	74.19% { background-position: -2880px -960px; }
	75.00% { background-position: -3120px -960px; }
	75.81% { background-position: -3360px -960px; }
	76.61% { background-position: -3600px -960px; }
	77.42% { background-position: -3840px -960px; }
	78.23% { background-position: -4080px -960px; }
	79.03% { background-position: -4320px -960px; }
	79.84% { background-position: -4560px -960px; }
	80.65% { background-position: 0px -1200px; }
	81.45% { background-position: -240px -1200px; }
	82.26% { background-position: -480px -1200px; }
	83.06% { background-position: -720px -1200px; }
	83.87% { background-position: -960px -1200px; }
	84.68% { background-position: -1200px -1200px; }
	85.48% { background-position: -1440px -1200px; }
	86.29% { background-position: -1680px -1200px; }
	87.10% { background-position: -1920px -1200px; }
	87.90% { background-position: -2160px -1200px; }
	88.71% { background-position: -2400px -1200px; }
	89.52% { background-position: -2640px -1200px; }
	90.32% { background-position: -2880px -1200px; }
	91.13% { background-position: -3120px -1200px; }
	91.94% { background-position: -3360px -1200px; }
	92.74% { background-position: -3600px -1200px; }
	93.55% { background-position: -3840px -1200px; }
	94.35% { background-position: -4080px -1200px; }
	95.16% { background-position: -4320px -1200px; }
	95.97% { background-position: -4560px -1200px; }
	96.77% { background-position: 0px -1440px; }
	97.58% { background-position: -240px -1440px; }
	98.39% { background-position: -480px -1440px; }
	99.19% { background-position: -720px -1440px; }
	100.00% { background-position: -960px -1440px; }
}

@keyframes springwalkersAni {
	0% { background-position: 0px 0px; }
	0.81% { background-position: -240px 0px; }
	1.61% { background-position: -480px 0px; }
	2.42% { background-position: -720px 0px; }
	3.23% { background-position: -960px 0px; }
	4.03% { background-position: -1200px 0px; }
	4.84% { background-position: -1440px 0px; }
	5.65% { background-position: -1680px 0px; }
	6.45% { background-position: -1920px 0px; }
	7.26% { background-position: -2160px 0px; }
	8.06% { background-position: -2400px 0px; }
	8.87% { background-position: -2640px 0px; }
	9.68% { background-position: -2880px 0px; }
	10.48% { background-position: -3120px 0px; }
	11.29% { background-position: -3360px 0px; }
	12.10% { background-position: -3600px 0px; }
	12.90% { background-position: -3840px 0px; }
	13.71% { background-position: -4080px 0px; }
	14.52% { background-position: -4320px 0px; }
	15.32% { background-position: -4560px 0px; }
	16.13% { background-position: 0px -240px; }
	16.94% { background-position: -240px -240px; }
	17.74% { background-position: -480px -240px; }
	18.55% { background-position: -720px -240px; }
	19.35% { background-position: -960px -240px; }
	20.16% { background-position: -1200px -240px; }
	20.97% { background-position: -1440px -240px; }
	21.77% { background-position: -1680px -240px; }
	22.58% { background-position: -1920px -240px; }
	23.39% { background-position: -2160px -240px; }
	24.19% { background-position: -2400px -240px; }
	25.00% { background-position: -2640px -240px; }
	25.81% { background-position: -2880px -240px; }
	26.61% { background-position: -3120px -240px; }
	27.42% { background-position: -3360px -240px; }
	28.23% { background-position: -3600px -240px; }
	29.03% { background-position: -3840px -240px; }
	29.84% { background-position: -4080px -240px; }
	30.65% { background-position: -4320px -240px; }
	31.45% { background-position: -4560px -240px; }
	32.26% { background-position: 0px -480px; }
	33.06% { background-position: -240px -480px; }
	33.87% { background-position: -480px -480px; }
	34.68% { background-position: -720px -480px; }
	35.48% { background-position: -960px -480px; }
	36.29% { background-position: -1200px -480px; }
	37.10% { background-position: -1440px -480px; }
	37.90% { background-position: -1680px -480px; }
	38.71% { background-position: -1920px -480px; }
	39.52% { background-position: -2160px -480px; }
	40.32% { background-position: -2400px -480px; }
	41.13% { background-position: -2640px -480px; }
	41.94% { background-position: -2880px -480px; }
	42.74% { background-position: -3120px -480px; }
	43.55% { background-position: -3360px -480px; }
	44.35% { background-position: -3600px -480px; }
	45.16% { background-position: -3840px -480px; }
	45.97% { background-position: -4080px -480px; }
	46.77% { background-position: -4320px -480px; }
	47.58% { background-position: -4560px -480px; }
	48.39% { background-position: 0px -720px; }
	49.19% { background-position: -240px -720px; }
	50.00% { background-position: -480px -720px; }
	50.81% { background-position: -720px -720px; }
	51.61% { background-position: -960px -720px; }
	52.42% { background-position: -1200px -720px; }
	53.23% { background-position: -1440px -720px; }
	54.03% { background-position: -1680px -720px; }
	54.84% { background-position: -1920px -720px; }
	55.65% { background-position: -2160px -720px; }
	56.45% { background-position: -2400px -720px; }
	57.26% { background-position: -2640px -720px; }
	58.06% { background-position: -2880px -720px; }
	58.87% { background-position: -3120px -720px; }
	59.68% { background-position: -3360px -720px; }
	60.48% { background-position: -3600px -720px; }
	61.29% { background-position: -3840px -720px; }
	62.10% { background-position: -4080px -720px; }
	62.90% { background-position: -4320px -720px; }
	63.71% { background-position: -4560px -720px; }
	64.52% { background-position: 0px -960px; }
	65.32% { background-position: -240px -960px; }
	66.13% { background-position: -480px -960px; }
	66.94% { background-position: -720px -960px; }
	67.74% { background-position: -960px -960px; }
	68.55% { background-position: -1200px -960px; }
	69.35% { background-position: -1440px -960px; }
	70.16% { background-position: -1680px -960px; }
	70.97% { background-position: -1920px -960px; }
	71.77% { background-position: -2160px -960px; }
	72.58% { background-position: -2400px -960px; }
	73.39% { background-position: -2640px -960px; }
	74.19% { background-position: -2880px -960px; }
	75.00% { background-position: -3120px -960px; }
	75.81% { background-position: -3360px -960px; }
	76.61% { background-position: -3600px -960px; }
	77.42% { background-position: -3840px -960px; }
	78.23% { background-position: -4080px -960px; }
	79.03% { background-position: -4320px -960px; }
	79.84% { background-position: -4560px -960px; }
	80.65% { background-position: 0px -1200px; }
	81.45% { background-position: -240px -1200px; }
	82.26% { background-position: -480px -1200px; }
	83.06% { background-position: -720px -1200px; }
	83.87% { background-position: -960px -1200px; }
	84.68% { background-position: -1200px -1200px; }
	85.48% { background-position: -1440px -1200px; }
	86.29% { background-position: -1680px -1200px; }
	87.10% { background-position: -1920px -1200px; }
	87.90% { background-position: -2160px -1200px; }
	88.71% { background-position: -2400px -1200px; }
	89.52% { background-position: -2640px -1200px; }
	90.32% { background-position: -2880px -1200px; }
	91.13% { background-position: -3120px -1200px; }
	91.94% { background-position: -3360px -1200px; }
	92.74% { background-position: -3600px -1200px; }
	93.55% { background-position: -3840px -1200px; }
	94.35% { background-position: -4080px -1200px; }
	95.16% { background-position: -4320px -1200px; }
	95.97% { background-position: -4560px -1200px; }
	96.77% { background-position: 0px -1440px; }
	97.58% { background-position: -240px -1440px; }
	98.39% { background-position: -480px -1440px; }
	99.19% { background-position: -720px -1440px; }
	100.00% { background-position: -960px -1440px; }
}
</style>


