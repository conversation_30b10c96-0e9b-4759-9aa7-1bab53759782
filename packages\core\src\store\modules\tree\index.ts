import { defineStore } from 'pinia'
import actions from './actions'
import type { treeType } from './type'

export const useTreeStore = defineStore('treeStore', {
    state: (): treeType => ({
        hasPlanted: false, //是否已经种植
        planted: false, //刚种植，
        treeCode: '',
        energyTotal: 0,
        lv: 1,
        treeList:{},
        nowTree:{},
        foundryed: false
    }),
    actions
})
