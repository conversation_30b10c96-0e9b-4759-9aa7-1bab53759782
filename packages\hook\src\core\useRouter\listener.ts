import type { RouteLocationNormalized } from 'vue-router'
import { useEventBus } from '../useEventBus'

const eventBus = useEventBus()

const key = 'ROUTE_CHANGE'

const instance = {
  isRegister: false
}

let latestRoute: RouteLocationNormalized

function emit(to: RouteLocationNormalized) {
  eventBus.emitToInstance(key, instance, to)
  latestRoute = to
}

function listen(handler: (route: RouteLocationNormalized) => void, immediate = true) {
  if (!instance.isRegister) {
    instance.isRegister = true
    eventBus.register(instance)
  }
  eventBus.on(key, handler, instance)
  if (immediate && latestRoute) {
    handler(latestRoute)
  }
}

function remove() {
  instance.isRegister = false
  eventBus.unregister(instance)
}

export { emit, listen, remove }
