<template>
        <div class="energy_container" style="display:block" :class="{'flower':item.flower_type}">
       <!-- <div class="energy_container" :style="`display:${flod? 'flex' :'block'}`" :class="{'flower':item.flower_type}"> -->
        <!-- 用户头像 -->
            <div :class="!item.flower_type ? 'user_avatar' : 'give_avatar'">
            <!-- <img @click="type == 0 ? toFriend() : null" :src="type == 0 ? item.headLogo : $imgs[`head.png`]" alt=""> -->
            <img @click="type == 0 ? toFriend() : null" :src="type == 0 ? $imgs[`head.png`] : $imgs[`head.png`]" alt="">
            </div>
            <!-- 收取信息 -->
            <div class="enery_info" v-if="!item.flower_type">
                <div class="info1">
                    <div class="info1_value" v-if="type == 0">
                        [<span class="info1_name">{{ item.name }}</span>]
                        <span>{{state.friend.共收取你}} <span style="color: #EF8A28;">{{ item.total }}g</span> {{ state.friend.减碳值数}}</span>
                    </div>
                    <div class="info1_value"  v-if="type == 1">
                    {{state.friend.我领取}} <span class="info1_name">{{ `[${ item.name }]` }}</span> {{ lang=='en'?state.friend.从:''}} <span class="info1_name" style="color: #22992C;">{{ item.total+'g' }}</span>
                </div>
                <div class="list_button">
                <div class="list_show">
                        <div class="button_name" @click="() => flod = !flod" v-if="flod">{{ state.friend.展開 }}</div>
                        <div class=" button_name" @click="() => flod = !flod" v-else>{{ state.friend.收起}}</div>
                <span class="triangle" v-if="flod"></span>
                <span class="triangle1" v-else></span>
                </div>
            </div>
            </div>
            </div>
            <!-- 花信息 -->
            <div class="flower_info"  v-if="item.flower_type">
                <!-- 别人送我 -->
                <p v-if="type == 0">[{{ item.name }}]{{ state.flower.送你一瓶 }}<span style="color: #006CFF;text-decoration: underline;pointer-events: all;" @click="eventBus.emit('clickson')">{{ treeName[item.flower_type] }}</span></p>
                <!-- 我送别人 -->
                <p v-else>【{{ state.flower.你 }}】{{ state.flower.贈送 }}[{{ item.name }}]{{ state.flower.一瓶 }}<span style="color: #006CFF;text-decoration: underline;pointer-events: all;" @click="eventBus.emit('clickson')">{{ treeName[item.flower_type] }}</span></p>
                <span class="time">{{ item.create_time.replace(day,'') }}</span>
            </div>
            <!--我/某一个好友减碳值的领取详情，+动画效果 -->
            <transition name="my" v-if="!item.flower_type">
              <div class="enery_detail" v-if="!flod">
                  <div class="detail_item" v-for="(item, index) in item.list" :key="index" >
                        <div class="circle"></div>
                        <div class="detail_value">{{ state.friend.領取了 }}<span class="num_color">{{item.energy_value}}g</span>{{ state.friend.減碳值 }}</div>
                        <div class="detail_time">{{item.clicked_at}}</div>
                   </div>
                  </div>
            </transition>     
       
    </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive, onMounted, onBeforeMount, onBeforeUnmount, getCurrentInstance, computed } from 'vue'
import { useRouter, useLang, useLoading, useDayjs, useStorage, useEnvConfig, useDialog, useToast, useEventBus } from 'hook'
import { useTaskStore, useTreeStore, useUserStore, usePropStore, useAppStore, useFriendStore } from '@/store'
import { stat } from 'fs'

const userStore = useUserStore()

const { state,lang} = useLang()
const props=defineProps({
    item:{
        type:Object,
        required:true
    },
    type: {
        type: Number,
        defualt: 1
    },
    third_id: {
        type: String
    },
    day: {
        type: String
    },
})
let eventBus = useEventBus()
const { router, currentRoute } = useRouter()
let emit = defineEmits(['changefold'])
let treeName = ref({
    diaozhonghua:state.flower.吊鐘花,
    jieguojueming:state.flower.節果決明,
    lanhuaying:state.flower.藍花楹,
    xiuqiuhua:state.flower.繡球花,
    yangzijing:state.flower.洋紫荊,
    yumushu:state.flower.魚木樹,
    mumian:state.flower.木棉
})
let flod = ref(true)
function toFriend() {
    if(!useTreeStore().hasPlanted){
        eventBus.emit('isPlant')
        return
    }
    eventBus.emit('closeFriendSheet')
    router.push({
        path: "/home",
        query: {
            friendId: props.third_id,
            hideNavigationBar:'true'
        }
    })
}
watch(props as any, ()=>{
    flod.value = true
}, {deep: true})

</script>
<style lang="less" scoped>

.energy_container{
                position: relative;
                margin-bottom:32px;
                // flex-wrap: wrap;
                align-items: center;
                .user_avatar{
                    position: relative;
                    pointer-events: all;
                    width: 66px;
                    height: 66px;
                    margin-right: 14px;
                    display: inline-block;
                img{
                        pointer-events: all;
                        width: 66px;
                        height: 66px;
                    }
                }
                .user_avatar::after{
                    position: absolute;
                    width: 24px;
                    height: 24px;
                    display: block;
                    content: '';
                    clear: both;
                    background: url('@assets/imgs/collect__icon.png') no-repeat;
                    background-size: cover;
                    bottom: 0;
                    right: 0;
                }
                .give_avatar{
                    position: relative;
                    pointer-events: all;
                    width: 66px;
                    height: 66px;
                    margin-right: 14px;
                    display: inline-block;
                img{
                        pointer-events: all;
                        width: 66px;
                        height: 66px;
                    }
                }
                .give_avatar::after{
                    position: absolute;
                    width: 24px;
                    height: 24px;
                    display: block;
                    content: '';
                    clear: both;
                    background: url('@assets/imgs/giveIcon.png') no-repeat;
                    background-size: cover;
                    bottom: 0;
                    right: 0;
                }
                .enery_info{
                    width: 600px;
                    align-items: center;
                    transition: height 1s linear;
                    display: inline-block;
                    transform: translateY(-20px);
                    .info1{
                        display: flex;
                        justify-content: space-around;
                        .info1_value{
                            width: 70%;
                            flex: 1;
                            // display: flex;
                            margin-right: 20px;
                            .info1_name{
                                display:inline-block;
                                overflow: hidden; /* 超出容器部分隐藏 */
                                text-overflow: ellipsis; /* 超出部分以省略号表示 */
                                white-space: nowrap; /* 防止文本换行 */
                                max-width:49%;
                                //微调元素
                                position: relative;
                                top:5px
                            }
                        }

                    }
                  
                }
                .flower_info{
                    display: flex;
                    justify-content: center;
                    flex-direction: column;
                    .time{
                        font-size: 6px;
                        font-family: PingFang TC-Regular, PingFang TC;
                        font-weight: 400;
                        color: rgba(78,91,126,0.4);
                    }
                }
                .list_button{
                    width: 118px;
                    height: 38px;
                    border-radius: 22px;

                    border: 2px solid #A7AEC3;
                .list_show{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    pointer-events: all;
                    .button_name{
                        color: #A7AEC3;
                    }
                    .triangle{
                        width: 0;
                        height: 0;
                        margin-left: 8px;
                        border-left: 7px solid transparent;
                        border-right: 7px solid transparent;
                        border-bottom: 8px solid #A7AEC3;
                    }
                    .triangle1{
                        width: 0;
                        height: 0;
                        margin-left: 8px;
                        border-left: 7px solid transparent;
                        border-right: 7px solid transparent;
                        border-top: 8px solid #A7AEC3;
                    }
                }
            }
                }
                .flower{
                    display: flex;
                }
                .enery_detail{
                    width: 100%;
                font-size: 24px;
                padding-right: 50px;
                position: relative;
                // padding-left:80px;
                // transition: height 2s ease;
                .detail_item{
                    display: flex;
                    margin-top: 15px;
                    justify-content: space-around;
                    align-items:center;
                    .detail_value{
                        flex: 1;
                        margin-left: 44px;
                        .num_color{
                        color: #22992C;
                    } 
                    }
                    .detail_time{
                        color: #ACB3C7;
                    }
                    
                }
                .circle{
                    // position: absolute;
                    // top: 16px;
                    // left: 26px;
                    width: 14px;
                    height: 14px;
                    border-radius: 50%;
                    background-color: #EDEFF3;
                    margin-left: 28px;
                }
            }
            .enery_detail::before{
                content: '';
                position: absolute;
                // top: 66px;
                left: 34px;
                height: 90%;
                width: 2px;
                background: #EDEFF3;
            }
            .folded{
                height: 0px;
                overflow: hidden;
            }
            .my-enter-from,
            .my-leave-to {
                opacity: 0;
        }
            .my-enter-active,
            .my-leave-active{
                transition: opacity 0.4s ease;
            }


</style>
