<template>
  <div class="box">
    <div class="contain">
      <p :class="lang == 'en' ? 'enp' : '' ">{{state.dialog.你已成功种下x的种子.replace("{x}", props.plantName)}} </p>
      <img class="treeIcon" :src="$imgs[`TT/state/${treeCode}/s1.png`]" alt="">
    </div>
    <button class="btn" @click="toHome"></button>
  </div>
</template>

<script setup>
import { useDialog, useLang } from 'hook';
import { onMounted, inject, ref, watch, getCurrentInstance } from 'vue'

const { state, lang } = useLang()

const props = defineProps({
  plantName: {
      type: String,
      required: false
  },
  treeCode:{
    type: String,
    required: false
  }
})
const uuid = inject('uuid')

const toHome = () =>{
    useDialog().getInstance(uuid)?.emit('toHome')
} 

</script>

<style lang='less' scoped>
.box{
    display: flex;
    flex-direction: column;
    align-items: center;
    .contain{
      width: 570px;
      height: 384px;
      background: url('@assets/TT/share_card.png') no-repeat;
      background-size: 100% 100%;
      position: relative;
      p{
        // background-color: #fff;
        width: 100%;
        padding: 0 20px;
        height: 72px;
        // position: absolute;
        // left: 50%;
        // transform: translate(-50%, 0);
        // top: 0px;
        font-size: 30px;
        white-space: pre-line;
        font-family: PingFang SC-Bold, PingFang SC, Arial;
        font-weight: bold;
        color: #FFFFFF;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .enp{
        font-size: 26px;
      }
      .treeIcon{
        position: absolute;
        left: 50%;
        bottom: 70px;
        transform-origin: center center;
        transform: translate(-50%, 0) scale(1);
        max-height: 176px;
        max-width: 176px;
      }
    }
    .btn{
      margin-top: 40px;
      width: 56px;
      height: 56px;
      background: url('@assets/imgs/closeIcon.png') no-repeat;
      background-size: 100% 100%;
      border: none;
    }
}
</style>