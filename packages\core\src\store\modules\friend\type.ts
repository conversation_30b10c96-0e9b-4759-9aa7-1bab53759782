import { useFriendStore } from '.'
export interface friendType {
    isOwn: boolean,
    friendConfig?: any,
    friendList: Array<friendType["friendConfig"]>,
    activityList: any[],
    marqueeList: any[],
    noticeCount: number,
    propsConfig: {
        挂饰: Array<any>, 
        铲子: null | string , 
        围栏: null | string,
        灯柱: null | string,
        稻草人: null | string,
        木头车: null | string,
        喷水池: null | string,
        桌子: null | string,
        椅子: null | string,
        GIF: null | string
    }
    homeFriendList:any[]
    flowerFriendList:any[]
}
export type TaskFriendType = ReturnType<typeof useFriendStore>