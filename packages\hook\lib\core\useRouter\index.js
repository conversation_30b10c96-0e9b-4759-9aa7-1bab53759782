import { createRouter, createWebHashHistory } from 'vue-router';
import * as listener from './listener';
const router = createRouter({
    history: createWebHashHistory(),
    routes: [],
    scrollBehavior: () => {
        return { top: 0 };
    }
});
router.afterEach((to) => {
    listener.emit(to);
});
function addRoutes(routes) {
    routes.forEach((route) => {
        router.addRoute(route);
    });
}
let currentRoute;
listener.listen((route) => {
    currentRoute = route;
}, true);
export function useRouter() {
    return {
        addRoutes,
        router,
        currentRoute,
        listener
    };
}
