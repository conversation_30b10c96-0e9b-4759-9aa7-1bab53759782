import { useEnvConfig } from '../useEnvConfig'

export enum RuntimeMode {
  DEV = 'dev',
  PROD = 'prod'
}
export enum RuntimeEnv {
  web = 'web', // h5
  wx = 'wx', // 微信
  wxMini = 'wxMini', // 微信小程序
  other = 'other' // 其他
}
export enum MobileType {
  web = 'web',
  android = 'android',
  ios = 'ios'
}

const { isProd } = useEnvConfig()

let runtimeEnv: RuntimeEnv = RuntimeEnv.web
let runtimeMode: RuntimeMode = isProd ? RuntimeMode.PROD : RuntimeMode.DEV
let mobileType: MobileType = MobileType.web

function checkEnv() {
  const ua = navigator.userAgent
  if (/micromessenger/i.test(ua)) {
    runtimeEnv = RuntimeEnv.wx
  }
}

function setRuntimeEnv(env: RuntimeEnv) {
  runtimeEnv = env
}

function checkMobileType() {
  const ua = navigator.userAgent
  if (ua.indexOf('Android') > -1 || ua.indexOf('Adr') > -1) {
    mobileType = MobileType.android
  }
  if (!!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {
    mobileType = MobileType.ios
  }
}

checkEnv()
checkMobileType()

export function useRuntimeEnv() {
  return {
    runtimeEnv,
    runtimeMode,
    mobileType,
    setRuntimeEnv
  }
}
