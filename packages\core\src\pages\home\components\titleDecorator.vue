<template>
    <div ref="sprite" style="position: absolute" v-show="loadOver">
        <slot></slot>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { usePropStore } from '@/store'
import { nextTick } from 'process'
import defaultHeadBoxImg from '@/assets/TT/head-box.png'
import { imgs } from '@/assets/imgs'
const sprite = ref()
const loadOver = ref(false)
// 在未登录的情况下可能获取到的道具为undefind所以取了-1字符串作为默认值
let preCode = '-1'
onMounted(async () => {
    initSprite()
})

// 监听标题装饰的变化
const stopWatching = watch(
    usePropStore(),
    () => {
        initSprite()
    },
    { deep: true }
)

// 请写成一个hooks
/** 初始化精灵 */
const initSprite = async () => {
    const html = sprite.value
    if (html) {
        const code = usePropStore().propsConfig['标题装饰']
        if (preCode === code) {
            return
        }
        preCode = code
        // 清除原来的样式
        html.classList.remove('default')
        html.classList.remove('sprite')
        html.style['background-image'] = 'unset'
        loadOver.value = false
        if (!code) {
            html.style['background-image'] = `url(${defaultHeadBoxImg})`
            nextTick(() => {
                html.classList.add('default')
                loadOver.value = true
            })
        } else {
            html.style['background-image'] = `url(${imgs[`titleDecorator/${code}.png`]})`
            nextTick(() => {
                html.classList.add('sprite')
                loadOver.value = true
            })
        }
    }
}
</script>

<style lang="less" scoped>
@spw: 225px;
@sph: 62px;
.frame(@col:0, @row:0) {
    background-position: -@spw * @col -@sph * @row;
}
.sprite {
    animation: myAnimation 3s steps(1) /*infinite*/;
    background-size: 4950px 372px;
    background-position: -450px -248px;
    // 让动画保持在最后一帧
    // animation-fill-mode: forwards;
    // .frame(1,2);
    @keyframes myAnimation {
        0% {
            background-position: 0px 0px;
        }
        4.35% {
            background-position: -450px 0px;
        }
        8.70% {
            background-position: -900px 0px;
        }
        13.04% {
            background-position: -1350px 0px;
        }
        17.39% {
            background-position: -1800px 0px;
        }
        21.74% {
            background-position: -2250px 0px;
        }
        26.09% {
            background-position: -2700px 0px;
        }
        30.43% {
            background-position: -3150px 0px;
        }
        34.78% {
            background-position: -3600px 0px;
        }
        39.13% {
            background-position: -4050px 0px;
        }
        43.48% {
            background-position: -4500px 0px;
        }
        47.83% {
            background-position: 0px -124px;
        }
        52.17% {
            background-position: -450px -124px;
        }
        56.52% {
            background-position: -900px -124px;
        }
        60.87% {
            background-position: -1350px -124px;
        }
        65.22% {
            background-position: -1800px -124px;
        }
        69.57% {
            background-position: -2250px -124px;
        }
        73.91% {
            background-position: -2700px -124px;
        }
        78.26% {
            background-position: -3150px -124px;
        }
        82.61% {
            background-position: -3600px -124px;
        }
        86.96% {
            background-position: -4050px -124px;
        }
        91.30% {
            background-position: -4500px -124px;
        }
        95.65% {
            background-position: 0px -248px;
        }
        100.00% {
            background-position: -450px -248px;
        }
    }
}

.default {
    background-size: 450px 124px;
}
</style>
