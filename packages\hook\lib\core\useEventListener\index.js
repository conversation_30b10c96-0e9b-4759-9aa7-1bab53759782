let supportsPassive = false;
try {
    const opts = {};
    Object.defineProperty(opts, 'passive', {
        get() {
            supportsPassive = true;
        }
    });
    window.addEventListener('test-passive', null, opts);
}
catch (e) { }
export function useEventListener(scope, type, handler, options) {
    if (options && typeof options !== 'boolean') {
        const { passive = false, capture = false } = options;
        options = supportsPassive ? { capture, passive } : capture;
    }
    if (typeof type === 'string') {
        scope.addEventListener(type, handler, options);
        return () => {
            scope.removeEventListener(type, handler, options);
        };
    }
    const resultList = [];
    type.forEach((item) => {
        scope.addEventListener(item, handler, options);
        resultList.push(() => {
            scope.removeEventListener(item, handler, options);
        });
    });
    return () => {
        resultList.forEach((fn) => fn());
    };
}
