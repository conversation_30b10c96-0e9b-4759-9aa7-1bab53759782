<template>
  <div class="box">
      <img class="bg" :src="$imgs['card-short.png']" alt="">
      <img class="sup" :src="$imgs['exclamation.png']" alt="">
      <p class="tip">{{state.home.一张道具卡}}</p>
      <div class="ikonw" @click="emit('close')">{{state.dialog.知道啦}}</div>
      <img class="xx" @click="emit('close')" :src="$imgs['xx.png']" alt="">
  </div>
</template>

<script setup>
import { useLang } from 'hook'
const { state } = useLang()
let emit = defineEmits(['close'])

</script>

<style lang='less' scoped>
.box{
    width: 570px;
    height: 642px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    .xx{
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -80px;
        width: 56px;
    }
    .bg{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
    .sup{
        position: relative;
        width: 280px;
        height: 280px;
        margin-bottom: 4px;
        margin-top: 96px;
    }
    .tip{
        position: relative;
        font-size: 36px;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #4E5B7E;
        white-space: pre-line;
        text-align: center;
    }
    .ikonw{
        position: absolute;
        left: 50%;
        bottom: 70px;
        transform: translate(-50%, 0);
        width: 372px;
        height: 86px;
        background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
        box-shadow: inset 0px 4px 0px 2px rgba(255,242,178,1);
        border-radius: 48px 12px 48px 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
    }
}
</style>