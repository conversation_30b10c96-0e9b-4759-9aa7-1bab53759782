import { AppType } from './type'
import { getSystem, getVersionForAosAndIos } from '@via/mylink-sdk'
import { useApi, useEnvConfig, useToast, useLang, useEventBus,useDialog } from 'hook'
import adDialog from "@/components/adDialog.vue";
const dialog = useDialog({
    adDialog
})
import { useUserStore } from '@/store'
const bus = useEventBus()
const env = useEnvConfig()
const { state } = useLang()
const { get, post, authPost, authGet, authRequest } = useApi()
const { toast } = useToast()

let adDia = dialog.get('adDialog')
function changeIsFinishKV(this: AppType, bol: boolean) {
    this.isFinishKV = bol
}

interface AdInitParams {
    resultCode: string
    [key: string]: any
}

interface AdNotiParams {
    resultCode: number
    rewardAmount?: number
    rewardType?: string
    isClickAd?: boolean
}


const originalInitCallback = window.googleAdInitCallback
const originalNotiCallback = window.googleAdNoti
function loadAndShowGoogleAd(adType: string, unitId: string): Promise<AdInitParams> {
    return new Promise((resolve, reject) => {
        // 参数校验（根据需求强化校验逻辑）
        if (!adType || !unitId) {
            return reject(new Error('adType and unitId are required parameters'))
        }

        // 保存原始回调防止污染

        // 广告初始化回调处理
        window.googleAdInitCallback = function initHandler(params: AdInitParams) {
            // 无论成功失败都立即恢复原始回调
            window.googleAdInitCallback = originalInitCallback
            resolve(params)
            // if (params.resultCode !== '0') {
            //     console.log('获取广告状态：', params)
            //     window.googleAdNoti = originalNotiCallback // 恢复通知回调
            //     reject(params) // 初始化失败
            // } else {
            //     // 初始化成功，启动广告展示
            //     const payload = { callbackName: 'googleAdNoti' }

            //     // 平台相关调用
            //     if (getSystem() === 'android') {
            //         window.HkAndroid?.mylinkStartGoogleAd(JSON.stringify(payload))
            //     } else if (getSystem() === 'ios') {
            //         window?.webkit?.messageHandlers?.mylinkStartGoogleAd?.postMessage(JSON.stringify(payload))
            //     }

            //     // 监听广告关闭通知
            //     window.googleAdNoti = function notiHandler(notiParams: AdNotiParams) {
            //         window.googleAdNoti = originalNotiCallback // 恢复原始回调
            //         resolve(notiParams) // 返回广告关闭参数
            //     }
            // }
        }

        const initPayload = {
            callbackName: 'googleAdInitCallback',
            unitId,
            adType
        }

        console.log('Ad initialization:', initPayload)

        // 执行平台初始化调用
        if (getSystem() === 'android') {
            window.HkAndroid?.mylinkInitGoogleAd(JSON.stringify(initPayload))
        } else if (getSystem() === 'ios') {
            window?.webkit?.messageHandlers?.mylinkInitGoogleAd?.postMessage(JSON.stringify(initPayload))
        }
    })
}

function startGoogleAd(this: AppType): Promise<AdNotiParams> {
    return new Promise((resolve, reject) => {
        if (this.params.resultCode !== '0') {
            console.log('获取广告状态：', this.params)
            // if (this.params.resultCode == '-2' || this.params.resultCode == '-3') {
            //     adDia
            //     .on('close',()=>{adDia.close()})
            //     .show({
            //         title:state.advertisement.暫無Google廣告來源請稍後再來,
            //         desc:state.advertisement.詳情請參閲條款及細則
            //     },{maskClose:false})
            // } else if (this.params.resultCode == '-1') {
            //     adDia
            //     .on('close',()=>{adDia.close()})
            //     .show({
            //         title:state.advertisement.您當前的設備不支持Google廣告播放,
            //         desc:state.advertisement.詳情請參閲條款及細則
            //     },{maskClose:false})
            // } else if (this.params.resultCode == '-4') {
            //     adDia
            //     .on('close',()=>{adDia.close()})
            //     .show({
            //         title:state.advertisement.您當前設備或MyLinkApp版本過低不支持Google廣告播放,
            //         desc:state.advertisement.詳情請參閲條款及細則
            //     },{maskClose:false})
            // }
            window.googleAdNoti = originalNotiCallback // 恢复通知回调
            reject(this.params) // 初始化失败
        } else {
            const unitId = this.unitId
            playAdaward(unitId)
            // 初始化成功，启动广告展示
            const payload = { callbackName: 'googleAdNoti' }
            // 平台相关调用
            if (getSystem() === 'android') {
                window.HkAndroid?.mylinkStartGoogleAd(JSON.stringify(payload))
            } else if (getSystem() === 'ios') {
                window?.webkit?.messageHandlers?.mylinkStartGoogleAd?.postMessage(JSON.stringify(payload))
            }

            // 监听广告关闭通知
            window.googleAdNoti = function notiHandler(notiParams: AdNotiParams) {
                window.googleAdNoti = originalNotiCallback // 恢复原始回调
                resolve(notiParams) // 返回广告关闭参数
            }
        }
    })
    
}

async function initAd(
    this: AppType,
    type: string,
    unitIdObj: {
        android: string
        ios: string
    }
) {
    const unitId = unitIdObj[getSystem()]
    let res = await loadAndShowGoogleAd(type, unitId)
    this.params = res
    this.unitId = unitId
    console.log('广告初始化成功，结果:', this.params);
}

function callBackAdaward(unitId,notiParams) {
    return post(env.CALLBACKURL, {
        data: {
            code: 'mylink-forest',
            advertisementKey: 'ADVERTISEMENT_CONFIG',
            data: notiParams,
            thirdId: useUserStore().third_id,
            unitId
        }
    })
}

function playAdaward(unitId) {
    return post(env.PLAYURL, {
        data: {
            code: 'mylink-forest',
            advertisementKey: 'ADVERTISEMENT_CONFIG',
            thirdId: useUserStore().third_id,
            unitId
        }
    })
}

export default { changeIsFinishKV, initAd, startGoogleAd,callBackAdaward }
