import { useDialog } from './index';
export declare namespace UseDialog {
    type useDialogType = typeof useDialog;
    type AlignX = 'left' | 'center' | 'right';
    type AlignY = 'top' | 'center' | 'bottom';
    type DialogOpt = Partial<{
        maskClose: boolean;
        animName: string;
        maskAnimName: string;
        maskBgColor: string;
        alignX: AlignX;
        alignY: AlignY;
    }>;
    type DialogData = {
        uuid: string;
        name: string;
        componentName: string;
        props: Record<string, any>;
        opts: DialogOpt;
        isLocal: boolean;
        isShowing: boolean;
    };
    type DialogRegisterType = Record<string, any> & {
        name: string;
        dialogName?: string;
        componentName?: string;
        opts: DialogOpt;
    };
}
