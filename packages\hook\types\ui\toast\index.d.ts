import { UseToast } from './type';
declare function toast(tip: string, config?: Partial<UseToast.ToastOption>, icon?: string, secondTip?: string): void;
declare namespace toast {
    var top: (tip: string, config?: Partial<UseToast.ToastOption>) => void;
    var center: (tip: string, config?: Partial<UseToast.ToastOption>) => void;
    var bottom: (tip: string, config?: Partial<UseToast.ToastOption>) => void;
    var withIcon: (icon: string, tip: string, secondTip: string, config?: Partial<UseToast.ToastOption>) => void;
}
export declare function useToast(): {
    app: import("vue").App<Element>;
    toast: typeof toast;
};
export {};
