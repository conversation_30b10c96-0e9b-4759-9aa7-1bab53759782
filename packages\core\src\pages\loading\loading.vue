<template>
    <div>
        <!-- loading页样式 -->
        <div class="lx-load-mark" v-if="!loading">
            <img :src="loadingImg" alt="" class="loading-img" />
            <p>{{ state.加载 }}</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, shallowRef, watch, ref } from 'vue'
import { useRouter, useEnvConfig, useWindow, useLang } from 'hook'
import { inApp } from '@via/mylink-sdk'
import { imgs } from '@/assets/imgs/index'
import { preloadImg } from '@/util/unity'
import loadingImg from '@/assets/imgs/loading.gif'
const envConfig = useEnvConfig()
const { router } = useRouter()
const { state } = useLang()
let loading = ref(false)
const imgArr = [
    new URL('../../assets/TT/head-box.png', import.meta.url).href,
    new URL('../../assets/TT/myMall.png', import.meta.url).href,
    new URL('../../assets/TT/blindBox.png', import.meta.url).href,
    new URL('../../assets/TT/house.png', import.meta.url).href,
    new URL('../../assets/TT/mountain.png', import.meta.url).href,
    new URL('../../assets/TT/background.png', import.meta.url).href,
    new URL('../../assets/TT/leafs.png', import.meta.url).href,

    // 预加载标题动效
    imgs['titleDecorator/title_diaozhonghua.png'],
    imgs['titleDecorator/title_jieguojueming.png'],
    imgs['titleDecorator/title_lanhuaying.png'],
    imgs['titleDecorator/title_xiuqiuhua.png'],
    imgs['titleDecorator/title_yangzijing.png'],
    imgs['titleDecorator/title_yumushu.png'],
    imgs['titleDecorator/title_mumian.png'],
    imgs['titleDecorator/title_fuguizhu.png'],
    imgs['titleDecorator/title_taohua.png'],

    imgs['活动/tw.png'],
    imgs['背包/tw.png'],
    imgs['task/tw.png']
]

const routerName = router.currentRoute.value.query.name

/** 初始化函数 */
const init = async () => {
    const promiseList: Array<Promise<any>> = []
    imgArr.forEach((value) => {
        promiseList.push(preloadImg(value as string))
    })

    const res = await Promise.all(promiseList)

    a = true
    if (inApp.value || envConfig.RUN_ENV == 'develop') {
        router.replace({ path: `/${routerName ? routerName : 'home'}`, query: {
            hideNavigationBar: 'true',
            type: router.currentRoute.value.redirectedFrom?.query?.type }
        })
    } else {
        router.replace({
            path: '/share',
            query: {
                hideNavigationBar: 'true'
            }
        })
    }
}

let a = false
router.beforeEach((to, from, next) => {
    if (!a) {
        next(false)
    } else {
        next()
    }
})

init()
</script>

<style lang="less" scoped>
.lx-load-mark {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999999999;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background: url('@assets/imgs/loadingBg.png') no-repeat;
    background-size: 100% 100%;
    > .loading-img {
        width: 2rem;
    }
    p {
        font-family: PingFang HK, PingFang HK;
        font-weight: 400;
        font-size: 26px;
        color: #4e5b7e;
        line-height: 18px;
    }
}
</style>
