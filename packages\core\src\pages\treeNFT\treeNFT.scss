.treeNFT {
    width: 100vw;
    max-height: 100vh;
    position: relative;
    overflow-x: hidden;
    background-color: #fff;
    font-family: PingFang SC-Bold, PingFang SC;
    h1 {
        font-size: 36px;
        color: #111a41;
        margin-bottom: 30px;
    }
    .equity {
        margin-bottom: 30px;
        .side {
            display: grid;
            grid-gap: 30px;
            grid-template-columns: 100px 1fr;
            grid-template-rows: repeat(auto-fill, 100px);
            border-radius: 16px 16px 16px 16px;
            border: 2px solid #e1e7fd;
            margin-bottom: 20px;
            .left {
                font-size: 24px;
                color: #ffffff;
                background: linear-gradient(270deg, #a3a2ff 0%, #7c74fe 100%);
                border-radius: 16px 0px 0px 16px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .right {
                font-size: 24px;
                color: #6a43d1;
                display: flex;
                align-items: center;
                justify-content: flex-start;
            }
        }
    }

    .flower {
        width: 100%;
        height: 100%;
    }
    .toHome {
        position: fixed;
        top: 80px;
        left: 30px;
        width: 20px;
    }
    .treeNFT__content {
        width: 100vw;
        z-index: 1;
        bottom: 20px;
        padding: 20px;
        background-color: #fff;
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;
        padding-bottom: 172px;
        margin-top: -132px;
        .content__Issuer {
            display: flex;
            align-items: center;
            font-size: 30px;
            img {
                border: 1px solid #f2f5fe;
                border-radius: 100%;
                margin: 0 20px;
                width: 15%;
                height: 100%;
            }
            .color__mylink {
                color: #6a43d1;
            }
            .fs__25 {
                font-size: 25px;
                color: rgb(191, 188, 188);
                margin-top: 5px;
            }
        }
        .issue {
            display: grid;
            grid-gap: 30px;
            grid-template-columns: 84px 1fr;
            grid-template-rows: repeat(auto-fill, 100px);
            .left {
                img {
                    width: 100%;
                }
            }
            .right {
                h2 {
                    font-weight: 500;
                    font-size: 32px;
                    color: #6a43d1;
                }
                p {
                    font-size: 24px;
                    color: #111a41;
                }
            }
        }
        .content {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            background: #f2f5fe;
            border-radius: 24px 24px 24px 24px;
            h3 {
                font-size: 36px;
                color: #111a41;
                text-align: left;
                margin-left: 25px;
            }
            .firstTopTitle {
                border-top-left-radius: 30px;
                display: flex;
                align-items: flex-end;
                height: 100px;
            }
            .lastTopTitle {
                border-top-right-radius: 30px;
                display: flex;
                align-items: flex-end;
                height: 100px;
            }
            .firstBottomTitle {
                border-bottom-left-radius: 30px;
                padding-bottom: 25px;
            }
            .lastBottomTitle {
                border-bottom-right-radius: 30px;
                padding-bottom: 25px;
            }
            .title {
                width: 100%;
                font-size: 25px;
                display: flex;
                border: none;
                .colors {
                    color: #6a43d1;
                }
                li:first-child {
                    padding-top: 25px;
                    padding-left: 25px;
                    width: 25%;
                }
                li:last-child {
                    width: 75%;
                    padding-top: 25px;
                    padding-left: 25px;
                    padding-right: 25px;
                }
            }
        }
        .confirm {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 160px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
        }
        .btn__bottom {
            width: 656px;
            display: block;
            margin: 20px auto 0;
            font-size: 40px;
            color: #fff;
            padding: 15px;
            border-radius: 50px;
            border: none;
            // background:linear-gradient(to right,#7a75ff,#a5a5ff);
            background-color: #6a43d1;
        }
    }
}
