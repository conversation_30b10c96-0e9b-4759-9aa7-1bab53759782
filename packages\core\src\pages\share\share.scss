.home{
    width: 100vw;
    height: 100vh;
    background: #2C992C;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    font-family: PingFang SC-Bold, PingFang SC;
    font-weight: bold;
    padding-top: 160px;
    .bottom{
        position: absolute;
        bottom: 0;
        height: 50%;
        width: 100%;
        background: #16B677;
        .button{
            z-index: 1;
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            bottom: 92px;
            width: 430px;
            height: 84px;
            background: linear-gradient(180deg, #FDDD3E 0%, #FBB629 100%);
            box-shadow: 0px 8px 0px 2px rgba(252,175,40,1), inset 0px 2px 0px 2px rgba(255,242,178,1);
            border-radius: 48px 12px 48px 12px;
            font-size: 36px;
            color: #FFFFFF;
            text-shadow: 0px 0px 12px #FBAC2E;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
    .banner{
        position: relative;
        z-index: 1;
        width: 750px;
        height: 1302px;
        background: url('@assets/TT/share_banner.png') no-repeat;
        background-size: 100% 100%;
        .title{
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            top: -40px;
            width: 498px;
            height: 80px;
            background: url('@assets/TT/share_title.png') no-repeat;
            background-size: 100% 100%;
            font-size: 28px;
            color: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .trip{
            text-align: center;
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            top: 74px;
            font-size: 28px;
            color: #363636;
            line-height: 28px;
        }
        .bubBalls{
            width: 100%;
            position: absolute;
            top: -310px;
            .bub{
                position: absolute;
                left: 35px;
                top: 670px;
                display: flex;
                flex-direction: column;
                align-items: center;
                .ball{
                    width: 98px;
                    height: 98px;
                    background: url('@assets/TT/Icon/bub.png') no-repeat;
                    background-size: 100% 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 32px;
                    font-family: Arial-Bold Italic, Arial;
                    font-weight: 600;
                    color: #22992C;
                    font-style: italic;
                }
                .text{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 145px;
                    position: absolute;
                    top: 95px;
                    text-align: center;
                    font-size: 24px;
                    color: #2A803D;
                    line-height: 28px;
                    white-space: pre-line;
                    // word-break: break-all;
                }
            }
            .bub1{
                left: 100px;
                top: 510px;
            }
            .bub2{
                left: 240px;
                top: 450px;
            }
            .bub3{
                left: 540px;
                top: 510px;
            }
            .bub4{
                left: 605px;
                top: 670px;
            }
            .bub5{
                left: 400px;
                top: 450px;
            }
        }
        .card{
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            top: 618px;
            width: 650px;
            // height: 444px;
            background: #FFFFFF;
            padding: 92px 50px 34px 50px;
            border-radius: 48px 48px 48px 48px;
            .b_title{
                width: 546px;
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                top: 0;
            }
            .c_title{
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                top: 13px;
                font-size: 32px;
                color: #FFFFFF;
            }
            .contain{
                width: 100%;
                height: 100%;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                .row{
                    width: 100%;
                    display: flex;
                    align-items: flex-start;
                    justify-content: space-between;
                    .ab{
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        
                        .aword{
                            width: 102px;
                            height: 102px;
                            background: #fff;
                            opacity: 1;
                            border: 6px solid #A3EC8F;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            
                            position: relative;
                            img{
                                max-width: 90%;
                                max-height: 90%;
                            }
                            
                        }
                        .propName{
                            width: 150px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            text-align: center;
                            font-size: 20px;
                            font-family: PingFang SC-Medium, PingFang SC;
                            font-weight: 500;
                            color: #4E5B7E;
                            line-height: 36px;
                            margin-bottom: 22px;
                        }
                        .zhuoyi{
                            align-items: flex-end;
                            img:nth-child(1){
                                width: 35px;
                                margin-bottom: 10px;
                            }
                            img:nth-child(2){
                                width: 40px;
                                margin-bottom: 10px;
                            }
                        }
                    }
                    .col-title{
                        background: #D5FAD7;
                        border-radius: 26px 26px 26px 26px;
                        width: 152px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 9px 0;
                        font-size: 24px;
                        font-family: PingFang TC-Medium, PingFang TC;
                        font-weight: 500;
                        color: #22992C;
                        margin-bottom: 20px;
                    }
                    
                }
                
            }
        }
        
    }
}