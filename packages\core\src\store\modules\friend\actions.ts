import { useApi, useToast,useRouter } from 'hook'
import { TaskFriendType, friendType } from './type'
const { router } = useRouter()
const { authRequest, authGet, authPost } = useApi()
//相关方法
//好友列表
async function getFriendList(this:friendType) {
    return await authGet('/api/sdk/treetask/friend/list').then((res)=>{
        this.friendList = res.friends || []
        return res;
    }).catch((err)=>{
        return err
    })
}

async function getFlowerFriendList(this:friendType) {
    return await authGet('/api/sdk/treetask/flower/friend/list')
    .then((res)=>{
        this.flowerFriendList = res.friends
        console.log(res);
        return res;
    }).catch((err)=>{
        return err
    })
}

async function giveFlowers(friend_third_id,tree_id) {
    return await authRequest('PATCH','/api/sdk/treetask/flower/send',{
        params: {
            friend_third_id,
            tree_id
        }
    })
    .then(res=>{
        if (!res.success) {
            useToast().toast(res.failed_msg)
        }else{
            window.localStorage.setItem('pushSuccess','true')
            router.go(-1)
        }
    })
    .catch(err=>{
        useToast().toast(err.msg)
    })
}

async function giveFlowersActivity() {
}

async function toFriendPage(this: friendType, id: number|string) {
    return await authGet('/api/sdk/treetask/friend/info', {
        params: {
            friend_third_id: id
        }
    }).then((res)=>{
        return res;
    }).catch((err)=>{
        return err
    })
}

function changeIsOwn(this: friendType, boo: boolean){
    this.isOwn = boo
}

async function friendActives(id: number|string) {
    return await authGet('/api/sdk/energy/friend/actives', {
        params: {
            friend_third_id: id
        }
    }).then((res)=>{
        return res;
    }).catch((err)=>{
        return err
    })
}

// 减碳值动态
async function friendActivity(this: friendType) {
    await authGet('/api/sdk/treetask/friend/energy/activity').then((res)=>{
        this.activityList = activityListHandle(res.activities)
        console.log(this.activityList, '动态')
        window.localStorage.setItem('day',res.activities[0].day)
        return res;
    }).catch((err)=>{
        return err
    })
    await authGet('/api/sdk/treetask/flower/activity')
    .then(res=>{
        let activities = res.activities.slice(0, 2)
        activities.forEach((act)=>{
            let tempAct = this.activityList.find((item)=>item.day==act.day)
            let tempIndex = this.activityList.indexOf(tempAct) !== -1 ? this.activityList.indexOf(tempAct) : this.activityList.length
            if (act.receive_activity) {
                if(!this.activityList[tempIndex]){
                    this.activityList[tempIndex] = {}
                }
                if (!this.activityList[tempIndex].os) {
                    this.activityList[tempIndex].os = {}
                }
                act.receive_activity.forEach((value)=>{
                    if (!this.activityList[tempIndex].os[value.third_id + value.create_time.replace(' ', '')]) {
                        this.activityList[tempIndex].os[value.third_id + value.create_time.replace(' ', '')] = {}
                    }
                    this.activityList[tempIndex].os[value.third_id + value.create_time.replace(' ', '')].flower_type = value.flower_type
                    this.activityList[tempIndex].os[value.third_id + value.create_time.replace(' ', '')].name = value.name
                    this.activityList[tempIndex].os[value.third_id + value.create_time.replace(' ', '')].head_logo = value.head_logo
                    this.activityList[tempIndex].os[value.third_id + value.create_time.replace(' ', '')].create_time = value.create_time
                })
                //
                for (const key in this.activityList[tempIndex].os) {
                    this.activityList[tempIndex].os[key].third_id = key
                }
            }
            if (act.send_activity) {
                if(!this.activityList[tempIndex]){
                    this.activityList[tempIndex] = {}
                }
                if (!this.activityList[tempIndex].as) {
                    this.activityList[tempIndex].as = {}
                }
                act.send_activity.forEach((value)=>{
                    if (!this.activityList[tempIndex].as[value.third_id + value.create_time.replace(' ', '')]) {
                        this.activityList[tempIndex].as[value.third_id + value.create_time.replace(' ', '')] = {}
                    }
                    this.activityList[tempIndex].as[value.third_id + value.create_time.replace(' ', '')].flower_type = value.flower_type
                    this.activityList[tempIndex].as[value.third_id + value.create_time.replace(' ', '')].name = value.name
                    this.activityList[tempIndex].as[value.third_id + value.create_time.replace(' ', '')].head_logo = value.head_logo
                    this.activityList[tempIndex].as[value.third_id + value.create_time.replace(' ', '')].create_time = value.create_time
                })
                for (const key in this.activityList[tempIndex].as) {
                    this.activityList[tempIndex].as[key].third_id = key
                }
            }
            if (!this.activityList[tempIndex].day) {
                this.activityList[tempIndex].day = act.day
                this.activityList[tempIndex].other_energy_value = 0
                this.activityList[tempIndex].self_energy_value = 0
            }
            console.log(this.activityList);
        })
        console.log(res,'------------------------------------');
    })
}

function activityListHandle(res: any[]){
    let result:any = res.slice(0, 2)
    result.forEach(ele => {
        let as = {}
        let os = {}
        ele.self_activity.forEach(item => {
            if(typeof(as[item.linked_friend.third_id]) == 'undefined'){
                as[item.linked_friend.third_id] = {}
                as[item.linked_friend.third_id].total = 0
                as[item.linked_friend.third_id].list = []
                as[item.linked_friend.third_id].name = item.linked_friend.name
                as[item.linked_friend.third_id].headLogo = item.linked_friend.head_logo ? item.linked_friend.head_logo : 'https://mylink.oss-cn-hongkong.aliyuncs.com/ico/sidebar/img_sidebar_profilephoto_unlogin.png'
            }
            as[item.linked_friend.third_id].total += item.energy_value
            as[item.linked_friend.third_id].list.push(item)
            as[item.linked_friend.third_id].create_time = as[item.linked_friend.third_id].list.sort((a,b)=>b-a)[0].created_at
        })
        ele.other_activity.forEach(item => {
            if(typeof(os[item.linked_friend.third_id]) == 'undefined'){
                os[item.linked_friend.third_id] = {}
                os[item.linked_friend.third_id].total = 0
                os[item.linked_friend.third_id].list = []
                os[item.linked_friend.third_id].name = item.linked_friend.name
                os[item.linked_friend.third_id].headLogo = item.linked_friend.head_logo ? item.linked_friend.head_logo : 'https://mylink.oss-cn-hongkong.aliyuncs.com/ico/sidebar/img_sidebar_profilephoto_unlogin.png'
            }
            os[item.linked_friend.third_id].total += item.energy_value
            os[item.linked_friend.third_id].list.push(item)
            os[item.linked_friend.third_id].create_time = os[item.linked_friend.third_id].list.sort((a,b)=>b-a)[0].created_at
        })
        ele.as = as
        ele.os = os
    });
    return result
}

// 跑马灯
async function friendMarquee(this: friendType) {
    return await authGet('/api/sdk/treetask/friend/energy/marquee').then((res)=>{
        this.marqueeList = res.activities
        // 开发环境模拟加好友
        // this.marqueeList.push({
        //     "energy": null,
        //     "notice": {
        //       "apply_id": "apply_id",
        //       "apply_remark": "apply_remark",
        //       "head_logo": "",
        //       "name": "加好友测试",
        //       "phone": "12312312",
        //       "third_id": "2f16213ea0224b97a7264a3e619ac85b"
        //     }
        // })
        this.noticeCount = res.notice_count
        // this.noticeCount = 1
        return res;
    }).catch((err)=>{
        return err
    })
}

// 下一个好友
async function friendNext() {
    return await authGet('/api/sdk/treetask/friend/energy/next').then((res)=>{
        return res;
    }).catch((err)=>{
        return err
    })
}

// 领取好友减碳值
async function friendClick(energy_keys: Array<String>, id: number|string) {
    energy_keys = Array.from(new Set(energy_keys))
    return await authPost('/api/sdk/treetask/friend/energy/click', {
        data: {
            energy_keys,
            friend_third_id: id
        }
    }).then((res)=>{
        return res;
    }).catch((err)=>{
        if (err.msg) {
            useToast().toast(err.msg)
        }
        console.log(err)
    })
}

async function propsHandle(this: friendType, propsArr: Array<any>) {
    let arr = propsArr.filter((item) => item.code.replace(/(\d)+/g, '') == 'item')
    if(arr.length == 0 ) this.propsConfig['挂饰'] = [null, null, null]
    this.propsConfig['铲子'] = null
    this.propsConfig['围栏'] = null
    this.propsConfig['灯柱'] = null
    this.propsConfig['稻草人'] = null
    this.propsConfig['木头车'] = null
    this.propsConfig['喷水池'] = null
    this.propsConfig['桌子'] = null
    this.propsConfig['椅子'] = null
    this.propsConfig['GIF'] = null
    propsArr.forEach((item) => {
        let code = item.code.replace(/(\d)+/g, '')
        switch(code){
            case 'item':
                for(let mm in this.propsConfig['挂饰']){
                    if(this.propsConfig['挂饰'][mm] == null) continue
                    let len = arr.filter((i) => i.code == this.propsConfig['挂饰'][mm].code)
                    if(len.length == 0){
                        this.propsConfig['挂饰'][mm] = null
                    }
                }
                for(let tt of arr){
                    let len = this.propsConfig['挂饰'].filter((i) => {
                        if(i == null) return
                        return  i.code == tt.code
                    }) 
                    if(len.length == 0){
                        for(let t in this.propsConfig['挂饰']){
                            if(this.propsConfig['挂饰'][t] == null){
                                this.propsConfig['挂饰'][t] = tt
                                break
                            }
                        }
                    }
                }
                break
            case 'chanzi':
                this.propsConfig['铲子'] = item.code
                break
            case 'weilan':
                this.propsConfig['围栏'] = item.code
                break
            case 'dengzhu':
                this.propsConfig['灯柱'] = item.code
                break
            case 'daocaoren':
                this.propsConfig['稻草人'] = item.code
                break
            case 'mutouche':
                this.propsConfig['木头车'] = item.code
                break
            case 'penshuichi':
                this.propsConfig['喷水池'] = item.code
                break
            case 'zhuozi':
                this.propsConfig['桌子'] = item.code
                break
            case 'yizi':
                this.propsConfig['椅子'] = item.code
                break
            default://GIF情况
                if (!item.code.includes('title')) {
                    this.propsConfig['GIF'] = item.code
                }
                break
        }
    })
    console.log(this.propsConfig, '好友的道具列表')
}


export default {
    propsHandle,
    friendClick,
    getFriendList,
    toFriendPage,
    changeIsOwn,
    friendActives,
    friendActivity,
    friendMarquee,
    friendNext,
    getFlowerFriendList,
    giveFlowers,
    giveFlowersActivity
}
