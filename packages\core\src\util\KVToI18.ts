// import api from '@api/index'
// import { useEnvConfig } from 'hook'
// import { reactive } from 'vue'
// import res from "../../public/js/73-proactivity.json";

// let { axios, get, post } = api
// let envConfig = useEnvConfig()

// let scDate = reactive({})
// let tcDate = reactive({})
// let enDate = reactive({})
// let isPat = envConfig.RUN_ENV == 'production' || envConfig.RUN_ENV == 'beta' ? true : false
// let kvUrl = isPat ? '/73-proactivity.json?v=' : '/73-testactivity.json?v='

// const key = ['plantKey', 'propKey', 'bearKey']

// axios.defaults.baseURL = 'https://mylink-via.oss-accelerate.aliyuncs.com/entry/'

// async function init() {
//     console.log(res);
    
//     res.categories.forEach((e: any) => {
//         let arr = [scDate, tcDate, enDate]
//         addAttribute(arr, e.nameEn, {}, false)
//         for (let i = 0; i < arr.length; i++) {
//             arr[i] = arr[i][`${e.nameEn}`]
//         }

//         e.commodities.forEach((el: any) => {
//             // console.log(JSON.parse(el.des))
//             let obj = JSON.parse(el.des)
//             let flag = false
//             let nowArr: any = []

//             obj.forEach((val: any) => {
//                 if (!flag && val.key == key[0]) {
//                     addAttribute(arr, val.value.tc, {}, false)
//                     flag = true
//                     nowArr = arr.map((v) => v[`${val.value.tc}`])
//                 } else {
//                     // console.log(val.key, val.value)
//                     addAttribute(nowArr, val.key, val.value, true)
//                 }
//             })
//         })

//         key.shift()
//     })
//     console.log(scDate, tcDate, enDate)

//     function addAttribute(obj: Array<object>, name: string, value: any, flag: boolean) {
//         if (!value || (flag && !value.sc && !value.tc && !value.en)) return
//         obj[0][`${name}`] = value.sc !== undefined ? value.sc : value instanceof Object ? Object.create(null) : value
//         obj[1][`${name}`] = value.tc !== undefined ? value.tc : value instanceof Object ? Object.create(null) : value
//         obj[2][`${name}`] = value.en !== undefined ? value.en : value instanceof Object ? Object.create(null) : value
//         // if (flag) console.log(obj)
//     }
// }

// export { init, scDate, tcDate, enDate }
