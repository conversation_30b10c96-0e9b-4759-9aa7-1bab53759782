{"compileOnSave": false, "compilerOptions": {"outDir": "lib", "target": "esnext", "module": "esnext", "strict": true, "declaration": true, "declarationDir": "types", "charset": "utf8", "jsx": "preserve", "noImplicitAny": false, "allowJs": false, "noUnusedLocals": false, "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "types": [], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules"]}