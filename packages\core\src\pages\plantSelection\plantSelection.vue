<template>
    <div class="plantSelection">
        <navComponent :returnType="2" :showTransmit="false" :showStrategy="true"/>
        <div  class="row">
            <div v-for="(item, index) in plantArr" :key="index" class="item" @click="goPlantDetail(item)">
                <div class="new" v-if="item.new">
                    <p>new</p>
                </div>
                <div class="img-box">
                    <div v-if="item.req_vip" class="vipIcon">
                        <img :src="$imgs[`plantSelection/vip-${$langValue.value}.png`]" />
                        <!-- <img src="@imgs/plantSelection/vip.png" alt="">
                        <p>{{state.home.会员}}</p> -->
                    </div>
                    <img v-if="item.req_vip" class="img" src="@imgs/plantSelection/vip-card.png" />
                    <img v-else-if="!item.req_vip" class="img" src="@imgs/plantSelection/tree-card.png" />
                    <div class="tree" >
                        <img :class="`${item.code}`" :src="$imgs[`TT/state/${item.code}/s9.png`]" alt="">
                    </div>
                    
                </div>

                <div class="info">
                    <div class="name">{{ state.plant[item.code].plantName }}</div>

                    <div class="kind">{{ state.plant[item.code].plantKind }}</div>
                </div>

                <div class="price">
                    <!-- <div class="value">
                        植株稀有度:
                        <img
                            v-for="i in Number(state.plant[item.code].plantValue)"
                            :key="i"
                            class="star"
                            src="@imgs/plantSelection/star.png"
                        />                       
                    </div> -->

                    <div class="target">{{ state.detail.目标减排值 + numberToThousands(item.target_energy_total) }}</div>
                </div>
                <div class="btn log" v-if="!useUserStore().inLogin" @click.stop="goToLog(item)">{{ state.home.登入并种植 }}</div>
                <div class="btn" v-else-if="useUserStore().inLogin" :class="(!(item.completed || item.owned)) 
                && (item.req_vip ? (useUserStore().isVip == true ? true : false) : true)
                ? 'can' : 'cannot'" @click.stop="goToPlant(item)">{{state.home.去种植}}</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onBeforeMount } from 'vue'
import { numberToThousands, getTreeList as getTList } from '@unity/unity'
import { useRouter, useToast, useDialog, useLang, useLoading } from 'hook'
import { useTaskStore, useTreeStore, useUserStore } from '@/store'
import PropDialog from '@/components/propDialog.vue'
import confirmDialog from '@/components/confirmDialog.vue'
import successPlant from '@/components/successPlant.vue'
import navComponent from '@/components/navComponent.vue';
import notVipDialog from '@/components/notVipDialog.vue';
const dialog = useDialog({ PropDialog, confirmDialog, successPlant, notVipDialog })
const { toast } = useToast()
const { state } = useLang()
const { router } = useRouter()
const { loading } = useLoading()
const { getTreeList, plantTree,getTreeListNoLogin } = useTaskStore()
let { isVip } = useUserStore()
const plantArr: any = ref([])

onBeforeMount(() => {
    init()
})

let info = ref()
const init = async() => {
    let list:any
    if(useUserStore().inLogin){
        list = (await getTreeList()).trees
        useTreeStore().treeList.trees = list
        info.value = await useTaskStore().getInfo()
    }else{
        list = (await getTreeListNoLogin()).trees
        useTreeStore().treeList.trees = list
        // list = getTList()
    }
    list.forEach(three => {
        if ((three.code === 'mumian' || three.code === 'yumushu') && !info.value?.webui_manage_values?.first_new_three) {
            three.new = true
        }
        if ((three.code === 'shuishirong' || three.code === 'huangzhongmu') && !info.value?.webui_manage_values?.two_new_three) {
            three.new = true
        }
    });
    // let vipList = list.filter((item) => item.req_vip == true && !item.new).sort((a, b) => a.target_energy_total - b.target_energy_total)
    // let unVipList = list.filter((item) => item.req_vip == false && !item.new).sort((a, b) => a.target_energy_total - b.target_energy_total)
    // let newVipList = list.filter((item) => item.req_vip == true && item.new).sort((a, b) => a.target_energy_total - b.target_energy_total)
    // let newUnVipList = list.filter((item) => item.req_vip == false && item.new).sort((a, b) => a.target_energy_total - b.target_energy_total)
    plantArr.value = list
    // console.log(vipList, unVipList, 'vipList')
    useUserStore().setUpData('first_new_three', '1')
    useUserStore().setUpData('two_new_three', '1')
}

function goPlantDetail(item: any) {
    if(item.req_vip && isVip == false && useUserStore().inLogin){
        return
    }
    router.push({ name: 'plantDetail', params: { id: item.tree_id }, query: {"hideNavigationBar": 'true'}})
}

function goToPlant(item: any) {
    if(item.req_vip && isVip == false){
        // toast(state.dialog.只有VIP会员可以种.replace("{x}", state.plant[item.code].plantName))
        // dialog.get('notVipDialog').show({treeCode:item.code}, { maskClose:false })
        return
    }else if(item.completed || item.owned){
        toast(state.dialog.你已经种过x啦.replace("{x}", state.plant[item.code].plantName))
        return
    }
    let confirmDlg = dialog.get('confirmDialog')
    confirmDlg.show({ plantName: state.plant[`${item.code}`].plantName }, { maskClose:false })
    confirmDlg.on('cancel', () => cancel(confirmDlg))
    confirmDlg.on('confirm', () => confirm(confirmDlg, item))
    return
}

const goToLog = async(item: any) => {
    loading('open')
    await useUserStore().login()
    if(useUserStore().inLogin){
        let info = await useTaskStore().getInfo()
        useUserStore().changeVip(info.user.vip)
        isVip = info.user.vip
        if(info.tree){//种树了
            // router.replace({ name: 'home', query: {"hideNavigationBar": 'true'}})
            router.go(-1)
        }else{
            goToPlant(item)
        }
        console.log(info)
    }
    init()
    loading('close')
}

const cancel = (confirmDlg: any) =>{
    confirmDlg.close()
} 

const confirm = (confirmDlg: any, item: any) =>{
    confirmDlg.close()
    plantTree(item.tree_id)
    .then((res) => {
        useTaskStore().upLevelPlantList = res.user_actions
        useTreeStore().changeHasPlanted(true)
        useTreeStore().changePlanted(true)
        let successPlant = dialog.get('successPlant')
        successPlant.show({ plantName: state.plant[`${item.code}`].plantName, treeCode: item.code }, { maskClose:false })
        successPlant.on('toHome', () => {
            successPlant.close()
            useTreeStore().changeHasPlanted(true)
            useTreeStore().changePlanted(true)
            router.back()
        })
    })
    .catch((err) => {
        toast(state.netError)
    })
} 
</script>

<style lang="less" scoped>
@import './plantSelection.scss';
</style>
