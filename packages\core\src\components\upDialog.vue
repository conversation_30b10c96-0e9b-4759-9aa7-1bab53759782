<template>
  <div v-if="to != 10" class="box">
      <p class="congrate">{{state.dialog.恭喜升到x级啦.replace('{x}', props.to)}}</p>
      <div class="jiantou"></div>
      <p class="continue">{{state.dialog.点击屏幕返回}}</p>
      <div class="from pic">
          <img :class="`s${from}`" :src="img[`TT/state/${treeCode}/s${from}.png`]" alt="">
          <p class="name">{{state.plantStage[from - 1]}}</p>
      </div>
      <div class="to pic">
          <img :class="`s${to}`" :src="img[`TT/state/${treeCode}/s${to}.png`]" alt="">
          <p class="name">{{state.plantStage[to - 1]}}</p>
      </div>
  </div>
  <!-- <div v-if="to == 10" class="box box1">
      <p class="congrate">{{state.dialog.恭喜获得x种植成就.replace('{x}', state.plant[treeCode].plantName)}}  </p>
      <p class="continue">{{state.dialog.点击屏幕返回}}</p>
      <achieve class="achievement" type='big' :treeCode='treeCode'/>
  </div> -->
</template>

<script setup>
import { useLang, useStorage, useEnvConfig, useToast } from 'hook'
import { defineComponent, onMounted, ref, watch, getCurrentInstance } from 'vue'
import achieve from './achieve.vue'
const { state } = useLang()
const props = defineProps({
  from: {
      type: Number,
      required: true
  },
  to: {
      type: Number,
      required: true
  },
  treeCode:{
      type: String,
      required: true
  }
})

const cns = getCurrentInstance()
const img = cns.appContext.config.globalProperties.$imgs
const arr = ref([
    '種子一期',
    '種子二期',
    '發芽期',
    '成苗一期',
    '成苗二期',
    '發育一期',
    '發育二期',
    '成熟一期',
    '成熟二期',
    '种植完成'
])

</script>

<style lang='less' scoped>

.box{
    width: 570px;
    height: 384px;
    position: relative;
    background: url('@assets/TT/upbg.png') no-repeat;
    background-size: 100% 100%;
    .achievement{
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
    }
    .congrate{
        text-align: center;
        z-index: 2;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        top: 10px;
        font-size: 32px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #FFFFFF;
        min-width: 340px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .jiantou{
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: 122px;
        width: 70px;
        height: 43px;
        background: url('@assets/TT/Icon/jt.png') no-repeat;
        background-size: 100% 100%;
    }
    .continue{
        white-space: nowrap;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        bottom: -40px;
        font-size: 28px;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 28px;
    }
    .pic{
        position: absolute;
        bottom: 60px;
        width: 144px;
        // height: 250px;
        // overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        img{
            flex-shrink: 0;
            margin-bottom: 16px;
            // transform: scale(0.6);
        }
        .s1 {
            max-width: 40px;
        }
        .s2 {
            max-height: 90px;
            max-width: 60px;
        }
        .s3 {
            max-width: 75px;
        }
        .s4 {
            max-width: 110px;
            max-height: 140px;
        }
        .s5 {
            max-width: 125px;
            max-height: 150px;
        }
        .s6 {
            max-width: 150px;
        }
        .s7 {
            max-width: 180px;
        }
        .s8 {
            max-width: 200px;
        }
        .s9 {
            max-width: 200px;
        }
        .name{
            white-space: nowrap;
            font-size: 24px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #4E5B7E;
        }
    }
    .from{
        left: 60px;
    }
    .to{
        right: 60px;
    }
}
.box1{
    background: none;
    .congrate{
        top: -150px;
    }
}
</style>