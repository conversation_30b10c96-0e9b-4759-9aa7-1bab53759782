<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />
  </head>
  <style>
    /* html {
      font-size: calc(75 / 750 * 100vw);
    } */
    body{
      -webkit-overflow-scrolling:touch;
    }
    html, body {
      overscroll-behavior: none; /* 阻止默认的下拉和上拉行为 */
    }
  </style>
  <script src="/js/rem.js"></script>
  <script>
    window.__vite_env__ = <%- __vite_env__ -%>;
    let lang = window.location.href.indexOf('lang=en')!=-1 ? 'en' : 'sc'
    try{
      let test=new Proxy({},{
        get:function(){
          console.log(1)
        }
      })
    }
    catch(err){
      if(lang === 'en') alert('Sorry, this activity cannot be viewed in IE browser, please change to another browser.')
      else alert('非常抱歉，該活動無法用IE瀏覽器查看，請更換其他瀏覽器。')
    }
  </script>
  <%- injectScriptBeforeBody -%>
  <body>
    <div id="app"></div>
    <div id="app-dialog"></div>
    <div id="app-toast"></div>
    <div id="app-loading"></div>
    <script src="/js/<EMAIL>"></script>
    <%- injectScriptAfterVue -%>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
