import { reactive, Ref, ref, watch } from 'vue'
import { createI18n } from 'vue-i18n'
import langAssets, { Lang } from './assets/index'
import Formatter from './format'
import { useWindow } from '../useWindow'

const formatter = new Formatter()

const { lang } = useWindow().mQuery

const currentLang = ref<'sc' | 'tc' | 'en'>(lang || window.sessionStorage.getItem('lang') || 'tc')

const i18n = createI18n({
  locale: currentLang.value
})

type State = Lang & UseLang.Lang

const state: State = reactive<State>(langAssets.tc)

const setLang = (locale: 'sc' | 'tc' | 'en') => {
  currentLang.value = i18n.global.locale = locale || 'tc'
  Object.assign(state, langAssets[currentLang.value], i18n.global.messages[currentLang.value])
  window.sessionStorage.setItem('lang', currentLang.value)
  console.log('当前语言', currentLang.value)
}

const setMessages = (messages: Record<'sc' | 'tc' | 'en', UseLang.Lang>) => {
  const keys = Object.keys(messages)
  keys.forEach((key) => {
    i18n.global.setLocaleMessage(key, messages[key])
  })
  Object.assign(state, langAssets[currentLang.value], i18n.global.messages[currentLang.value])
}

setLang(currentLang.value)

const onLangChange = (cb: (lang: Ref<'sc' | 'tc' | 'en'>) => void) => {
  watch(
    currentLang,
    () => {
      cb(currentLang)
    },
    { immediate: true }
  )
}

function format(message: string, values: any) {
  return formatter.interpolate(message, values)
}

export function useLang() {
  return {
    state,
    lang: currentLang,
    setLang,
    onLangChange,
    format,
    setMessages
  }
}

export type LangState = State
export type LangValue = typeof currentLang
export type LangFormat = typeof format

export namespace UseLang {
  export interface Lang {}
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $lang: LangState
    $langValue: LangValue
    $langFormat: LangFormat
  }
}
