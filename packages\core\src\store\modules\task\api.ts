import { useEnvConfig, useApi, useLang } from 'hook'
import { GetBuyHistoryReturnType } from './type'
import { inApp, getUserInfo, logEventStatistics, AliLog, getSystem } from '@via/mylink-sdk'

const { authPost, authGet, authRequest, get, post } = useApi()
const { lang } = useLang()
const envConfig = useEnvConfig()
// 设置引导完成标志
function guild_completed() {
    return authRequest('PATCH', '/api/sdk/treetask/update/guild_completed', {})
}

//获取可点击的减碳球
function getEnergy(stepNum: number = 0) {
    //nft
    return authGet('/api/sdk/energy/actives', {
        headers: {
            lang: lang.value
        },
        params: {
            steps: stepNum
        }
    })
}

// 点击减碳球获取减碳值
function clickEnergy(energy_keys: Array<String>) {
    return authPost('/api/sdk/treetask/energy/click', {
        data: {
            energy_keys
        }
    })
}

// 获取用户信息
function getInfo() {
    return authGet('/api/sdk/treetask/info')
}

// 获取功能卡道具
function getPull() {
    return authGet('/api/sdk/treetask/pull')
}

// 未登录时获取道具列表
function getShareList(target: Number = 0, bag: Number = 0) {
    return get('/api/sdk/treetask/item/list/shared', {
        params: {
            target: target,
            bag: bag
        }
    })
}

// 获取道具列表
function getPropList(target: Number = 0, bag: Number = 0) {
    return authGet('/api/sdk/treetask/item/list', {
        params: {
            target: target,
            bag: bag
        }
    })
}

// 获取树列表
function getTreeList() {
    return authGet('/api/sdk/treetask/tree/list')
}

// 未登录时获取树列表
function getTreeListNoLogin() {
    return get('/api/sdk/treetask/tree/list/shared')
}

// 种树
function plantTree(tree_id: Number) {
    return authPost('/api/sdk/treetask/plant', {
        data: {
            tree_id
        }
    })
}

// 主页
function main() {
    return authGet('/api/sdk/task/main')
}

// 分享信息
function shared() {
    return authGet('/api/sdk/task/shared')
}

// 减碳值领取记录
function getRecord(startTime: String, endTime: String) {
    //nft
    return authGet('/api/sdk/treetask/energy/click/history', {
        params: {
            first_month: startTime,
            last_month: endTime
        },
        headers: {
            lang: lang.value
        }
    })
}

// 获取购买记录
function getBuyHistory(startTime: string, endTime: string) {
    return authGet<GetBuyHistoryReturnType>('/api/sdk/treetask/commodity/buy/history', {
        params: {
            first_month: startTime,
            last_month: endTime
        },
        headers: {
            lang: lang.value
        }
    })
}

// 任务列表
function getTaskList() {
    return authGet('/api/sdk/treetask/task/list')
}
// 领取任务
function getAwards(task_id) {
    return authRequest('PATCH', '/api/sdk/treetask/task/receive_reward', {
        params: {
            task_id
        }
    })
}

// 任务状态
function getTaskState() {
    return authGet('/api/sdk/treetask/task/state')
}

// 领取上期任务
function getPreviousReward() {
    return authRequest('PATCH', '/api/sdk/treetask/task/receive_previous_rewards')
}

/**
 * 获取盲盒红点状态
 */
async function getBlindBoxState() {
    let token = envConfig.RUN_ENV == 'develop' ? envConfig.DEFAULT_TOKEN : ''
    // TODO 以下这段代码需要到UAT环境进行测试
    if (envConfig.RUN_ENV != 'develop') {
        const userInfo = await getUserInfo(false)
        if (userInfo && userInfo.authorization) {
            return authGet('/api/sdk/treetask/blindbox/state', {
                headers: {
                    'mylink-user-token': userInfo.authorization
                }
            })
        } else {
            console.warn('缺少token')
        }
    } else {
        // TODO 以下代码迁移到统一请求中
        return authGet('/api/sdk/treetask/blindbox/state', {
            headers: {
                'mylink-user-token': token
            }
        })
    }
}

/**
 * 获取nft权益状态
 */
function getNFTEquityState() {
    return authGet('/api/sdk/treetask/nft/equity/state')
}

/**
 * nft权益数据清除
 */
function nftNewestOff() {
    return authRequest('PATCH', '/api/sdk/treetask/nft/newest/off')
}

function changeNewest(target:number) {
    return authRequest('PATCH','/api/sdk/treetask/item/newest/off',{
        params:{
            target
        }
    })
}

export {
    getEnergy,
    clickEnergy,
    getInfo,
    getPropList,
    getShareList,
    getTreeList,
    getTreeListNoLogin,
    plantTree,
    main,
    shared,
    getRecord,
    guild_completed,
    getPull,
    getBuyHistory,
    getTaskList,
    getAwards,
    getTaskState,
    getPreviousReward,
    getBlindBoxState,
    getNFTEquityState,
    changeNewest,
    nftNewestOff
}
