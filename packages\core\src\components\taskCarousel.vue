<template>
    <!-- 限定任务 -->
    <div class="taskMain" v-if="taskList.length">
        <!-- 任务 -->
        <van-swipe vertical class="swipeStyle" :show-indicators="false" :touchable="false" autoplay="5000"
            v-if="virtualBub">
            <van-swipe-item v-for="item in taskList" :key="item.task_id">
                <div class="taskList">
                    <div class="taskTop">
                        <img :src="$imgs[`task/${item.code}.png`]" alt="" />
                        <div class="listTitle">
                            <div class="headline">{{ item.metadata[langValue['title' + lang]] }}</div>
                            <div class="subtitle" v-html="item.metadata[langValue['content' + lang]].replace(
                                `${item.energy_value}g`,
                                `<span style='color:#22992C'>${item.energy_value}g</span>`)"></div>
                        </div>
                        <div class="listRight">
                            <div :class="item.status == 0 ? 'tofiniosh' : item.status == 1 ? 'claim' : 'success'"
                                class="listBtn" @click="getClaim(item)">
                                {{
                                    item.status == 0 ? state.task.去完成 : item.status == 1 ? state.task.待領取 : state.task.已完成
                                }}
                            </div>
                        </div>
                    </div>
                </div>
            </van-swipe-item>
            <van-swipe-item v-for="item in useTaskStore().banner" :key="item.banner_id">
                <img :src="item.image_url" alt="" @click="goLink(item.jump_url)">
            </van-swipe-item>
        </van-swipe>
        <div class="swipeStyle" v-if="!virtualBub">
            <div class="taskList">
                <div class="taskTop">
                    <img :src="$imgs[`task/login.png`]" alt="" />
                    <div class="listTitle">
                        <div class="headline">{{ state.task.每日登录 }}</div>
                        <div class="subtitle">{{ state.task.每日登录文案 }}</div>
                    </div>
                    <div class="listRight">
                        <div :class="'tofiniosh'" class="listBtn">
                            {{
                                state.task.去完成
                            }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, watch } from 'vue'
import { useTaskStore, useTreeStore, useUserStore } from '@/store'
import { TaskListType } from '@/store/modules/task/type'
import { useLang, useEventBus, useToast, useLoading, useEnvConfig } from 'hook'
import { logEventStatistics } from '@via/mylink-sdk'
import router from '@/router'
import { jumpMyGame } from '@/util/mygame-sdk'
const { state, lang } = useLang()
const loading = useLoading()
const { getTaskList, getAwards } = useTaskStore()
const props = defineProps({
    taskList: {
        type: Array<TaskListType>,
        required: true,
        default: []
    },
    virtualBub: {
        type: Boolean,
        default: false
    }
})
let eventBus = useEventBus()

const langValue = {
    titletc: 'title',
    titlesc: 'titleSc',
    titleen: 'titleEn',
    contenttc: 'content',
    contentsc: 'contentSc',
    contenten: 'contentEn'
}

const taskList = ref<any[]>()

// 领取奖励
const getClaim = async (item) => {
    // if (!useTreeStore().nowTree.tree) {
    //     useToast().toast(state.task.需開始種植才可進行任務)
    //     return
    // }
    if (item.status == 1) {
        logEventStatistics('garden_task01_collect_click')
        loading.loading('open')
        const result = await getAwards(item.task_id)
        loading.loading('close')
        console.log(result, '----------------------------------------------------');
        eventBus.emit('upLevelFun', result)
        if (result.user_actions) {
            if (item.code == "login_days" || item.code == 'buy-commodity') {
                useToast().toast(state.task.領取雙倍卡)
            } else {
                item.energy_type == 0 ? useToast().toast(state.task.已成功領取 + item.energy_value + state.task.g減碳值) : useToast().toast(state.task.已成功領取 + item.save_energy_value + state.task.g減碳值)
            }
            await useTaskStore().getInfo()
                .then(res => {
                    console.log(res.user.energy_total, '总数');
                    useTreeStore().changeEnergyTotal(res.user.energy_total)
                })
        } else {
            useToast().toast(state.task.發生異常)
        }
        eventBus.emit('rolandTaskState')
    }

    if (item.status == 0) {
        logEventStatistics('garden_task01_go_click')
        let linkUrl = item.metadata.linkUrl
        if (linkUrl.indexOf('internal-jump://') == -1) {
            if (item.metadata.linkType && item.metadata.linkType === 'mygame') {
                jumpMyGame(useEnvConfig().RUN_ENV == 'uat' || useEnvConfig().RUN_ENV == 'develop',item.metadata.linkValue)
            }else{
                location.href = linkUrl
            }
        } else {
            let jumpUrl = linkUrl.replace('internal-jump://', '')
            if (jumpUrl.indexOf('home') !== -1) {
                eventBus.emit('closeTask')
            } else {
                router.push(jumpUrl)
            }
        }
    }
}

watch(props, () => {
    // 切分任务
    taskList.value =
        props.taskList &&
        props.taskList.filter((item) => {
            switch (item.type) {
                case 0: return item.status < 2
                case 1: return ((!item.expired_at && item.status == 1) || (item.expired_at)) && item.status < 2
                default: return false
            }
        })
}, {
    immediate: true
})

onBeforeUnmount(() => {
    useTaskStore().openTask = false
})

const goLink = (link:string)=>{
    location.href = link
}
</script>

<style lang="less" scoped>
.taskMain {
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: center;


    .swipeStyle {
        height: 162px;
        width: 702px;
        background: #ffffff;
        border-radius: 16px 16px 16px 16px;
    }

    .taskList {
        width: 702px;
        height: 162px;
        opacity: 1;
        padding: 24px;
        box-sizing: border-box;
        display: flex;
        align-items: center;

        .taskTop {
            display: flex;
            align-items: center;

            img {
                width: 100px;
                height: 100px;
                margin-right: 16px;
            }

            .listTitle {
                display: flex;
                flex-direction: column;
                width: 350px;
                margin-right: 44px;

                .headline {
                    font-size: 32px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 500;
                    color: #4e5b7e;
                    display: -webkit-box; //对象作为弹性伸缩盒子模型显示
                    overflow: hidden; //溢出隐藏
                    -webkit-box-orient: vertical; //设置伸缩盒子对象的子元素的排列方式
                    -webkit-line-clamp: 1; //设置 块元素包含的文本行数
                }

                .subtitle {
                    font-size: 24px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    color: rgba(78, 91, 126, 0.7);
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
            }

            .listRight {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                .listBtn {
                    width: 144px;
                    height: 58px;
                    line-height: 58px;
                    text-align: center;
                    // background: linear-gradient(128deg, #50DD4C 0%, #3CBF48 100%);
                    border-radius: 30px 30px 30px 30px;
                    opacity: 1;
                    font-size: 24px;
                    font-family: PingFang TC, PingFang TC;
                    font-weight: 500;
                    color: #ffffff;
                }

                .listPace {
                    font-size: 18px;
                    font-family: PingFang TC, PingFang TC;
                    font-weight: 400;
                    color: #4e5b7e;
                }
            }

            .tofiniosh {
                color: #ffffff;
                background: linear-gradient(128deg, #50dd4c 0%, #3cbf48 100%);
            }

            .success {
                background: #acdeb0;
                color: #3c8e43;
            }

            .claim {
                color: #ffffff;
                background: linear-gradient(128deg, #ffdc7e 0%, #fbb629 100%);
            }
        }
    }

}
:deep(.van-swipe-item){
    img{
        width: 100%;
        height: 100%;
    }
}
</style>
