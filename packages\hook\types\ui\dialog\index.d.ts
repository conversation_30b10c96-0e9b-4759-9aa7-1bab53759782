import { App, Component } from 'vue';
import { UseDialog } from './type';
import { Dialog } from './dialog';
/**
 * 注册弹窗，显示弹窗之前，先要有注册，否则会读取不到弹窗配置
 * @param dialog
 */
declare function registerDialog(dialog: UseDialog.DialogRegisterType): void;
export declare function useDialog(localDialog?: Record<string, Component | {
    componentName?: string;
    component: Component;
    opts?: UseDialog.DialogOpt;
}>): {
    registerDialog: typeof registerDialog;
    app: App<Element>;
    state: {
        showList: {
            uuid: string;
            name: string;
            componentName: string;
            props: Record<string, any>;
            opts: {
                maskClose?: boolean | undefined;
                animName?: string | undefined;
                maskAnimName?: string | undefined;
                maskBgColor?: string | undefined;
                alignX?: UseDialog.AlignX | undefined;
                alignY?: UseDialog.AlignY | undefined;
            };
            isLocal: boolean;
            isShowing: boolean;
        }[];
    };
    rawState: {
        refs: Map<string, any>;
        dialogMapper: Map<string, UseDialog.DialogRegisterType>;
        dialogMap: Map<string, Dialog<any>>;
        singleDialogMap: Map<string, Dialog<any>>;
    };
    get: (name: string, opts?: UseDialog.DialogOpt) => Dialog<Record<string, any>>;
    getSingle: (name: string, opts?: UseDialog.DialogOpt) => Dialog<any>;
    getInstance: (uuid: string) => Dialog<any> | undefined;
    closeAll: () => void;
};
export {};
