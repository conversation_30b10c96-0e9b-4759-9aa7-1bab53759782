import { inApp, getSystem, logEventStatistics, upShareControl, closeWebView } from '@via/mylink-sdk'
import { imgs } from '@/assets/imgs'
import { useLang, useRouter, useEnvConfig } from 'hook'
import * as api from '@/store/modules/task/api'
const { router } = useRouter()
const { state } = useLang()
const system = getSystem()

function numberToThousands(num: string | number) {
    let str = num.toString().split('')
    if (str.length < 4) {
        return str.join('') + 'g'
    }
    if (str.length >= 4 && str.length < 6) {
        for (let i = str.length - 3; i > 0; i -= 3) {
            str[i] = ',' + str[i]
        }
        return str.join('') + 'g'
    }
    if (str.length >= 6) {
        let n: number | string = Number(num) / 1000
        n = n.toFixed(2)
        return n + 'kg'
    }
}

function debounce(fn: Function, wait: number = 300) {
    let timer: any = null
    return function (this: any, ...args: any[]) {
        let that: any = this
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
            fn.apply(that, args)
        }, wait)
    }
}

function throttle(fn: Function, delay: number = 300) {
    let nowTime = +new Date()
    return function (this: any, ...args: any[]) {
        let that: any = this
        let lastTime = +new Date()
        if (lastTime - nowTime <= delay) return
        else {
            nowTime = lastTime
            fn.apply(that, args)
        }
    }
}

/**
 *
 * @param obj
 * 获取计步数据
 */
function stepCountStatus(obj) {
    const name = system
    const isAndroid = name === 'android' //android终端
    const isiOS = name === 'ios' //ios终端
    obj = JSON.stringify(obj)
    if (!inApp.value) return
    if (isAndroid) {
        // 调用原生的分享功能
        window.HkAndroid && window.HkAndroid.stepCountStatusCallBack(obj)
    }

    if (isiOS) {
        // 调用原生的分享功能
        window.webkit &&
            window.webkit.messageHandlers &&
            window.webkit.messageHandlers.stepCountStatusCallBack &&
            window.webkit.messageHandlers.stepCountStatusCallBack.postMessage(obj)
    }
}

//左上角返回方法
function nativeBackClick() {
    // if (location.href.indexOf("friendId") !== -1) {
    //     router.replace({
    //         path: "/home",
    //         query: {
    //             hideNavigationBar:'true'
    //         }
    //     })
    // }
    const name = system
    const isAndroid = name === 'android' //android终端
    const isiOS = name === 'ios' //ios终端
    if (router.options.history.state.back) {
        router.go(-1)
    } else {
        if (isiOS) {
            window?.webkit?.messageHandlers.onNativeBackClick.postMessage(true)
        } else if (isAndroid) {
            closeWebView()
        }
    }
}

// 右上角分享按钮
function upShare(isOwn: boolean = false) {
    if (isOwn) return
    //配置分享
    const urlObj = new URL(location.href.split('#')[0])
    const shareUrl = urlObj.toString()
    // 埋点统计的事件名
    logEventStatistics('Mygarden_share_page')
    upShareControl({
        url: shareUrl,
        title: state.share.title,
        content: state.share.desc,
        img: imgs['share.png']
    })
}

function getTreeList() {
    return [
        {
            tree_id: 1000,
            code: 'lanhuaying',
            owned: false,
            completed: false,
            actived: true,
            req_vip: false,
            target_energy_total: 115,
            nft_actived: false,
            nft_actived_notice: false,
            nft_build_notice: false,
            nft_exists: false
        },
        {
            tree_id: 1001,
            code: 'diaozhonghua',
            owned: false,
            completed: false,
            actived: true,
            req_vip: false,
            target_energy_total: 72,
            nft_actived: false,
            nft_actived_notice: false,
            nft_build_notice: false,
            nft_exists: false
        },
        {
            tree_id: 1002,
            code: 'yangzijing',
            owned: false,
            completed: false,
            actived: true,
            req_vip: true,
            target_energy_total: 81,
            nft_actived: false,
            nft_actived_notice: false,
            nft_build_notice: false,
            nft_exists: false
        },
        {
            tree_id: 1003,
            code: 'jieguojueming',
            owned: false,
            completed: false,
            actived: true,
            req_vip: false,
            target_energy_total: 85,
            nft_actived: false,
            nft_actived_notice: false,
            nft_build_notice: false,
            nft_exists: false
        },
        {
            tree_id: 1004,
            code: 'xiuqiuhua',
            owned: false,
            completed: false,
            actived: true,
            req_vip: false,
            target_energy_total: 84,
            nft_actived: false,
            nft_actived_notice: false,
            nft_build_notice: false,
            nft_exists: false
        }
    ]
}

// getNetTime.js
//  获取网络时间
export function getNowTime() {
    return new Promise((resolve, reject) => {
        const xhr = new window.XMLHttpRequest()
        xhr.responseType = 'document'
        // 通过get的方式请求当前文件
        xhr.open('head', useEnvConfig().API_HOST + 'api/sdk/treetask/task/list')
        xhr.send(null)
        // 监听请求状态变化
        xhr.onreadystatechange = function () {
            let time
            if (xhr.readyState === 2) {
                // 获取响应头里的时间戳
                time = xhr.getResponseHeader('Date')
                time = time ? new Date(time) : new Date()
                resolve(time)
                reject(time)
            }
        }

        setTimeout(function () {
            resolve(new Date())
        }, 2000)
    })
}

/**
 * 动态获取图片地址
 * @param url 图片地址
 */
function getImageUrl(url) {
    const path = new URL(url, import.meta.url)
    return path.href
}

/**
 * 预加载图片地址
 * @param url 图片地址
 */
function preloadImg(url: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
        const img = new Image()
        img.src = url
        img.onload = () => {
            resolve(true)
        }
        img.onerror = () => {
            console.error(`${url} load error`)
            reject(false)
        }
    })
}

export {
    numberToThousands,
    debounce,
    throttle,
    stepCountStatus,
    upShare,
    nativeBackClick,
    getTreeList,
    getImageUrl,
    preloadImg
}
