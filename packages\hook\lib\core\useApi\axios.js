import config from './config';
import Axios from 'axios';
import { useUtils } from '../useUtils';
const { md5 } = useUtils();
const axios = Axios.create({
    baseURL: config.host,
    headers: config.defaultHeaders,
    timeout: config.timeout
});
const cacheData = {};
const cacheDataKeys = [];
const loadingList = {};
function getKVStr(obj) {
    return Object.entries(obj)
        .map(([k, v]) => `${k}=${JSON.stringify(v)}`)
        .sort()
        .join('&');
}
function getMD5(method, url, params, data, headers) {
    const str = [method, url, getKVStr(data), getKVStr(params), getKVStr(headers)].join('_');
    return md5(encodeURI(str), 16);
}
async function preDeal(md5Str, fn) {
    return fn
        .then((res) => {
        cacheData[md5Str] = res;
        cacheDataKeys.push(md5Str);
        // 只缓存20条数据
        if (cacheDataKeys.length > 20) {
            const key = cacheDataKeys.shift();
            if (key) {
                delete cacheData[key];
            }
        }
        if (loadingList[md5Str]) {
            loadingList[md5Str].fnList.forEach((resolve) => {
                resolve(res);
            });
            delete loadingList[md5Str];
        }
        return res.data;
    })
        .catch((e) => {
        // 接口挂了也需要清除
        delete loadingList[md5Str];
        throw e;
    });
}
let responseInterceptor;
async function realRequest(method, url, opts) {
    const options = Object.assign({ params: {}, headers: {}, data: {}, refresh: true }, opts);
    console.groupCollapsed('%c api请求发起', 'color: #ffffff;background-color: #8f4b2e;padding: 5px', url);
    console.log('请求方式：', method);
    console.log('请求链接：', url);
    console.log('请求参数：', options);
    console.groupEnd();
    const md5Str = getMD5(method, url, options.params, options.data, options.headers);
    let fn;
    if (loadingList[md5Str]) {
        // 已经有相同的接口发起了，等待结果返回
        console.log('有相同接口发起，整合成一个接口 ', `[${url}]`);
        fn = new Promise((resolve) => {
            loadingList[md5Str].fnList.push(resolve);
        });
    }
    else {
        loadingList[md5Str] = {
            fnList: []
        };
        if (!options.refresh && cacheData[md5Str]) {
            console.log('从缓存获取数据');
            fn = new Promise((resolve) => {
                resolve(cacheData[md5Str]);
            });
        }
        else {
            fn = axios({
                ...opts,
                url,
                method,
                params: options.params,
                data: options.data,
                headers: options.headers
            }).then(async (res) => {
                return res;
            });
        }
    }
    return preDeal(md5Str, fn);
}
async function request(method, url, opts) {
    return realRequest(method, url, opts).then(async (res) => {
        console.groupCollapsed('%c api请求结束', 'color: #ffffff;background-color: #5c7a29;padding: 5px', url);
        console.log('请求方式：', method);
        console.log('请求链接：', url);
        console.log('请求结果：', res);
        console.groupEnd();
        if (responseInterceptor) {
            res = await responseInterceptor(res);
        }
        if (+res.code !== 0) {
            throw res;
        }
        return res.data;
    });
}
async function get(url, opts) {
    return request('get', url, Object.assign({ params: {}, headers: {}, refresh: true }, opts));
}
async function post(url, opts) {
    return request('post', url, Object.assign({ data: {}, headers: {}, refresh: true }, opts));
}
function registerResponseInterceptor(fn) {
    responseInterceptor = fn;
}
export { request, post, get, registerResponseInterceptor };
